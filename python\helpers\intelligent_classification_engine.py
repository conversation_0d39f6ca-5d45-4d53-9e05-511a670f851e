"""
Intelligent Classification Engine for advanced data analysis and routing.
"""

import asyncio
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import asdict
import dataclasses # Added import for dataclasses.replace
from datetime import datetime # Added datetime import
from enum import Enum

from .classification_engine import (
    ClassificationEngine,
    ClassificationRequest,
    ClassificationResult,
    ClassificationMetrics,
    ClassificationStatus,
    ClassificationPriority,
    ClassificationStrategy, # Assuming this is in classification_config or classification_engine
    generate_id
)
from .classification_config import ClassificationConfig # Main config
from .entity_relationship_analyzer import EntityRelationshipAnalyzer
from .user_preferences import UserPreferenceManager, PreferenceType, LearningSignal
from .behavioral_analyzer import BehavioralPatternAnalyzer
# Add other specific data classes if directly used and not via manager/analyzer methods


class IntelligentClassificationEngine(ClassificationEngine):
    """
    An advanced classification engine that uses entity extraction, semantic analysis,
    and relationship analysis to determine data classification and storage.
    """

    def __init__(self, config: ClassificationConfig):
        """
        Initializes the IntelligentClassificationEngine.

        Args:
            config (ClassificationConfig): The configuration object for the engine.
        """
        super().__init__(config) # Initializes self.config, self.performance_tracker, etc.
        self.entity_analyzer = EntityRelationshipAnalyzer(config=asdict(config.thresholds)) # Pass relevant part of config
        self.domain_keywords: Dict[str, List[str]] = {}
        # Example: self.cache = LRUCache(maxsize=config.cache.l1_memory_size) if config.cache.enabled else None
        self.cache: Dict[str, ClassificationResult] = {} # Simple dict cache for now
        self.preference_managers: Dict[str, UserPreferenceManager] = {}
        self.behavioral_analyzers: Dict[str, BehavioralPatternAnalyzer] = {}

    async def _initialize_engine(self):
        """
        Engine-specific initialization. Loads domain keywords, models, etc.
        """
        print("IntelligentClassificationEngine: Initializing...")
        await self._initialize_domain_keywords()
        # In a real scenario, load NLP models (e.g., for NER, semantic similarity) here.
        # For example:
        # self.ner_model = spacy.load("en_core_web_lg")
        # self.semantic_model = SentenceTransformer("all-mpnet-base-v2")
        print("IntelligentClassificationEngine: Domain keywords initialized.")
        # Initialize entity_analyzer if it has an async init method
        # await self.entity_analyzer.initialize() # If applicable
        self.is_initialized = True
        print("IntelligentClassificationEngine: Initialization complete.")

    async def _initialize_domain_keywords(self):
        """
        Initializes domain-specific keywords.
        In a real system, these might come from a database, config files, or an ontology.
        """
        # Placeholder: Load keywords for different domains
        self.domain_keywords = {
            "finance": ["stock", "investment", "ipo", "earnings", "market", "sec", "asset"],
            "healthcare": ["patient", "medical", "fda", "clinical trial", "hipaa", "diagnosis", "pharma"],
            "technology": ["software", "hardware", "ai", "cloud", "cybersecurity", "api", "algorithm"],
            "legal": ["contract", "litigation", "court", "attorney", "compliance", "gdpr", "subpoena"],
            "internal_dev": ["jira", "confluence", "github", "pull request", "sprint", "bug", "feature"],
        }
        # Simulate async loading if keywords were from an external source
        await asyncio.sleep(0.01)

    def _generate_cache_key(self, content: str, metadata: Dict[str, Any]) -> str:
        """
        Generates a cache key for a classification request.
        """
        hasher = hashlib.sha256()
        hasher.update(content.encode('utf-8'))
        # Include relevant metadata fields in the hash to ensure uniqueness
        # Sorting metadata items ensures consistent key order
        for key, value in sorted(metadata.items()):
            hasher.update(str(key).encode('utf-8'))
            hasher.update(str(value).encode('utf-8'))
        return hasher.hexdigest()

    async def classify_content(
        self,
        content: str,
        metadata: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None # Context for more advanced reasoning
    ) -> ClassificationResult:
        """
        Classifies the given content based on various analytical measures.
        """
        if not self.is_initialized:
            await self.initialize()

        operation_id = self.performance_tracker.start_timing()
        cache_key = ""
        if self.config.cache.enabled:
            cache_key = self._generate_cache_key(content, metadata)
            if cache_key in self.cache:
                cached_result = self.cache[cache_key]
                cached_result.cache_hit = True
                cached_result.processing_time_ms = self.performance_tracker.end_timing(operation_id) or 0.0
                self.performance_tracker.record_classification(cached_result)
                return cached_result

        # 1. Entity Extraction
        entities = await self.extract_entities(content, metadata)

        # 2. Relationship Analysis (using the stubbed analyzer)
        # relationships = await self.entity_analyzer.analyze_entity_relationships(entities, content)
        # For now, relationships are not used in metrics calculation directly in this stub.

        # 3. Calculate various metrics
        classification_metrics = await self._calculate_classification_metrics(content, entities, metadata)

        # 4. Apply Decision Matrix (Stubbed)
        strategy, confidence, reasoning = self._apply_decision_matrix(classification_metrics)

        # 5. Determine Storage Location (Stubbed)
        namespace, domain = self._determine_storage_location(strategy, classification_metrics, metadata)

        processing_time_ms = self.performance_tracker.end_timing(operation_id) or 0.0

        result = ClassificationResult(
            classification_id=generate_id("cls_intel"),
            strategy=strategy,
            confidence=confidence,
            metrics=classification_metrics,
            reasoning=reasoning,
            processing_time_ms=processing_time_ms,
            namespace=namespace,
            domain=domain,
            timestamp=datetime.now(),
            status=ClassificationStatus.COMPLETED, # Default to COMPLETED for stub
            cache_hit=False
        )

        if self.config.cache.enabled and cache_key:
             # Basic cache size management (very rudimentary)
            if len(self.cache) >= self.config.cache.l1_memory_size : # Using l1_memory_size as rough item limit
                self.cache.popitem() # Remove oldest item
            self.cache[cache_key] = result

        self.performance_tracker.record_classification(result)
        return result

    async def extract_entities(self, content: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extracts basic, domain-specific, and privacy-related entities.
        """
        # In a real system, these might run in parallel or use a unified NER model.
        basic_entities = await self._extract_basic_entities(content)
        domain_entities = await self._extract_domain_entities(content, metadata.get("domain_hint"))
        privacy_entities = await self._extract_privacy_entities(content)

        # Combine and de-duplicate entities (simple approach)
        all_entities_dict: Dict[str, Dict[str, Any]] = {}
        for entity_list in [basic_entities, domain_entities, privacy_entities]:
            for entity in entity_list:
                # Assuming entity has 'text' and 'type'
                key = (entity.get("text","").lower(), entity.get("type",""))
                if key[0] and key[1] and key not in all_entities_dict:
                     all_entities_dict[key] = entity

        all_entities = list(all_entities_dict.values())

        # Calculate confidence for each entity
        for entity in all_entities:
            entity["confidence"] = await self._calculate_entity_confidence(entity, content)

        return all_entities

    async def _extract_basic_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extracts common entities (PERSON, ORG, LOC, DATE, etc.). Placeholder."""
        # Example using regex, a real version would use an NER model (e.g., spaCy, Stanza)
        # This is a very simplified example.
        entities = []
        if "John Doe" in content: entities.append({"text": "John Doe", "type": "PERSON", "start": content.find("John Doe"), "end": content.find("John Doe")+len("John Doe")})
        if "Acme Corp" in content: entities.append({"text": "Acme Corp", "type": "ORG", "start": content.find("Acme Corp"), "end": content.find("Acme Corp")+len("Acme Corp")})
        await asyncio.sleep(0.01) # Simulate async work
        return entities

    async def _extract_domain_entities(self, content: str, domain_hint: Optional[str]) -> List[Dict[str, Any]]:
        """Extracts entities relevant to specific domains. Placeholder."""
        entities = []
        # Use domain_hint or try to infer domain
        # For example, if finance domain:
        # if "stock" in content.lower(): entities.append({"text": "stock", "type": "FINANCIAL_INSTRUMENT"})
        await asyncio.sleep(0.01)
        return entities

    async def _extract_privacy_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extracts PII and other privacy-sensitive information. Placeholder."""
        entities = []
        # Example:
        # if re.search(r"\b\d{3}-\d{2}-\d{4}\b", content): # SSN-like pattern
        #    entities.append({"text": "SSN_pattern", "type": "PII_SSN"})
        await asyncio.sleep(0.01)
        return entities

    async def _calculate_entity_confidence(self, entity: Dict[str, Any], content: str) -> float:
        """Calculates confidence for a single extracted entity. Placeholder."""
        # Factors: model confidence (if from NER), context, ambiguity.
        await asyncio.sleep(0.005)
        return entity.get("model_confidence", 0.85) # Default high confidence for stub

    async def _calculate_classification_metrics(
        self, content: str, entities: List[Dict[str, Any]], metadata: Dict[str, Any]
    ) -> ClassificationMetrics:
        """
        Calculates a comprehensive set of metrics for classification.
        """
        # Most of these will be stubs or simple calculations for now.
        semantic_overlap = await self.calculate_semantic_overlap(content, metadata.get("related_docs", []))
        entity_confidence_agg = await self._calculate_entity_confidence_aggregate(entities)
        domain_specificity = await self._calculate_domain_specificity(content, entities)
        privacy_flags, privacy_score = await self._detect_privacy_flags(entities) # Returns tuple
        relationship_density = await self._calculate_relationship_density(content, entities) # Placeholder
        interconnectedness = await self._calculate_interconnectedness(entities, metadata) # Placeholder
        temporal_relevance = await self._calculate_temporal_relevance(metadata) # Placeholder

        return ClassificationMetrics(
            semantic_overlap=semantic_overlap,
            entity_confidence=entity_confidence_agg,
            domain_specificity=domain_specificity,
            relationship_density=relationship_density,
            privacy_score=privacy_score,
            privacy_flags=privacy_flags,
            interconnectedness=interconnectedness,
            temporal_relevance=temporal_relevance,
            related_entities_count=len(entities),
            content_length=len(content),
            entity_types=list(set(e.get("type", "UNKNOWN") for e in entities))
        )

    async def _calculate_entity_confidence_aggregate(self, entities: List[Dict[str, Any]]) -> float:
        """Aggregates confidence scores from all entities. Placeholder."""
        if not entities: return 0.0
        total_confidence = sum(e.get("confidence", 0.0) for e in entities)
        avg_confidence = total_confidence / len(entities)
        await asyncio.sleep(0.005)
        return avg_confidence

    async def calculate_semantic_overlap(self, content: str, related_docs: List[str]) -> float:
        """Calculates semantic overlap with related documents. Placeholder."""
        # Real impl: Use sentence transformers or other embedding techniques.
        # For stub:
        if not related_docs: return 0.0
        await asyncio.sleep(0.02) # Simulate embedding & comparison
        return 0.65 # Stubbed value

    async def _calculate_domain_specificity(self, content: str, entities: List[Dict[str, Any]]) -> float:
        """Determines how specific the content is to a particular domain. Placeholder."""
        score = 0.0
        content_lower = content.lower()
        for domain, keywords in self.domain_keywords.items():
            for keyword in keywords:
                if keyword in content_lower:
                    score += 0.1 # Arbitrary increment
        # Normalize or cap score, e.g. min(1.0, score)
        await asyncio.sleep(0.01)
        return min(1.0, score)

    async def _detect_privacy_flags(self, entities: List[Dict[str, Any]]) -> Tuple[bool, int]:
        """Detects privacy flags and calculates a privacy score. Placeholder."""
        # privacy_flags (bool): True if PII or sensitive data indicators are found.
        # privacy_score (int): A score from 0 (no PII) to N (high PII/sensitivity).
        has_pii = any(e.get("type", "").startswith("PII_") for e in entities)
        score = 0
        if has_pii: score = 3 # Example score
        # Could also check for keywords like "confidential", "private", etc.
        await asyncio.sleep(0.01)
        return has_pii, score

    async def _calculate_relationship_density(self, content: str, entities: List[Dict[str, Any]]) -> float:
        """Calculates density of relationships between entities. Placeholder."""
        # Real impl: Use graph metrics if relationships were extracted.
        # Stub: based on number of entities
        if len(entities) > 5: return 0.7
        if len(entities) > 2: return 0.4
        await asyncio.sleep(0.005)
        return 0.2

    async def _get_user_preference_manager(self, user_id: str) -> UserPreferenceManager:
       if user_id not in self.preference_managers:
           manager = UserPreferenceManager(user_id)
           if hasattr(manager, 'load_preferences') and asyncio.iscoroutinefunction(manager.load_preferences):
               await manager.load_preferences()
           self.preference_managers[user_id] = manager
       return self.preference_managers[user_id]

    async def _get_behavioral_analyzer(self, user_id: str) -> BehavioralPatternAnalyzer:
       if user_id not in self.behavioral_analyzers:
           analyzer = BehavioralPatternAnalyzer(user_id)
           self.behavioral_analyzers[user_id] = analyzer
       return self.behavioral_analyzers[user_id]

    async def _get_domain_weights(self, user_preferences: UserPreferenceManager) -> Dict[str, float]:
       domain_weights = {}
       if hasattr(user_preferences, 'preferences') and isinstance(user_preferences.preferences, dict):
           for key, pref_item_maybe in user_preferences.preferences.items():
               pref_item_type = None
               pref_item_value = None

               if hasattr(pref_item_maybe, 'preference_type') and hasattr(pref_item_maybe, 'value'):
                   pref_item_type = pref_item_maybe.preference_type
                   pref_item_value = pref_item_maybe.value
               elif isinstance(pref_item_maybe, dict) and 'preference_type' in pref_item_maybe and 'value' in pref_item_maybe:
                   pref_item_type = pref_item_maybe['preference_type']
                   pref_item_value = pref_item_maybe['value']

               current_pref_type_enum = None
               if isinstance(pref_item_type, str):
                   try:
                       current_pref_type_enum = PreferenceType[pref_item_type.upper()]
                   except KeyError:
                       found = False
                       for pt_enum in PreferenceType:
                           if pt_enum.value == pref_item_type:
                               current_pref_type_enum = pt_enum
                               found = True
                               break
                       if not found: continue
               elif isinstance(pref_item_type, PreferenceType): # Already an enum
                    current_pref_type_enum = pref_item_type


               if current_pref_type_enum == PreferenceType.DOMAIN_WEIGHT and pref_item_value is not None:
                   domain_name = key.replace("domain_weight_", "")
                   try:
                       domain_weights[domain_name] = float(pref_item_value)
                   except ValueError:
                       print(f"WARN: Could not convert domain weight value '{pref_item_value}' to float for key '{key}'.")
       return domain_weights

    async def _apply_user_preferences(
       self,
       current_metrics: ClassificationMetrics,
       user_preferences: UserPreferenceManager,
       content: str,
       entities: List[Dict[str, Any]]
    ) -> ClassificationMetrics:
        adjusted_metrics = dataclasses.replace(current_metrics) if hasattr(dataclasses, 'replace') else current_metrics # Python 3.7+ for replace

        domain_weights = await self._get_domain_weights(user_preferences)
        if domain_weights:
            adjusted_metrics.domain_specificity = await self._adjust_domain_specificity(
                content, entities, domain_weights
            )

        privacy_sensitivity_pref = await user_preferences.get_preference("privacy_sensitivity_level", "medium")
        if privacy_sensitivity_pref is not None: # Check if preference was actually found or default used
            adjusted_metrics.privacy_score = self._adjust_privacy_scoring(
                current_metrics.privacy_score,
                privacy_sensitivity_pref,
                current_metrics.privacy_flags
            )

        semantic_boost_pref = await user_preferences.get_preference("semantic_boost_factor", 1.0)
        if semantic_boost_pref is not None:
           try:
               boost_factor = float(semantic_boost_pref)
               # Ensure _adjust_semantic_overlap exists and is callable
               if hasattr(self, '_adjust_semantic_overlap') and callable(self._adjust_semantic_overlap):
                   adjusted_metrics.semantic_overlap = self._adjust_semantic_overlap(
                       current_metrics.semantic_overlap, boost_factor
                   )
               else:
                    print(f"WARN: _adjust_semantic_overlap method not implemented on {self.__class__.__name__}.")
           except ValueError:
               print(f"WARN: Could not convert semantic_boost_factor '{semantic_boost_pref}' to float.")
        return adjusted_metrics

    async def _calculate_domain_specificity_with_weights(self, content: str, entities: List[Dict[str, Any]], domain_weights: Dict[str, float]) -> float:
       content_lower = content.lower()
       weighted_score_sum = 0.0
       total_weight_sum = 0.0

       if not domain_weights: # Fallback to original if no weights
           return await self._calculate_domain_specificity(content, entities)

       for domain, keywords in self.domain_keywords.items():
           weight = domain_weights.get(domain, 0.0)
           if weight > 0:
               total_weight_sum += weight
               domain_keyword_hits = 0
               for keyword in keywords:
                   if keyword in content_lower:
                       domain_keyword_hits += 1
               normalized_hits = domain_keyword_hits / len(keywords) if keywords else 0.0
               weighted_score_sum += normalized_hits * weight

       final_score = weighted_score_sum / total_weight_sum if total_weight_sum > 0 else 0.0
       final_score = max(0.0, min(1.0, final_score))
       await asyncio.sleep(0.01)
       return final_score

    async def _adjust_domain_specificity(self, content: str, entities: List[Dict[str, Any]], domain_weights: Dict[str, float]) -> float:
       return await self._calculate_domain_specificity_with_weights(content, entities, domain_weights)

    def _adjust_privacy_scoring(self, current_privacy_score: int, privacy_preference_value: Any, privacy_flags: bool) -> int:
       if not privacy_flags:
           return current_privacy_score

       adjusted_score = current_privacy_score
       pref_val_str = str(privacy_preference_value).lower()

       if pref_val_str == "high":
           adjusted_score = min(current_privacy_score + 2, 5)
       elif pref_val_str == "medium":
           adjusted_score = min(current_privacy_score + 1, 5)
       elif pref_val_str == "low":
           adjusted_score = max(current_privacy_score - 1, 1 if current_privacy_score > 0 else 0)
       return max(0, min(5, int(adjusted_score)))

    def _adjust_semantic_overlap(self, current_overlap: float, boost_factor: float) -> float:
       adjusted_overlap = current_overlap * boost_factor
       return max(0.0, min(1.0, adjusted_overlap))

    async def _record_classification_interaction(
       self,
       user_id: str,
       classification_result: ClassificationResult,
       content_info: Dict[str, Any]
    ):
       try:
           analyzer = await self._get_behavioral_analyzer(user_id)
           if hasattr(analyzer, 'record_interaction') and callable(analyzer.record_interaction):
               interaction_data = {
                   "classification_id": classification_result.classification_id,
                   "strategy_used": classification_result.strategy.value if isinstance(classification_result.strategy, Enum) else str(classification_result.strategy), # Ensure Enum is imported if ClassificationStrategy is an Enum
                   "confidence": classification_result.confidence,
                   "content_details": content_info,
                   "timestamp": datetime.now().isoformat(),
                   "namespace": classification_result.namespace,
                   "domain": classification_result.domain,
                   "metrics": classification_result.metrics.__dict__ if hasattr(classification_result.metrics, '__dict__') else {} # Ensure ClassificationMetrics is serializable
               }

               if asyncio.iscoroutinefunction(analyzer.record_interaction):
                    await analyzer.record_interaction(
                        interaction_type="classification_performed",
                        context=interaction_data,
                    )
               else:
                    analyzer.record_interaction(
                        interaction_type="classification_performed",
                        context=interaction_data,
                    )
           else:
               print(f"WARN: BehavioralPatternAnalyzer for {user_id} does not have 'record_interaction' method or it's not callable.")
       except Exception as e:
           # Consider using logging module for errors
           print(f"ERROR: Failed to record classification interaction for {user_id}: {e}")

    async def _calculate_interconnectedness(self, entities: List[Dict[str, Any]], metadata: Dict[str, Any]) -> float:
        """Measures how connected the content is to existing knowledge. Placeholder."""
        # Real impl: Check against knowledge graph, existing schemas.
        # Stub:
        # await self._load_existing_schemas() # If schemas were dynamic
        # existing_schemas = self._get_existing_schemas()
        # if any(e.get("type") in existing_schemas for e in entities): return 0.75
        await asyncio.sleep(0.01)
        return 0.5 # Stubbed value

    async def _calculate_temporal_relevance(self, metadata: Dict[str, Any]) -> float:
        """Assesses the temporal relevance of the content. Placeholder."""
        # Real impl: Compare content timestamp with current time, decay functions.
        # timestamp_str = metadata.get("timestamp") # Assuming ISO format string
        # if timestamp_str:
        #     try:
        #         content_dt = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
        #         age_days = (datetime.now(timezone.utc) - content_dt).days
        #         return max(0, 1.0 - age_days / 365) # Example: relevance drops over a year
        #     except ValueError:
        #         return 0.5 # Default if timestamp is bad
        await asyncio.sleep(0.005)
        return 0.9 # Stubbed: fairly relevant

    async def _load_existing_schemas(self):
        """Stub for loading existing data schemas or ontologies."""
        print("IntelligentClassificationEngine: _load_existing_schemas (stub).")
        # In a real system, this might load from a database, file, or schema registry.
        await asyncio.sleep(0.01) # Simulate async loading
        pass

    def _get_existing_schemas(self) -> List[str]:
        """Stub for getting currently known schemas/entity types."""
        print("IntelligentClassificationEngine: _get_existing_schemas (stub).")
        # Example: return ["PERSON", "ORGANIZATION", "PRODUCT", "FINANCIAL_REPORT"]
        return [] # Stub

    def _apply_decision_matrix(self, metrics: ClassificationMetrics) -> Tuple[ClassificationStrategy, float, str]:
        """
        Applies a decision matrix (or model) to metrics to decide classification strategy.
        Stub implementation.
        """
        # Args: metrics (ClassificationMetrics)
        # Returns: Tuple (ClassificationStrategy, confidence_score, reasoning_string)

        # Example logic (very simplified for stub):
        if metrics.privacy_flags and metrics.privacy_score >= 3:
            return (ClassificationStrategy.ISOLATED_NAMESPACE, 0.9,
                    f"High privacy risk (score: {metrics.privacy_score}), isolating content.")

        if metrics.domain_specificity > 0.6 and metrics.entity_confidence > 0.7:
            # This might map to a specific domain or a custom strategy if domains are well-defined
            return (ClassificationStrategy.CUSTOM_DOMAIN,
                    (metrics.domain_specificity + metrics.entity_confidence) / 2,
                    "High domain specificity and entity confidence.")

        if metrics.semantic_overlap > 0.5 or metrics.interconnectedness > 0.5:
             return (ClassificationStrategy.SHARED_ONTOLOGY,
                    (metrics.semantic_overlap + metrics.interconnectedness) / 2,
                    "Good semantic overlap or interconnectedness suggesting shared context.")

        # Default fallback
        return (ClassificationStrategy.ISOLATED_NAMESPACE, 0.5,
                "Default fallback strategy based on metrics evaluation (stubbed decision matrix).")

    def _determine_storage_location(
        self,
        strategy: ClassificationStrategy,
        metrics: ClassificationMetrics,
        metadata: Dict[str, Any]
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Determines the namespace and domain for storage based on classification.
        Stub implementation.
        """
        # Args: strategy (ClassificationStrategy), metrics (ClassificationMetrics), metadata (Dict)
        # Returns: Tuple (namespace: Optional[str], domain: Optional[str])

        # Example logic (simplified for stub):
        user_id = metadata.get("user_id")

        if strategy == ClassificationStrategy.ISOLATED_NAMESPACE:
            # For isolated, namespace might be user-specific or content-specific hash
            return (f"user_{user_id}_isolated" if user_id else "default_isolated", "private_data")

        elif strategy == ClassificationStrategy.CUSTOM_DOMAIN:
            # Try to infer domain from metadata or metrics
            # This part would need more sophisticated domain detection based on keywords/entities
            inferred_domain = metadata.get("domain_hint")
            if not inferred_domain:
                # Basic inference from domain_keywords used in specificity calc (crude)
                best_domain = "general"
                max_kw_hits = 0
                content_lower = metadata.get("content_preview","").lower() # Assuming content was passed or preview available
                for domain_key, keywords in self.domain_keywords.items():
                    hits = sum(1 for kw in keywords if kw in content_lower)
                    if hits > max_kw_hits:
                        max_kw_hits = hits
                        best_domain = domain_key
                inferred_domain = best_domain

            return (f"domain_specific_ns", inferred_domain)

        elif strategy == ClassificationStrategy.SHARED_ONTOLOGY:
            return ("shared_knowledge_base", "common_entities")

        # Default / Fallback
        return (None, None) # Let consuming system decide or use a global default

    async def classify_content_with_preferences(
       self,
       user_id: str,
       content: str,
       metadata: Dict[str, Any],
       context: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
       if not self.is_initialized:
           await self.initialize()

       operation_id = self.performance_tracker.start_timing()

       preference_aware_metadata_for_cache = {**metadata, "_user_id_for_cache": user_id}
       cache_key = ""
       if self.config.cache.enabled:
           cache_key = self._generate_cache_key(content, preference_aware_metadata_for_cache)
           if cache_key in self.cache:
               cached_result = self.cache[cache_key]
               cached_result.cache_hit = True
               cached_result.processing_time_ms = self.performance_tracker.end_timing(operation_id) or 0.0
               self.performance_tracker.record_classification(cached_result)
               return cached_result

       user_preference_manager = await self._get_user_preference_manager(user_id)
       entities = await self.extract_entities(content, metadata)

       classification_metrics_base = await self._calculate_classification_metrics(content, entities, metadata)
       classification_metrics_adjusted = await self._apply_user_preferences(
           classification_metrics_base, user_preference_manager, content, entities
       )

       strategy, confidence, reasoning = self._apply_decision_matrix(classification_metrics_adjusted)

       internal_metadata_for_storage = {**metadata, "_internal_content_lower_for_domain_inference": content.lower(), "user_id": user_id}
       namespace, domain = self._determine_storage_location(strategy, classification_metrics_adjusted, internal_metadata_for_storage)

       processing_time_ms = self.performance_tracker.end_timing(operation_id) or 0.0

       result = ClassificationResult(
           classification_id=generate_id("cls_intel_pref"),
           strategy=strategy,
           confidence=confidence,
           metrics=classification_metrics_adjusted,
           reasoning=reasoning,
           processing_time_ms=processing_time_ms,
           namespace=namespace,
           domain=domain,
           timestamp=datetime.now(),
           status=ClassificationStatus.COMPLETED,
           cache_hit=False,
       )

       if self.config.cache.enabled and cache_key:
           if len(self.cache) >= self.config.cache.l1_memory_size :
               # Attempt to remove the oldest item; basic dicts > 3.7 remember insertion order
               # For <3.7 or more robust LRU, an OrderedDict or custom LRU class would be better.
               try:
                   self.cache.pop(next(iter(self.cache)))
               except StopIteration: # Cache was empty
                   pass
           self.cache[cache_key] = result

       self.performance_tracker.record_classification(result)

       content_info_for_analyzer = {
           'length': len(content),
           'type': metadata.get('content_type', 'unknown'),
           'source': metadata.get('source', 'unknown')
       }
       await self._record_classification_interaction(user_id, result, content_info_for_analyzer)

       return result

    async def _shutdown_engine(self):
        """Engine-specific shutdown logic."""
        print("IntelligentClassificationEngine: Shutting down.")
        # Release resources, close connections, etc.
        self.is_initialized = False
        print("IntelligentClassificationEngine: Shutdown complete.")


if __name__ == "__main__":
    # Example usage and basic test for IntelligentClassificationEngine

    async def run_engine_tests():
        print("--- Initializing Test Intelligent Classification Engine ---")
        # Use a default config for testing
        test_config = ClassificationConfig()
        # Optionally override specific settings for test if needed from default .env
        # e.g., test_config.cache.enabled = False

        engine = IntelligentClassificationEngine(config=test_config)

        # Test initialization
        await engine.initialize()
        assert engine.is_initialized, "Engine failed to initialize"
        print("\nEngine initialized successfully.")

        # Test classification of simple content
        print("\n--- Classifying Test Content ---")
        test_content = "This is a test document about financial stock market analysis and patient healthcare records."
        test_metadata = {"type": "test_document", "source": "pytest", "user_id": "test_user_123"}

        classification_result = await engine.classify_content(test_content, test_metadata)

        print(f"\nClassification ID: {classification_result.classification_id}")
        print(f"Strategy: {classification_result.strategy.value}")
        print(f"Confidence: {classification_result.confidence:.2f}")
        print(f"Reasoning: {classification_result.reasoning}")
        print(f"Namespace: {classification_result.namespace}, Domain: {classification_result.domain}")
        print(f"Processing Time: {classification_result.processing_time_ms:.2f}ms")
        print(f"Cache Hit: {classification_result.cache_hit}")

        print("\nMetrics:")
        for key, value in asdict(classification_result.metrics).items():
            print(f"  {key}: {value}")

        # Verify stubbed decision matrix was called (indirectly, by checking strategy)
        # Based on current stub, if privacy flags are not high, it might go to other conditions or default.
        # The default stub returns ISOLATED_NAMESPACE if other conditions aren't strongly met.
        # Let's check if reasoning implies the stub was hit.
        assert "stubbed decision matrix" in classification_result.reasoning.lower() or \
               "high privacy risk" in classification_result.reasoning.lower() or \
               "domain specificity" in classification_result.reasoning.lower() or \
               "semantic overlap" in classification_result.reasoning.lower(), \
               "Reasoning does not indicate stubbed decision matrix was used as expected."

        # Test caching (second call should be faster and hit cache if enabled)
        if test_config.cache.enabled:
            print("\n--- Testing Cache (Second Classification of Same Content) ---")
            classification_result_cached = await engine.classify_content(test_content, test_metadata)
            assert classification_result_cached.cache_hit is True, "Cache hit expected for second call"
            print(f"Cached Result - Strategy: {classification_result_cached.strategy.value}, Time: {classification_result_cached.processing_time_ms:.2f}ms, Cache Hit: {classification_result_cached.cache_hit}")
            # Assert that IDs are same for cached results (if cache returns original object)
            # or that key fields are the same. For a simple dict cache, object might be same.
            assert classification_result.classification_id == classification_result_cached.classification_id, "Cache returned different result object on ID"

        print("\n--- Testing Engine Status ---")
        status = await engine.get_engine_status()
        # print(json.dumps(status, indent=2)) # Requires json import
        assert status['is_initialized'] is True
        assert status['performance_summary']['total_operations'] > 0


        # Test classification with preferences
        print("\n--- Classifying Test Content WITH PREFERENCES ---")
        user_id_for_prefs = "pref_user_test"
        # Simulate setting some preferences for this user via the manager
        # (In a real scenario, API would do this, here we manipulate manager directly for test)
        pref_manager = await engine._get_user_preference_manager(user_id_for_prefs)

        # Manually add placeholder PreferenceItem-like structures if UserPreferenceManager expects them
        # This depends on the actual or placeholder implementation of UserPreferenceManager
        # Assuming placeholder UserPreferenceManager.preferences is a simple dict for this test
        if isinstance(pref_manager.preferences, dict): # Check if it's the placeholder's dict
            # Example of setting a domain weight preference
            # The key "domain_weight_technology" and structure {"value": ..., "preference_type": ...}
            # must align with what _get_domain_weights expects.
            pref_manager.preferences["domain_weight_technology"] = {
                "value": 0.9, # Boost technology
                "preference_type": PreferenceType.DOMAIN_WEIGHT
            }
            pref_manager.preferences["domain_weight_finance"] = {
                "value": 0.2, # Reduce finance
                "preference_type": PreferenceType.DOMAIN_WEIGHT
            }
            # Example of setting privacy sensitivity
            pref_manager.preferences["privacy_sensitivity_level"] = {
                "value": "high",
                "preference_type": PreferenceType.PRIVACY_SENSITIVITY
            }

        print(f"Simulated preferences for {user_id_for_prefs}: {pref_manager.preferences}")


        classification_result_prefs = await engine.classify_content_with_preferences(
            user_id_for_prefs,
            test_content, # Using same content
            test_metadata # Using same metadata
        )

        print(f"\nClassification ID (with Prefs): {classification_result_prefs.classification_id}")
        print(f"Strategy (with Prefs): {classification_result_prefs.strategy.value}")
        print(f"Confidence (with Prefs): {classification_result_prefs.confidence:.2f}")
        print(f"Reasoning (with Prefs): {classification_result_prefs.reasoning}")
        print(f"Namespace (with Prefs): {classification_result_prefs.namespace}, Domain: {classification_result_prefs.domain}")
        print("\nMetrics (with Prefs):")
        for key, value in asdict(classification_result_prefs.metrics).items():
            print(f"  {key}: {value}")

        # Add assertions here to check if preferences influenced the outcome
        # For example, domain_specificity or privacy_score might change, leading to different strategy/domain.
        # This depends heavily on the stubbed logic in _apply_user_preferences and decision matrix.
        # Example: assert classification_result_prefs.metrics.domain_specificity != classification_result.metrics.domain_specificity

        # Test shutdown
        await engine.shutdown()
        assert not engine.is_initialized, "Engine failed to shutdown properly"
        print("\nEngine shutdown successfully.")

    asyncio.run(run_engine_tests())
