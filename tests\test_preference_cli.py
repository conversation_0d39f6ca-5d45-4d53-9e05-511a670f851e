import asyncio
import unittest
from unittest.mock import patch, AsyncMock, MagicMock
import io
import sys
import json
from datetime import datetime
from typing import Any, List # Added Any, List

# Attempt to import the CLI and the preference_api it uses
try:
    from python.helpers.preference_cli import PreferenceCLI
    # The CLI module imports 'preference_api' from '.preference_api'
    # So, for testing, we need to patch 'python.helpers.preference_cli.preference_api'
except ImportError as e:
    print(f"test_preference_cli.py: Error importing modules: {e}. Tests may fail or not run.")
    # Define minimal placeholders if imports fail
    class PreferenceCLI:
        def __init__(self): self.parser = MagicMock()
        async def run(self, args): pass


# Helper to run async tests
def async_test(coro):
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

class TestPreferenceCLI(unittest.TestCase):

    def setUp(self):
        self.cli = PreferenceCLI()
        # It's crucial to patch the preference_api where it's looked up by the CLI module
        self.mock_api_patcher = patch('python.helpers.preference_cli.preference_api', new_callable=AsyncMock)
        self.mock_api = self.mock_api_patcher.start()

    def tearDown(self):
        self.mock_api_patcher.stop()
        patch.stopall() # Clean up any other patches

    async def run_cli_command(self, command_args: List[str], expected_api_method: str = None, api_return_value: Any = None, expected_partial_output: str = None):
        """Helper to run CLI commands and check output."""
        if expected_api_method:
            api_method_mock = getattr(self.mock_api, expected_api_method)
            api_method_mock.return_value = api_return_value

        # Capture stdout
        captured_output = io.StringIO()
        sys.stdout = captured_output

        await self.cli.run(command_args)

        sys.stdout = sys.__stdout__  # Reset stdout
        output = captured_output.getvalue()

        if expected_api_method:
            self.assertTrue(api_method_mock.called, f"Expected API method {expected_api_method} to be called.")

        if expected_partial_output:
            self.assertIn(expected_partial_output, output, f"Output did not contain '{expected_partial_output}'")

        return output # Return output for more specific assertions if needed

    # Test 'get' command
    @async_test
    async def test_cli_get_preferences_table(self):
        user_id = "user_get_table"
        api_response = {
            "theme": {"value": "dark", "type": "GENERAL", "confidence": 1.0, "last_updated": datetime.now().isoformat(), "user_explicit": False, "learning_history_count": 0},
            "results": {"value": 10, "type": "SEARCH", "confidence": 0.9, "last_updated": datetime.now().isoformat(), "user_explicit": True, "learning_history_count": 2}
        }
        await self.run_cli_command(
            ['get', user_id],
            expected_api_method='get_user_preferences',
            api_return_value=api_response,
            expected_partial_output=f"Preferences for user: {user_id}"
        )
        self.mock_api.get_user_preferences.assert_called_once_with(user_id)
        # Add more specific output checks if needed, e.g., for table format

    @async_test
    async def test_cli_get_preferences_json(self):
        user_id = "user_get_json"
        api_response = {"theme": "dark"}
        output = await self.run_cli_command(
            ['get', user_id, '--format', 'json'],
            expected_api_method='get_user_preferences',
            api_return_value=api_response
        )
        self.mock_api.get_user_preferences.assert_called_once_with(user_id)
        try:
            parsed_output = json.loads(output)
            self.assertEqual(parsed_output, api_response)
        except json.JSONDecodeError:
            self.fail(f"Output was not valid JSON: {output}")

    # Test 'set' command
    @async_test
    async def test_cli_set_preference(self):
        user_id = "user_set"
        key = "notifications"
        value_str = "true" # CLI value is string
        parsed_value = True # Expected parsed value
        pref_type = "GENERAL"
        api_response = {'success': True, 'preference_key': key, 'new_value': parsed_value}

        await self.run_cli_command(
            ['set', user_id, key, value_str, pref_type],
            expected_api_method='set_user_preference',
            api_return_value=api_response,
            expected_partial_output=f"Preference '{key}' set to '{parsed_value}'"
        )
        self.mock_api.set_user_preference.assert_called_once_with(user_id, key, parsed_value, pref_type)

    @async_test
    async def test_cli_set_preference_int_value(self):
        user_id = "user_set_int"
        key = "max_items"
        value_str = "25"
        parsed_value = 25
        pref_type = "SEARCH_PREFERENCE"
        api_response = {'success': True}
        await self.run_cli_command(
            ['set', user_id, key, value_str, pref_type],
            expected_api_method='set_user_preference',
            api_return_value=api_response,
            expected_partial_output=f"set to '{parsed_value}' (type: int)"
        )
        self.mock_api.set_user_preference.assert_called_once_with(user_id, key, parsed_value, pref_type)


    @async_test
    async def test_cli_set_preference_float_value(self):
        user_id = "user_set_float"
        key = "threshold"
        value_str = "0.75"
        parsed_value = 0.75
        pref_type = "CLASSIFICATION_THRESHOLD"
        api_response = {'success': True}
        await self.run_cli_command(
            ['set', user_id, key, value_str, pref_type],
            expected_api_method='set_user_preference',
            api_return_value=api_response,
            expected_partial_output=f"set to '{parsed_value}' (type: float)"
        )
        self.mock_api.set_user_preference.assert_called_once_with(user_id, key, parsed_value, pref_type)


    @async_test
    async def test_cli_set_preference_string_value(self):
        user_id = "user_set_string"
        key = "username_display"
        value_str = "CoolUser123"
        # parsed_value is same as value_str for strings
        pref_type = "PROFILE_SETTING" # Assuming a generic type
        api_response = {'success': True}
        await self.run_cli_command(
            ['set', user_id, key, value_str, pref_type],
            expected_api_method='set_user_preference',
            api_return_value=api_response,
            expected_partial_output=f"set to '{value_str}' (type: str)"
        )
        self.mock_api.set_user_preference.assert_called_once_with(user_id, key, value_str, pref_type)


    # Test 'insights' command
    @async_test
    async def test_cli_get_insights_summary(self):
        user_id = "user_insights_summary"
        api_response = {
            'user_id': user_id,
            'insights': {
                'usage_metrics': {'total_interactions': 100, 'avg_session_length_minutes': 30.5, 'preferred_domains': ['tech', 'science'], 'feedback_ratio': 0.25, 'error_rate': 0.05},
                'detected_patterns': [{'type': 'frequent_search', 'confidence': 0.8}]
            },
            'preference_count': 5, 'feedback_history_count': 20
        }
        await self.run_cli_command(
            ['insights', user_id],
            expected_api_method='get_user_behavioral_insights',
            api_return_value=api_response,
            expected_partial_output=f"Behavioral Insights for user: {user_id}"
        )
        self.mock_api.get_user_behavioral_insights.assert_called_once_with(user_id)
        # Add more specific output checks for summary format

    @async_test
    async def test_cli_get_insights_json(self):
        user_id = "user_insights_json"
        api_response = {"user_id": user_id, "insights": {"usage_metrics": {"total_interactions": 50}}}
        output = await self.run_cli_command(
            ['insights', user_id, '--format', 'json'],
            expected_api_method='get_user_behavioral_insights',
            api_return_value=api_response
        )
        self.mock_api.get_user_behavioral_insights.assert_called_once_with(user_id)
        try:
            parsed_output = json.loads(output)
            self.assertEqual(parsed_output, api_response)
        except json.JSONDecodeError:
            self.fail(f"Output was not valid JSON: {output}")

    # Test 'adapt' command
    @async_test
    async def test_cli_trigger_adaptation(self):
        user_id = "user_adapt"
        api_response = {
            'user_id': user_id,
            'adaptation_session': {'patterns_analyzed': 10, 'recommendations_generated': 3, 'adaptations_applied': 2},
            'timestamp': datetime.now().isoformat()
        }
        await self.run_cli_command(
            ['adapt', user_id],
            expected_api_method='trigger_preference_adaptation',
            api_return_value=api_response,
            expected_partial_output=f"Preference adaptation process completed for user: {user_id}"
        )
        self.mock_api.trigger_preference_adaptation.assert_called_once_with(user_id)

    # Test 'recommendations' command
    @async_test
    async def test_cli_get_recommendations(self):
        user_id = "user_recs"
        api_response = {
            'user_id': user_id,
            'recommendations': [{'type': 'increase_limit', 'confidence': 0.75, 'reason': 'Frequent usage'}],
            'recommendation_count': 1,
            'timestamp': datetime.now().isoformat()
        }
        await self.run_cli_command(
            ['recommendations', user_id],
            expected_api_method='get_preference_recommendations',
            api_return_value=api_response,
            expected_partial_output=f"Preference recommendations for user: {user_id} (1 found)"
        )
        self.mock_api.get_preference_recommendations.assert_called_once_with(user_id)

    # Test 'export' command
    @async_test
    async def test_cli_export_data_stdout(self):
        user_id = "user_export_stdout"
        api_response = {"user_id": user_id, "preferences": {"theme": "light"}, "behavioral_insights": {}}
        output = await self.run_cli_command(
            ['export', user_id],
            expected_api_method='export_user_data',
            api_return_value=api_response
        )
        self.mock_api.export_user_data.assert_called_once_with(user_id)
        try:
            parsed_output = json.loads(output)
            self.assertEqual(parsed_output, api_response)
        except json.JSONDecodeError:
            self.fail(f"Output was not valid JSON for export: {output}")

    @patch('builtins.open', new_callable=unittest.mock.mock_open)
    @async_test
    async def test_cli_export_data_file(self, mock_open_file):
        user_id = "user_export_file"
        output_filepath = "test_export.json"
        api_response = {"user_id": user_id, "data_key": "data_value"}

        await self.run_cli_command(
            ['export', user_id, '--output', output_filepath],
            expected_api_method='export_user_data',
            api_return_value=api_response,
            expected_partial_output=f"User data exported to: {output_filepath}"
        )
        self.mock_api.export_user_data.assert_called_once_with(user_id)
        mock_open_file.assert_called_once_with(output_filepath, 'w')
        # Check that json.dumps was called with the data and written to the file handle
        # json.dumps(api_response, indent=2, default=str)
        # For more precise check, you'd capture args to write call of mock_open_file().write
        handle = mock_open_file()
        expected_json_str = json.dumps(api_response, indent=2, default=str)
        handle.write.assert_called_once_with(expected_json_str)


    # Test 'reset' command
    @async_test
    async def test_cli_reset_preferences_keep_explicit(self):
        user_id = "user_reset_keep"
        api_response = {'success': True, 'total_preferences': 5, 'explicit_preferences_kept': 2}
        await self.run_cli_command(
            ['reset', user_id, '--keep-explicit'],
            expected_api_method='reset_user_preferences',
            api_return_value=api_response,
            expected_partial_output=f"Preferences reset for user: {user_id}"
        )
        self.mock_api.reset_user_preferences.assert_called_once_with(user_id, keep_explicit=True)

    @async_test
    async def test_cli_reset_preferences_discard_explicit(self):
        user_id = "user_reset_discard"
        api_response = {'success': True, 'total_preferences': 3, 'explicit_preferences_kept': 0}
        await self.run_cli_command(
            ['reset', user_id], # No --keep-explicit means it's False by default
            expected_api_method='reset_user_preferences',
            api_return_value=api_response,
            expected_partial_output=f"Preferences reset for user: {user_id}"
        )
        self.mock_api.reset_user_preferences.assert_called_once_with(user_id, keep_explicit=False)


    # Test argument parsing for missing command
    @async_test
    async def test_cli_no_command(self):
        # Argparse by default exits if no command is given and subparsers are 'required'
        # To test this, we'd need to check for SystemExit or how argparse handles it.
        # For this helper, we can check if print_help was called on the parser mock.
        # This requires a bit more direct interaction with the parser if it's exposed.
        # The current CLI `run` method handles this by printing help.

        # Temporarily replace the parser with a mock to check print_help
        original_parser = self.cli.parser
        self.cli.parser = MagicMock()
        # self.cli.parser.parse_args = MagicMock(return_value=MagicMock(command=None)) # Simulate no command parsed
        # Simulate argparse exiting when no command is given for a required subparser
        self.cli.parser.parse_args = MagicMock(side_effect=SystemExit(2))


        await self.run_cli_command([], expected_partial_output="Argument parsing error") # argparse help output
        # Instead of print_help, check that parse_args was called, and SystemExit was handled
        self.cli.parser.parse_args.assert_called_once_with([])

        self.cli.parser = original_parser # Restore original parser


    @async_test
    async def test_cli_help_command(self):
        # Testing 'python -m python.helpers.preference_cli -h'
        # Argparse handles -h by printing help and exiting.
        # The run_cli_command captures stdout and can check for help text.
        # The CLI's run method catches SystemExit from argparse and prints help.
        await self.run_cli_command(['-h'], expected_partial_output="usage: preference_cli.py [-h]")


if __name__ == '__main__':
    unittest.main()
