# Phase 4.2: Adaptive Learning Framework
## Behavioral Pattern Recognition and Preference Adaptation

### Overview
This step implements the adaptive learning framework that analyzes user behavior patterns and automatically adjusts preferences to improve classification accuracy and user experience.

### Time Estimate: 2-3 hours

---

## Step 4.2.1: Create Behavioral Pattern Analyzer

**File:** `python/helpers/behavioral_analyzer.py`
**Action:** Create new file

```python
"""
Behavioral Pattern Analyzer for User Preference Learning
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics
import asyncio

from .user_preferences import UserFeedback, LearningSignal

@dataclass
class BehaviorPattern:
    """Represents a detected behavioral pattern"""
    pattern_type: str
    pattern_data: Dict[str, Any]
    confidence: float
    frequency: int
    first_observed: datetime
    last_observed: datetime
    impact_score: float

@dataclass
class UsageMetrics:
    """Usage metrics for pattern analysis"""
    total_interactions: int
    avg_session_length: float
    preferred_domains: List[str]
    search_patterns: Dict[str, int]
    feedback_ratio: float
    error_rate: float

class BehavioralPatternAnalyzer:
    """
    Analyzes user behavior patterns for preference learning
    """

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.interaction_history: List[Dict[str, Any]] = []
        self.detected_patterns: List[BehaviorPattern] = []
        self.usage_metrics = UsageMetrics(0, 0.0, [], {}, 0.0, 0.0)
        
        # Pattern detection thresholds
        self.min_pattern_frequency = 3
        self.pattern_confidence_threshold = 0.6
        self.analysis_window_days = 30

    async def record_interaction(
        self,
        interaction_type: str,
        context: Dict[str, Any],
        timestamp: Optional[datetime] = None
    ) -> None:
        """Record user interaction for pattern analysis"""

        interaction = {
            'type': interaction_type,
            'context': context,
            'timestamp': timestamp or datetime.now(),
            'session_id': context.get('session_id', 'default')
        }

        self.interaction_history.append(interaction)

        # Keep history manageable (last 30 days)
        cutoff_date = datetime.now() - timedelta(days=self.analysis_window_days)
        self.interaction_history = [
            i for i in self.interaction_history 
            if i['timestamp'] > cutoff_date
        ]

        # Update usage metrics
        await self._update_usage_metrics()

    async def analyze_patterns(self) -> List[BehaviorPattern]:
        """Analyze interaction history to detect behavioral patterns"""

        if len(self.interaction_history) < 10:
            return []  # Need minimum interactions for pattern detection

        patterns = []

        # Detect domain preference patterns
        domain_patterns = await self._detect_domain_patterns()
        patterns.extend(domain_patterns)

        # Detect search behavior patterns
        search_patterns = await self._detect_search_patterns()
        patterns.extend(search_patterns)

        # Detect feedback patterns
        feedback_patterns = await self._detect_feedback_patterns()
        patterns.extend(feedback_patterns)

        # Detect temporal patterns
        temporal_patterns = await self._detect_temporal_patterns()
        patterns.extend(temporal_patterns)

        # Filter patterns by confidence and frequency
        significant_patterns = [
            p for p in patterns 
            if p.confidence >= self.pattern_confidence_threshold 
            and p.frequency >= self.min_pattern_frequency
        ]

        self.detected_patterns = significant_patterns
        return significant_patterns

    async def _detect_domain_patterns(self) -> List[BehaviorPattern]:
        """Detect domain preference patterns"""
        patterns = []

        # Analyze domain interactions
        domain_interactions = defaultdict(list)
        for interaction in self.interaction_history:
            if 'domain' in interaction['context']:
                domain = interaction['context']['domain']
                domain_interactions[domain].append(interaction)

        # Detect patterns for each domain
        for domain, interactions in domain_interactions.items():
            if len(interactions) >= self.min_pattern_frequency:
                
                # Calculate success rate for domain
                successful_interactions = sum(
                    1 for i in interactions 
                    if i['context'].get('success', False)
                )
                success_rate = successful_interactions / len(interactions)

                # Calculate average confidence
                confidences = [
                    i['context'].get('confidence', 0.5) 
                    for i in interactions
                ]
                avg_confidence = statistics.mean(confidences) if confidences else 0.5

                # Determine pattern strength
                pattern_confidence = (success_rate + avg_confidence) / 2

                if pattern_confidence > 0.6:
                    pattern = BehaviorPattern(
                        pattern_type='domain_preference',
                        pattern_data={
                            'domain': domain,
                            'success_rate': success_rate,
                            'avg_confidence': avg_confidence,
                            'interaction_count': len(interactions)
                        },
                        confidence=pattern_confidence,
                        frequency=len(interactions),
                        first_observed=min(i['timestamp'] for i in interactions),
                        last_observed=max(i['timestamp'] for i in interactions),
                        impact_score=len(interactions) / len(self.interaction_history)
                    )
                    patterns.append(pattern)

        return patterns

    async def _detect_search_patterns(self) -> List[BehaviorPattern]:
        """Detect search behavior patterns"""
        patterns = []

        search_interactions = [
            i for i in self.interaction_history 
            if i['type'] == 'search'
        ]

        if len(search_interactions) < self.min_pattern_frequency:
            return patterns

        # Analyze search result usage
        result_counts = [
            i['context'].get('results_used', 0) 
            for i in search_interactions
        ]
        
        if result_counts:
            avg_results_used = statistics.mean(result_counts)
            result_limit_requests = [
                i['context'].get('result_limit', 10) 
                for i in search_interactions
            ]
            avg_limit_requested = statistics.mean(result_limit_requests)

            # Detect if user consistently uses fewer results than requested
            if avg_results_used < avg_limit_requested * 0.6:
                pattern = BehaviorPattern(
                    pattern_type='search_result_preference',
                    pattern_data={
                        'avg_results_used': avg_results_used,
                        'avg_limit_requested': avg_limit_requested,
                        'efficiency_ratio': avg_results_used / avg_limit_requested
                    },
                    confidence=0.8,
                    frequency=len(search_interactions),
                    first_observed=min(i['timestamp'] for i in search_interactions),
                    last_observed=max(i['timestamp'] for i in search_interactions),
                    impact_score=0.3
                )
                patterns.append(pattern)

        # Analyze search timing patterns
        search_times = [i['timestamp'].hour for i in search_interactions]
        if search_times:
            time_counter = Counter(search_times)
            most_common_hour = time_counter.most_common(1)[0]
            
            if most_common_hour[1] >= len(search_interactions) * 0.4:
                pattern = BehaviorPattern(
                    pattern_type='search_timing_preference',
                    pattern_data={
                        'preferred_hour': most_common_hour[0],
                        'frequency_at_hour': most_common_hour[1],
                        'total_searches': len(search_interactions)
                    },
                    confidence=0.7,
                    frequency=most_common_hour[1],
                    first_observed=min(i['timestamp'] for i in search_interactions),
                    last_observed=max(i['timestamp'] for i in search_interactions),
                    impact_score=0.2
                )
                patterns.append(pattern)

        return patterns

    async def _detect_feedback_patterns(self) -> List[BehaviorPattern]:
        """Detect feedback behavior patterns"""
        patterns = []

        feedback_interactions = [
            i for i in self.interaction_history 
            if i['type'] == 'feedback'
        ]

        if len(feedback_interactions) < self.min_pattern_frequency:
            return patterns

        # Analyze feedback types
        feedback_types = [
            i['context'].get('feedback_type', 'unknown') 
            for i in feedback_interactions
        ]
        
        feedback_counter = Counter(feedback_types)
        total_feedback = len(feedback_interactions)

        for feedback_type, count in feedback_counter.items():
            if count >= self.min_pattern_frequency:
                pattern = BehaviorPattern(
                    pattern_type='feedback_behavior',
                    pattern_data={
                        'feedback_type': feedback_type,
                        'frequency': count,
                        'percentage': count / total_feedback
                    },
                    confidence=min(0.9, count / total_feedback + 0.3),
                    frequency=count,
                    first_observed=min(
                        i['timestamp'] for i in feedback_interactions 
                        if i['context'].get('feedback_type') == feedback_type
                    ),
                    last_observed=max(
                        i['timestamp'] for i in feedback_interactions 
                        if i['context'].get('feedback_type') == feedback_type
                    ),
                    impact_score=count / len(self.interaction_history)
                )
                patterns.append(pattern)

        return patterns

    async def _detect_temporal_patterns(self) -> List[BehaviorPattern]:
        """Detect temporal usage patterns"""
        patterns = []

        if len(self.interaction_history) < 20:
            return patterns

        # Analyze daily usage patterns
        daily_counts = defaultdict(int)
        for interaction in self.interaction_history:
            day = interaction['timestamp'].strftime('%A')
            daily_counts[day] += 1

        # Find peak usage days
        total_interactions = len(self.interaction_history)
        for day, count in daily_counts.items():
            if count >= total_interactions * 0.2:  # 20% of interactions on this day
                pattern = BehaviorPattern(
                    pattern_type='temporal_usage',
                    pattern_data={
                        'peak_day': day,
                        'interaction_count': count,
                        'percentage': count / total_interactions
                    },
                    confidence=0.7,
                    frequency=count,
                    first_observed=min(self.interaction_history, key=lambda x: x['timestamp'])['timestamp'],
                    last_observed=max(self.interaction_history, key=lambda x: x['timestamp'])['timestamp'],
                    impact_score=0.1
                )
                patterns.append(pattern)

        return patterns

    async def _update_usage_metrics(self) -> None:
        """Update overall usage metrics"""

        if not self.interaction_history:
            return

        # Calculate basic metrics
        self.usage_metrics.total_interactions = len(self.interaction_history)

        # Calculate average session length
        sessions = defaultdict(list)
        for interaction in self.interaction_history:
            session_id = interaction['context'].get('session_id', 'default')
            sessions[session_id].append(interaction['timestamp'])

        session_lengths = []
        for session_times in sessions.values():
            if len(session_times) > 1:
                session_length = (max(session_times) - min(session_times)).total_seconds() / 60
                session_lengths.append(session_length)

        self.usage_metrics.avg_session_length = statistics.mean(session_lengths) if session_lengths else 0.0

        # Calculate preferred domains
        domain_counts = Counter()
        for interaction in self.interaction_history:
            domain = interaction['context'].get('domain')
            if domain:
                domain_counts[domain] += 1

        self.usage_metrics.preferred_domains = [
            domain for domain, count in domain_counts.most_common(5)
        ]

        # Calculate search patterns
        search_interactions = [i for i in self.interaction_history if i['type'] == 'search']
        search_patterns = Counter()
        for interaction in search_interactions:
            query_type = interaction['context'].get('query_type', 'general')
            search_patterns[query_type] += 1

        self.usage_metrics.search_patterns = dict(search_patterns)

        # Calculate feedback ratio
        feedback_interactions = [i for i in self.interaction_history if i['type'] == 'feedback']
        self.usage_metrics.feedback_ratio = len(feedback_interactions) / len(self.interaction_history)

        # Calculate error rate
        error_interactions = [
            i for i in self.interaction_history 
            if i['context'].get('error', False)
        ]
        self.usage_metrics.error_rate = len(error_interactions) / len(self.interaction_history)

    async def get_pattern_recommendations(self) -> List[Dict[str, Any]]:
        """Get recommendations based on detected patterns"""

        recommendations = []

        for pattern in self.detected_patterns:
            if pattern.pattern_type == 'domain_preference':
                domain = pattern.pattern_data['domain']
                success_rate = pattern.pattern_data['success_rate']
                
                if success_rate > 0.8:
                    recommendations.append({
                        'type': 'increase_domain_weight',
                        'domain': domain,
                        'current_success_rate': success_rate,
                        'recommended_weight_increase': 0.1,
                        'confidence': pattern.confidence
                    })

            elif pattern.pattern_type == 'search_result_preference':
                avg_used = pattern.pattern_data['avg_results_used']
                recommendations.append({
                    'type': 'adjust_search_limit',
                    'current_avg_used': avg_used,
                    'recommended_limit': int(avg_used * 1.2),
                    'confidence': pattern.confidence
                })

            elif pattern.pattern_type == 'feedback_behavior':
                feedback_type = pattern.pattern_data['feedback_type']
                if feedback_type == 'negative' and pattern.pattern_data['percentage'] > 0.3:
                    recommendations.append({
                        'type': 'review_classification_thresholds',
                        'negative_feedback_rate': pattern.pattern_data['percentage'],
                        'recommended_action': 'lower_confidence_threshold',
                        'confidence': pattern.confidence
                    })

        return recommendations

    async def export_analysis_report(self) -> Dict[str, Any]:
        """Export comprehensive analysis report"""

        return {
            'user_id': self.user_id,
            'analysis_timestamp': datetime.now(),
            'analysis_period_days': self.analysis_window_days,
            'usage_metrics': {
                'total_interactions': self.usage_metrics.total_interactions,
                'avg_session_length_minutes': self.usage_metrics.avg_session_length,
                'preferred_domains': self.usage_metrics.preferred_domains,
                'search_patterns': self.usage_metrics.search_patterns,
                'feedback_ratio': self.usage_metrics.feedback_ratio,
                'error_rate': self.usage_metrics.error_rate
            },
            'detected_patterns': [
                {
                    'type': p.pattern_type,
                    'data': p.pattern_data,
                    'confidence': p.confidence,
                    'frequency': p.frequency,
                    'impact_score': p.impact_score
                }
                for p in self.detected_patterns
            ],
            'recommendations': await self.get_pattern_recommendations()
        }
```

**Validation:**
```python
# Test behavioral pattern analyzer
from python.helpers.behavioral_analyzer import BehavioralPatternAnalyzer
from datetime import datetime

analyzer = BehavioralPatternAnalyzer("test_user")

# Record sample interactions
await analyzer.record_interaction(
    'search',
    {
        'domain': 'programming',
        'results_used': 3,
        'result_limit': 10,
        'success': True,
        'confidence': 0.8
    }
)

await analyzer.record_interaction(
    'feedback',
    {
        'feedback_type': 'positive',
        'domain': 'programming'
    }
)

# Analyze patterns
patterns = await analyzer.analyze_patterns()
print(f"Detected {len(patterns)} behavioral patterns")

# Get recommendations
recommendations = await analyzer.get_pattern_recommendations()
print(f"Generated {len(recommendations)} recommendations")
```

---

## Step 4.2.2: Implement Preference Adaptation Engine

**File:** `python/helpers/preference_adaptation.py`
**Action:** Create new file

```python
"""
Preference Adaptation Engine - Applies behavioral insights to preferences
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio

from .user_preferences import UserPreferenceManager, PreferenceType
from .behavioral_analyzer import BehavioralPatternAnalyzer

class PreferenceAdaptationEngine:
    """
    Adapts user preferences based on behavioral pattern analysis
    """

    def __init__(self, preference_manager: UserPreferenceManager):
        self.preference_manager = preference_manager
        self.behavioral_analyzer = BehavioralPatternAnalyzer(preference_manager.user_id)
        self.adaptation_history: List[Dict[str, Any]] = []

    async def analyze_and_adapt(self) -> Dict[str, Any]:
        """Analyze behavior patterns and adapt preferences"""

        # Get behavioral patterns
        patterns = await self.behavioral_analyzer.analyze_patterns()
        
        # Get recommendations
        recommendations = await self.behavioral_analyzer.get_pattern_recommendations()

        # Apply adaptations
        adaptations_made = []
        for recommendation in recommendations:
            adaptation = await self._apply_recommendation(recommendation)
            if adaptation:
                adaptations_made.append(adaptation)

        # Record adaptation session
        adaptation_session = {
            'timestamp': datetime.now(),
            'patterns_analyzed': len(patterns),
            'recommendations_generated': len(recommendations),
            'adaptations_applied': len(adaptations_made),
            'adaptations': adaptations_made
        }

        self.adaptation_history.append(adaptation_session)

        return adaptation_session

    async def _apply_recommendation(self, recommendation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Apply a specific recommendation"""

        rec_type = recommendation['type']
        
        if rec_type == 'increase_domain_weight':
            return await self._adapt_domain_weight(recommendation)
        elif rec_type == 'adjust_search_limit':
            return await self._adapt_search_limit(recommendation)
        elif rec_type == 'review_classification_thresholds':
            return await self._adapt_classification_threshold(recommendation)

        return None

    async def _adapt_domain_weight(self, recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt domain weight based on recommendation"""

        domain = recommendation['domain']
        weight_increase = recommendation['recommended_weight_increase']
        confidence = recommendation['confidence']

        preference_key = f'domain_weight_{domain}'
        current_weight = await self.preference_manager.get_preference(preference_key, 0.5)
        
        new_weight = min(1.0, current_weight + weight_increase)

        success = await self.preference_manager.set_preference(
            preference_key,
            new_weight,
            PreferenceType.DOMAIN_WEIGHT,
            user_explicit=False
        )

        if success:
            return {
                'type': 'domain_weight_adaptation',
                'domain': domain,
                'old_weight': current_weight,
                'new_weight': new_weight,
                'confidence': confidence,
                'reason': f'High success rate ({recommendation["current_success_rate"]:.2f})'
            }

        return None

    async def _adapt_search_limit(self, recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt search result limit based on recommendation"""

        recommended_limit = recommendation['recommended_limit']
        confidence = recommendation['confidence']

        preference_key = 'search_result_limit'
        current_limit = await self.preference_manager.get_preference(preference_key, 10)
        
        success = await self.preference_manager.set_preference(
            preference_key,
            recommended_limit,
            PreferenceType.SEARCH_PREFERENCE,
            user_explicit=False
        )

        if success:
            return {
                'type': 'search_limit_adaptation',
                'old_limit': current_limit,
                'new_limit': recommended_limit,
                'confidence': confidence,
                'reason': f'Average usage ({recommendation["current_avg_used"]:.1f}) suggests lower limit'
            }

        return None

    async def _adapt_classification_threshold(self, recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """Adapt classification threshold based on recommendation"""

        negative_feedback_rate = recommendation['negative_feedback_rate']
        confidence = recommendation['confidence']

        preference_key = 'classification_confidence_threshold'
        current_threshold = await self.preference_manager.get_preference(preference_key, 0.7)
        
        # Lower threshold if too much negative feedback
        new_threshold = max(0.5, current_threshold - 0.05)

        success = await self.preference_manager.set_preference(
            preference_key,
            new_threshold,
            PreferenceType.CLASSIFICATION_THRESHOLD,
            user_explicit=False
        )

        if success:
            return {
                'type': 'threshold_adaptation',
                'old_threshold': current_threshold,
                'new_threshold': new_threshold,
                'confidence': confidence,
                'reason': f'High negative feedback rate ({negative_feedback_rate:.2f})'
            }

        return None
```

**Validation:**
```python
# Test preference adaptation engine
from python.helpers.preference_adaptation import PreferenceAdaptationEngine
from python.helpers.user_preferences import UserPreferenceManager

# Create managers
preference_manager = UserPreferenceManager("test_user")
adaptation_engine = PreferenceAdaptationEngine(preference_manager)

# Run adaptation analysis
adaptation_session = await adaptation_engine.analyze_and_adapt()
print(f"Adaptation session: {adaptation_session['adaptations_applied']} adaptations applied")
```

---

## Next Steps

✅ **Step 4.2 Complete - Validation Checklist:**
- [ ] Behavioral pattern analyzer can detect usage patterns
- [ ] Pattern analysis generates actionable recommendations
- [ ] Preference adaptation engine applies recommendations automatically
- [ ] Adaptation history is maintained for review
- [ ] All pattern types (domain, search, feedback, temporal) are detected

**Next:** Proceed to Step 4.3 - Classification Integration
