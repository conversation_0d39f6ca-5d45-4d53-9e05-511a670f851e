"""
Command-line interface for preference management
"""

import asyncio
import json
from typing import Dict, Any # No List needed here based on usage
import argparse

# Attempt to import the global preference_api instance from preference_api.py
try:
    from .preference_api import preference_api
except ImportError:
    print("WARN: preference_cli.py: Could not import 'preference_api' from .preference_api. Using a placeholder.")
    # Placeholder for preference_api if import fails
    class PlaceholderPreferenceAPI:
        async def get_user_preferences(self, user_id: str) -> Dict[str, Any]: return {"placeholder_pref": "value"}
        async def set_user_preference(self, user_id: str, key: str, value: Any, type_str: str) -> Dict[str, Any]: return {'success': True}
        async def get_user_behavioral_insights(self, user_id: str) -> Dict[str, Any]: return {'insights': {'usage_metrics': {}, 'detected_patterns': []}}
        async def trigger_preference_adaptation(self, user_id: str) -> Dict[str, Any]: return {'adaptation_session': {}}
        async def get_preference_recommendations(self, user_id: str) -> Dict[str, Any]: return {'recommendations': []}
        async def export_user_data(self, user_id: str) -> Dict[str, Any]: return {'user_id': user_id, 'data': 'placeholder_export'}
        async def reset_user_preferences(self, user_id: str, keep_explicit: bool = True) -> Dict[str, Any]: return {'success': True, 'total_preferences': 0}

    preference_api = PlaceholderPreferenceAPI()


class PreferenceCLI:
    """Command-line interface for user preference management"""

    def __init__(self):
        self.parser = self._create_parser()

    def _create_parser(self) -> argparse.ArgumentParser:
        """Create command-line argument parser"""
        parser = argparse.ArgumentParser(
            description='Agent Zero User Preference Management CLI',
            prog='preference_cli.py' # Set program name for help messages
        )
        subparsers = parser.add_subparsers(dest='command', help='Available commands', required=True) # Made command required

        # Get preferences command
        get_parser = subparsers.add_parser('get', help='Get user preferences')
        get_parser.add_argument('user_id', help='User ID')
        get_parser.add_argument('--format', choices=['json', 'table'], default='table', help='Output format')

        # Set preference command
        set_parser = subparsers.add_parser('set', help='Set user preference')
        set_parser.add_argument('user_id', help='User ID')
        set_parser.add_argument('preference_key', help='Preference key')
        set_parser.add_argument('value', help='Preference value')
        set_parser.add_argument('preference_type', help='Preference type (e.g., GENERAL, DOMAIN_WEIGHT)') # Clarified help

        # Insights command
        insights_parser = subparsers.add_parser('insights', help='Get behavioral insights')
        insights_parser.add_argument('user_id', help='User ID')
        insights_parser.add_argument('--format', choices=['json', 'summary'], default='summary', help='Output format')

        # Adapt command
        adapt_parser = subparsers.add_parser('adapt', help='Trigger preference adaptation')
        adapt_parser.add_argument('user_id', help='User ID')

        # Recommendations command
        rec_parser = subparsers.add_parser('recommendations', help='Get preference recommendations')
        rec_parser.add_argument('user_id', help='User ID')

        # Export command
        export_parser = subparsers.add_parser('export', help='Export user data')
        export_parser.add_argument('user_id', help='User ID')
        export_parser.add_argument('--output', help='Output file path (outputs to stdout if not provided)') # Clarified behavior

        # Reset command
        reset_parser = subparsers.add_parser('reset', help='Reset user preferences')
        reset_parser.add_argument('user_id', help='User ID')
        reset_parser.add_argument('--keep-explicit', action='store_true', help='Keep explicitly set preferences')

        return parser

    async def run(self, args: list = None) -> None: # Type hint for args
        """Run the CLI with given arguments"""
        try:
            parsed_args = self.parser.parse_args(args) # args=None means sys.argv[1:] by default
        except SystemExit as e: # Handle cases like -h or invalid arguments that cause exit
            if e.code == 0: # Successful exit (e.g. help message)
                 return
            # For other SystemExit (like argument errors), argparse already prints help.
            # We might want to re-raise or handle differently if needed, but often not necessary.
            print(f"Argument parsing error. Use -h or --help for usage information.")
            return


        if not parsed_args.command: # Should not happen if subparsers are 'required'
            self.parser.print_help()
            return

        try:
            if parsed_args.command == 'get':
                await self._handle_get_preferences(parsed_args)
            elif parsed_args.command == 'set':
                await self._handle_set_preference(parsed_args)
            elif parsed_args.command == 'insights':
                await self._handle_get_insights(parsed_args)
            elif parsed_args.command == 'adapt':
                await self._handle_trigger_adaptation(parsed_args)
            elif parsed_args.command == 'recommendations':
                await self._handle_get_recommendations(parsed_args)
            elif parsed_args.command == 'export':
                await self._handle_export_data(parsed_args)
            elif parsed_args.command == 'reset':
                await self._handle_reset_preferences(parsed_args)
        except Exception as e:
            print(f"An error occurred while executing command '{parsed_args.command}': {e}")
            # Consider more detailed error logging or specific exception handling here

    async def _handle_get_preferences(self, args: argparse.Namespace) -> None: # Type hint for args
        """Handle get preferences command"""
        prefs = await preference_api.get_user_preferences(args.user_id)
        if args.format == 'json':
            print(json.dumps(prefs, indent=2, default=str)) # default=str for datetime
        else: # table format
            print(f"\nPreferences for user: {args.user_id}")
            print("-" * 50)
            if prefs:
                for key, pref_data in prefs.items():
                    # Ensure all expected keys are present, provide defaults if not
                    value = pref_data.get('value', 'N/A')
                    confidence = pref_data.get('confidence', float('nan')) # Use float('nan') for missing numeric
                    pref_type = pref_data.get('type', 'N/A')
                    print(f"{key:30} = {str(value):<15} (Type: {pref_type}, Confidence: {confidence:.2f})")
            else:
                print("No preferences found for this user.")

    async def _handle_set_preference(self, args: argparse.Namespace) -> None: # Type hint for args
        """Handle set preference command"""
        # Try to parse value as appropriate type (bool, float, int, then string)
        value_str = args.value
        parsed_value: Any
        if value_str.lower() == 'true':
            parsed_value = True
        elif value_str.lower() == 'false':
            parsed_value = False
        else:
            try:
                parsed_value = float(value_str)
                # If it's a whole number float, convert to int
                if parsed_value.is_integer():
                    parsed_value = int(parsed_value)
            except ValueError:
                # Not a float, try int directly (for cases like "007" that float would parse but int should handle)
                try:
                    parsed_value = int(value_str)
                except ValueError:
                    parsed_value = value_str # Keep as string if not bool, float, or int

        result = await preference_api.set_user_preference(
            args.user_id, args.preference_key, parsed_value, args.preference_type
        )
        if result.get('success'): # Check for key existence
            print(f"✓ Preference '{args.preference_key}' set to '{parsed_value}' (type: {type(parsed_value).__name__}) for user {args.user_id}")
        else:
            print(f"✗ Failed to set preference: {result.get('error', 'Unknown error')}")

    async def _handle_get_insights(self, args: argparse.Namespace) -> None: # Type hint for args
        """Handle get insights command"""
        insights_data = await preference_api.get_user_behavioral_insights(args.user_id)
        if args.format == 'json':
            print(json.dumps(insights_data, indent=2, default=str))
        else: # summary format
            print(f"\nBehavioral Insights for user: {args.user_id}")
            print("-" * 50)
            # Safely access nested dictionary keys
            report = insights_data.get('insights', {})
            metrics = report.get('usage_metrics', {})
            print(f"Total interactions: {metrics.get('total_interactions', 'N/A')}")
            avg_session = metrics.get('avg_session_length_minutes', float('nan'))
            print(f"Average session length: {avg_session:.1f} minutes")
            print(f"Preferred domains: {', '.join(metrics.get('preferred_domains', [])) or 'N/A'}")
            feedback_ratio = metrics.get('feedback_ratio', float('nan'))
            print(f"Feedback ratio: {feedback_ratio:.2f}")
            error_rate = metrics.get('error_rate', float('nan'))
            print(f"Error rate: {error_rate:.2f}")

            patterns = report.get('detected_patterns', [])
            print(f"\nDetected patterns: {len(patterns)}")
            for i, pattern in enumerate(patterns[:5], 1): # Show top 5 or fewer
                p_type = pattern.get('type', 'N/A')
                p_conf = pattern.get('confidence', float('nan'))
                print(f"  {i}. {p_type}: confidence {p_conf:.2f}")


    async def _handle_trigger_adaptation(self, args: argparse.Namespace) -> None: # Type hint for args
        """Handle trigger adaptation command"""
        result = await preference_api.trigger_preference_adaptation(args.user_id)
        session = result.get('adaptation_session', {})
        print(f"\nPreference adaptation process completed for user: {args.user_id}")
        print(f"Patterns analyzed: {session.get('patterns_analyzed', 'N/A')}")
        print(f"Recommendations generated: {session.get('recommendations_generated', 'N/A')}")
        print(f"Adaptations applied: {session.get('adaptations_applied', 'N/A')}")
        adaptations_made = session.get('adaptations', [])
        if adaptations_made:
            print("\nAdaptations made:")
            for i, adaptation in enumerate(adaptations_made, 1):
                adapt_type = adaptation.get('type', 'N/A')
                adapt_reason = adaptation.get('reason', 'No reason provided')
                print(f"  {i}. {adapt_type}: {adapt_reason}")


    async def _handle_get_recommendations(self, args: argparse.Namespace) -> None: # Type hint for args
        """Handle get recommendations command"""
        result = await preference_api.get_preference_recommendations(args.user_id)
        recommendations = result.get('recommendations', [])
        print(f"\nPreference recommendations for user: {args.user_id} ({len(recommendations)} found)")
        print("-" * 50)
        if not recommendations:
            print("No recommendations available at this time.")
        else:
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. Type: {rec.get('type', 'N/A')}")
                rec_conf = rec.get('confidence', float('nan'))
                print(f"   Confidence: {rec_conf:.2f}")
                if 'reason' in rec:
                    print(f"   Reason: {rec.get('reason')}")
                if 'parameters' in rec and rec.get('parameters'): # Added check for non-empty params
                    print(f"   Parameters: {json.dumps(rec.get('parameters'))}")
                print() # Blank line for readability

    async def _handle_export_data(self, args: argparse.Namespace) -> None: # Type hint for args
        """Handle export data command"""
        data = await preference_api.export_user_data(args.user_id)
        json_data = json.dumps(data, indent=2, default=str)
        if args.output:
            try:
                with open(args.output, 'w') as f:
                    f.write(json_data)
                print(f"User data exported to: {args.output}")
            except IOError as e:
                print(f"Error writing to file {args.output}: {e}")
        else:
            print(json_data)

    async def _handle_reset_preferences(self, args: argparse.Namespace) -> None: # Type hint for args
        """Handle reset preferences command"""
        result = await preference_api.reset_user_preferences(args.user_id, keep_explicit=args.keep_explicit)
        if result.get('success'):
            print(f"✓ Preferences reset for user: {args.user_id}")
            print(f"  Total preferences after reset: {result.get('total_preferences', 'N/A')}")
            if args.keep_explicit:
                print(f"  Explicit preferences kept: {result.get('explicit_preferences_kept', 'N/A')}")
        else:
            print(f"✗ Failed to reset preferences: {result.get('error', 'Unknown error')}")


# CLI instance (as per the document)
preference_cli = PreferenceCLI()

async def main(): # Renamed from main_cli_entry_point for consistency with doc
    """Main CLI entry point"""
    # No sys import needed here if called via preference_cli.run() without args (uses sys.argv by default)
    await preference_cli.run() # argparse handles sys.argv by default if args is None

if __name__ == '__main__':
    # Example of how to run (actual execution might be via a script or module invocation)
    # To test from command line: python -m python.helpers.preference_cli get test_user
    asyncio.run(main())
