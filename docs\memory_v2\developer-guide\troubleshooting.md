# Troubleshooting Guide - Agent Zero v2.0

This comprehensive troubleshooting guide covers common issues, diagnostic procedures, and solutions for Agent Zero v2.0. Use this guide to quickly identify and resolve problems with installation, configuration, memory systems, and runtime issues.

## Quick Diagnostic Checklist

### System Health Check

```bash
# 1. Check Docker status
docker ps
docker logs agent-zero-v2

# 2. Check port accessibility
curl http://localhost:8080/health

# 3. Check memory backend status
curl http://localhost:8080/api/memory/status

# 4. Check available disk space
df -h

# 5. Check system resources
docker stats agent-zero-v2
```

### Configuration Validation

```bash
# Validate environment variables
docker exec agent-zero-v2 env | grep -E "(MEMORY_BACKEND|NEO4J|OPENAI)"

# Check settings file
docker exec agent-zero-v2 cat /a0/settings.json

# Validate API keys
curl -H "Authorization: Bearer $OPENAI_API_KEY" https://api.openai.com/v1/models
```

## Installation Issues

### Docker Problems

**Issue: Docker container won't start**
```bash
# Check Docker Desktop status
docker version

# Check available resources
docker system df
docker system prune  # Clean up if needed

# Check port conflicts
netstat -tulpn | grep 8080
lsof -i :8080  # macOS/Linux

# Try different port
docker run -p 8081:80 frdel/agent-zero-run:latest
```

**Issue: Container starts but web UI inaccessible**
```bash
# Check container logs
docker logs agent-zero-v2 --tail 50

# Check container networking
docker inspect agent-zero-v2 | grep -A 10 "NetworkSettings"

# Test internal connectivity
docker exec agent-zero-v2 curl http://localhost:80
```

**Issue: Volume mount problems**
```bash
# Check volume permissions
ls -la ~/agent-zero-data
chmod 755 ~/agent-zero-data  # Fix permissions if needed

# Verify volume mount
docker inspect agent-zero-v2 | grep -A 5 "Mounts"

# Test file access
docker exec agent-zero-v2 ls -la /a0
```

### Environment Setup Issues

**Issue: Python environment problems (development setup)**
```bash
# Check Python version
python --version  # Should be 3.11 or 3.12

# Verify virtual environment
which python
pip list | grep -E "(torch|transformers|langchain)"

# Reinstall requirements
pip install -r requirements.txt --force-reinstall
```

**Issue: Missing dependencies**
```bash
# Check for missing system dependencies
docker exec agent-zero-v2 which git
docker exec agent-zero-v2 which curl

# Install missing packages
docker exec -u root agent-zero-v2 apt-get update
docker exec -u root agent-zero-v2 apt-get install -y git curl
```

## Memory System Issues

### FAISS Backend Problems

**Issue: FAISS index corruption**
```bash
# Check FAISS files
docker exec agent-zero-v2 ls -la /a0/memory/

# Backup and rebuild index
docker exec agent-zero-v2 cp -r /a0/memory /a0/memory_backup
docker exec agent-zero-v2 rm -rf /a0/memory/faiss_*

# Restart to rebuild
docker restart agent-zero-v2
```

**Issue: Memory search not working**
```python
# Test memory operations via API
import requests

# Test memory save
response = requests.post('http://localhost:8080/api/memory/save', json={
    'content': 'Test memory content',
    'metadata': {'test': True}
})
print(response.status_code, response.json())

# Test memory search
response = requests.get('http://localhost:8080/api/memory/search', params={
    'query': 'test',
    'limit': 5
})
print(response.status_code, response.json())
```

### Graphiti Backend Problems

**Issue: Neo4j connection failed**
```bash
# Test Neo4j connectivity
docker exec neo4j-graphiti cypher-shell -u neo4j -p your-password "RETURN 'Connected' as status"

# Check Neo4j logs
docker logs neo4j-graphiti --tail 50

# Verify network connectivity
docker exec agent-zero-v2 telnet neo4j-graphiti 7687
```

**Issue: Graphiti initialization errors**
```bash
# Check Graphiti configuration
docker exec agent-zero-v2 env | grep -E "(NEO4J|GRAPHITI)"

# Test OpenAI API for embeddings
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"input": "test", "model": "text-embedding-ada-002"}' \
     https://api.openai.com/v1/embeddings

# Check Graphiti logs
docker exec agent-zero-v2 grep -i graphiti /a0/logs/agent.log
```

**Issue: Entity extraction not working**
```python
# Test entity extraction manually
import asyncio
from python.helpers.memory_abstraction import MemoryManager

async def test_entity_extraction():
    memory = MemoryManager()
    await memory.initialize()
    
    result = await memory.import_knowledge(
        content="Python is a programming language used for web development.",
        source="test.txt",
        extract_entities=True
    )
    print(f"Document ID: {result}")

asyncio.run(test_entity_extraction())
```

## Model and API Issues

### OpenAI API Problems

**Issue: API key authentication failed**
```bash
# Verify API key format
echo $OPENAI_API_KEY | wc -c  # Should be around 51 characters

# Test API key
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# Check API usage and limits
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/usage
```

**Issue: Rate limiting or quota exceeded**
```bash
# Check rate limit headers
curl -I -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models

# Monitor API usage
docker exec agent-zero-v2 grep -i "rate limit" /a0/logs/agent.log
```

### Local Model Issues (Ollama)

**Issue: Ollama connection failed**
```bash
# Check Ollama status
curl http://localhost:11434/api/tags

# Test model availability
curl http://localhost:11434/api/show -d '{"name": "llama3.2"}'

# Check Ollama logs
docker logs ollama-container  # If running in Docker
```

**Issue: Model performance problems**
```bash
# Check available models
ollama list

# Monitor resource usage
docker stats ollama-container

# Test model response time
time curl -X POST http://localhost:11434/api/generate \
     -d '{"model": "llama3.2", "prompt": "Hello", "stream": false}'
```

## Performance Issues

### Slow Response Times

**Diagnostic Steps:**
```bash
# Check system resources
docker stats agent-zero-v2

# Monitor memory usage
docker exec agent-zero-v2 free -h

# Check disk I/O
docker exec agent-zero-v2 iostat -x 1 5

# Profile agent performance
docker exec agent-zero-v2 python -m cProfile -o profile.stats agent.py
```

**Optimization Solutions:**
```bash
# Increase Docker memory allocation
# In Docker Desktop: Settings > Resources > Memory

# Use GPU acceleration for FAISS
# Set FAISS_INDEX_TYPE=gpu in environment

# Optimize model selection
# Use smaller models for utility tasks
# Configure appropriate context lengths
```

### Memory Usage Issues

**Issue: High memory consumption**
```bash
# Check memory breakdown
docker exec agent-zero-v2 python -c "
import psutil
process = psutil.Process()
print(f'Memory: {process.memory_info().rss / 1024 / 1024:.2f} MB')
print(f'Memory %: {process.memory_percent():.2f}%')
"

# Check FAISS index size
docker exec agent-zero-v2 du -sh /a0/memory/

# Check Neo4j memory usage (if using Graphiti)
docker exec neo4j-graphiti cypher-shell -u neo4j -p your-password \
    "CALL dbms.listQueries() YIELD query, elapsedTimeMillis, allocatedBytes"
```

**Memory Optimization:**
```bash
# Clean up old memories
curl -X DELETE http://localhost:8080/api/memory/cleanup \
     -d '{"older_than": "30_days", "importance_threshold": 0.3}'

# Rebuild indices
curl -X POST http://localhost:8080/api/memory/rebuild-indices

# Configure memory limits
docker update --memory=4g agent-zero-v2
```

## Configuration Issues

### Settings File Problems

**Issue: Settings not persisting**
```bash
# Check settings file location
docker exec agent-zero-v2 find /a0 -name "settings.json"

# Verify file permissions
docker exec agent-zero-v2 ls -la /a0/settings.json

# Check for write errors
docker exec agent-zero-v2 tail -f /a0/logs/agent.log | grep -i settings
```

**Issue: Invalid configuration values**
```python
# Validate configuration programmatically
import json

def validate_config(config_path):
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check required fields
        required_fields = ['memory_backend', 'models']
        for field in required_fields:
            if field not in config:
                print(f"Missing required field: {field}")
        
        # Validate model configurations
        if 'models' in config:
            for model_type, model_config in config['models'].items():
                if 'provider' not in model_config:
                    print(f"Missing provider for {model_type}")
                if 'model_name' not in model_config:
                    print(f"Missing model_name for {model_type}")
        
        print("Configuration validation complete")
        
    except json.JSONDecodeError as e:
        print(f"Invalid JSON in config file: {e}")
    except Exception as e:
        print(f"Error validating config: {e}")

# Run validation
validate_config('/path/to/settings.json')
```

### Environment Variable Issues

**Issue: Environment variables not loaded**
```bash
# Check .env file
docker exec agent-zero-v2 cat /a0/.env

# Verify environment loading
docker exec agent-zero-v2 python -c "
import os
from dotenv import load_dotenv
load_dotenv('/a0/.env')
print('MEMORY_BACKEND:', os.getenv('MEMORY_BACKEND'))
print('OPENAI_API_KEY:', 'Set' if os.getenv('OPENAI_API_KEY') else 'Not set')
"
```

## Network and Connectivity Issues

### Web UI Access Problems

**Issue: Cannot access web interface**
```bash
# Check if service is running
curl -I http://localhost:8080

# Check firewall settings
sudo ufw status  # Linux
netsh advfirewall show allprofiles  # Windows

# Try different browsers
# Clear browser cache and cookies
# Disable browser extensions
```

**Issue: API endpoints not responding**
```bash
# Test API health endpoint
curl http://localhost:8080/api/health

# Check API routes
curl http://localhost:8080/api/routes

# Test with verbose output
curl -v http://localhost:8080/api/memory/status
```

### External Service Connectivity

**Issue: Cannot reach external APIs**
```bash
# Test internet connectivity
docker exec agent-zero-v2 ping -c 3 *******

# Test DNS resolution
docker exec agent-zero-v2 nslookup api.openai.com

# Test HTTPS connectivity
docker exec agent-zero-v2 curl -I https://api.openai.com

# Check proxy settings
docker exec agent-zero-v2 env | grep -i proxy
```

## Data and File Issues

### File System Problems

**Issue: File permissions errors**
```bash
# Check file ownership
docker exec agent-zero-v2 ls -la /a0/

# Fix permissions
docker exec -u root agent-zero-v2 chown -R app:app /a0/
docker exec -u root agent-zero-v2 chmod -R 755 /a0/
```

**Issue: Disk space problems**
```bash
# Check available space
docker exec agent-zero-v2 df -h

# Find large files
docker exec agent-zero-v2 find /a0 -type f -size +100M -exec ls -lh {} \;

# Clean up logs
docker exec agent-zero-v2 find /a0/logs -name "*.log" -mtime +7 -delete
```

## Recovery Procedures

### Complete System Reset

```bash
# 1. Stop and remove container
docker stop agent-zero-v2
docker rm agent-zero-v2

# 2. Backup important data
cp -r ~/agent-zero-data ~/agent-zero-backup

# 3. Clean up Docker resources
docker system prune -a

# 4. Pull fresh image and restart
docker pull frdel/agent-zero-run:latest
docker run -d --name agent-zero-v2 -p 8080:80 -v ~/agent-zero-data:/a0 frdel/agent-zero-run:latest
```

### Memory System Reset

```bash
# Backup current memory
docker exec agent-zero-v2 tar -czf /a0/memory_backup.tar.gz /a0/memory/

# Reset FAISS backend
docker exec agent-zero-v2 rm -rf /a0/memory/faiss_*

# Reset Graphiti backend (if using Neo4j)
docker exec neo4j-graphiti cypher-shell -u neo4j -p your-password \
    "MATCH (n) DETACH DELETE n"

# Restart Agent Zero
docker restart agent-zero-v2
```

## Getting Additional Help

### Log Analysis

```bash
# Collect comprehensive logs
docker logs agent-zero-v2 > agent-zero.log
docker exec agent-zero-v2 tar -czf /tmp/logs.tar.gz /a0/logs/
docker cp agent-zero-v2:/tmp/logs.tar.gz ./agent-zero-logs.tar.gz
```

### System Information

```bash
# Collect system information for support
echo "=== System Information ===" > debug-info.txt
docker version >> debug-info.txt
docker info >> debug-info.txt
echo "=== Container Status ===" >> debug-info.txt
docker ps -a >> debug-info.txt
echo "=== Environment ===" >> debug-info.txt
docker exec agent-zero-v2 env >> debug-info.txt
```

### Community Support

- **Discord**: [Join our Discord](https://discord.gg/Z2tun2N3) for real-time help
- **GitHub Issues**: [Report bugs](https://github.com/frdel/agent-zero/issues) with detailed information
- **Documentation**: Check the [complete documentation](../README.md) for additional guidance
- **FAQ**: Review [frequently asked questions](faq.md) for common solutions

### Professional Support

For enterprise deployments or complex issues:
- Include system information and logs
- Describe the exact steps to reproduce the issue
- Specify your configuration and environment details
- Mention any recent changes or updates
