# Quick Start Guide - Agent Zero v2.0

Get up and running with Agent Zero v2.0 in under 10 minutes! This guide covers the essential steps to start using Agent Zero with its enhanced memory systems.

## Prerequisites

- Docker Desktop installed and running
- Web browser (Chrome, Firefox, Safari, or Edge)
- Internet connection for model downloads

## 5-Minute Setup

### Step 1: Launch Agent Zero (2 minutes)

```bash
# Pull and run Agent Zero v2.0
docker run -d \
  --name agent-zero-v2 \
  -p 8080:80 \
  -v ~/agent-zero-data:/a0 \
  frdel/agent-zero-run:latest
```

### Step 2: Access Web Interface (1 minute)

1. Open your browser to `http://localhost:8080`
2. Wait for the initialization to complete (30-60 seconds)
3. You'll see the Agent Zero welcome screen

### Step 3: Basic Configuration (2 minutes)

1. **Click the Settings gear icon** in the sidebar
2. **Set up your first LLM**:
   - For OpenAI: Add your API key and select `gpt-4` or `gpt-3.5-turbo`
   - For local models: Install Ollama and select a model like `llama3.2`
3. **Configure memory backend**: Leave as "Auto" for automatic selection
4. **Click Save** to apply settings

## First Conversation

### Test Basic Functionality

Start with a simple question to verify everything works:

```
Hello! Can you tell me about yourself and what you can do?
```

Agent Zero should respond with information about its capabilities and current configuration.

### Test Memory System

Ask Agent Zero to remember something:

```
Please remember that my favorite programming language is Python and I'm working on a web application project.
```

Then test memory retrieval:

```
What do you remember about my programming preferences?
```

### Test Tool Usage

Try a task that requires tools:

```
Can you search for the latest news about artificial intelligence and summarize the top 3 stories?
```

This will test web search capabilities and information processing.

## Understanding the Interface

### Main Chat Area
- **Message Input**: Type your messages at the bottom
- **Chat History**: Scrollable conversation history
- **Agent Responses**: Detailed responses with reasoning and tool usage

### Sidebar Features
- **Settings**: Configure models, API keys, and memory backends
- **File Browser**: Upload and manage files
- **Memory Browser**: View and search stored memories
- **Chat Management**: Create new chats, view history

### Action Buttons
- **Restart**: Restart the agent framework
- **Stop**: Stop current agent execution
- **Attachments**: Upload files for analysis

## Memory System Quick Tour

### Automatic Memory
Agent Zero automatically saves important information from conversations:

```
I'm planning a trip to Japan next month. I'll be visiting Tokyo and Kyoto.
```

### Manual Memory
You can explicitly ask Agent Zero to remember things:

```
Please remember that I prefer vegetarian restaurants and I'm allergic to shellfish.
```

### Memory Retrieval
Agent Zero automatically recalls relevant memories during conversations:

```
Can you recommend some restaurants for my Japan trip?
```

## Common First Tasks

### 1. Knowledge Import
Upload documents for Agent Zero to learn from:

1. Click the **File Browser** in the sidebar
2. Upload PDF, TXT, or MD files
3. Agent Zero will automatically process and index them

### 2. Code Analysis
Ask Agent Zero to analyze code:

```
Can you review this Python function and suggest improvements?

def calculate_total(items):
    total = 0
    for item in items:
        total = total + item['price']
    return total
```

### 3. Research Tasks
Use Agent Zero for research:

```
Research the current state of quantum computing and create a summary with key developments in 2024.
```

### 4. Multi-Step Projects
Give Agent Zero complex tasks:

```
Help me plan a personal budget tracker application. Break down the requirements, suggest a tech stack, and create a basic project structure.
```

## Memory Backend Selection

### Auto Mode (Default)
- Automatically selects the best available backend
- Falls back gracefully if advanced features aren't available
- Recommended for most users

### FAISS Backend
- Fast vector-based similarity search
- Good for general conversation memory
- No additional setup required

### Graphiti Backend
- Advanced temporal knowledge graph
- Excellent for complex relationships and time-based queries
- Requires Neo4j database setup

To switch backends:
1. Go to Settings
2. Find "Memory Backend" section
3. Select your preferred option
4. Restart Agent Zero

## Next Steps

### Explore Advanced Features
- **[Multi-Agent Cooperation](../advanced-features/multi-agent.md)**: Learn about agent delegation
- **[Custom Tools](../developer-guide/custom-tools.md)**: Create your own tools and instruments
- **[Voice Interface](../advanced-features/voice-interface.md)**: Use speech-to-text features

### Optimize Performance
- **[Memory Configuration](../memory-systems/configuration.md)**: Fine-tune memory settings
- **[Model Selection](configuration.md)**: Choose optimal models for your use case
- **[Docker Optimization](../advanced-features/docker-setup.md)**: Optimize Docker configuration

### Join the Community
- **[Discord](https://discord.gg/Z2tun2N3)**: Join discussions and get help
- **[GitHub](https://github.com/frdel/agent-zero)**: Report issues and contribute
- **[Show and Tell](https://github.com/frdel/agent-zero/discussions/categories/show-and-tell)**: Share your projects

## Troubleshooting Quick Fixes

### Agent Zero Won't Start
```bash
# Check Docker status
docker ps

# Restart container
docker restart agent-zero-v2

# Check logs
docker logs agent-zero-v2
```

### Memory Issues
1. Check available disk space
2. Verify API keys are correct
3. Try switching memory backend in settings

### Performance Issues
1. Allocate more memory to Docker
2. Use smaller/faster models
3. Clear old chat history

### Connection Issues
1. Verify port 8080 isn't blocked
2. Check firewall settings
3. Try a different port: `-p 8081:80`

## Getting Help

- **Documentation**: Browse the complete [documentation](../README.md)
- **Troubleshooting**: See the [troubleshooting guide](../developer-guide/troubleshooting.md)
- **Community**: Join our [Discord server](https://discord.gg/Z2tun2N3)
- **Issues**: Report bugs on [GitHub](https://github.com/frdel/agent-zero/issues)

---

**Ready for more?** Continue with the [Configuration Guide](configuration.md) to customize Agent Zero for your specific needs.
