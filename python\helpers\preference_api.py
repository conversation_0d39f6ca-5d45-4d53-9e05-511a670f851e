"""
User Preference Management API
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio
from enum import Enum
from dataclasses import dataclass, field, asdict

from .user_preferences import UserPreferenceManager, PreferenceType, LearningSignal, UserFeedback # Added UserFeedback
from .behavioral_analyzer import BehavioralPatternAnalyzer, UsageMetrics, BehaviorPattern # Added UsageMetrics, BehaviorPattern
from .preference_adaptation import PreferenceAdaptationEngine

class PreferenceAPI:
    """
    API for managing user preferences and behavioral insights
    """

    def __init__(self):
        self.preference_managers: Dict[str, UserPreferenceManager] = {}
        self.adaptation_engines: Dict[str, PreferenceAdaptationEngine] = {}

    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get all preferences for a user"""
        manager = await self._get_preference_manager(user_id)
        preferences = {}
        for key, pref_item in manager.preferences.items():
            preferences[key] = {
                'value': pref_item.value,
                'type': pref_item.preference_type.value if isinstance(pref_item.preference_type, Enum) else str(pref_item.preference_type),
                'confidence': pref_item.confidence,
                'last_updated': pref_item.last_updated.isoformat(),
                'user_explicit': pref_item.user_explicit,
                'learning_history_count': len(pref_item.learning_history)
            }
        return preferences

    async def set_user_preference(
        self,
        user_id: str,
        preference_key: str,
        value: Any,
        preference_type: str
    ) -> Dict[str, Any]:
        """Set a user preference"""
        manager = await self._get_preference_manager(user_id)
        try:
            # Ensure preference_type is valid, convert string to Enum if necessary
            if isinstance(preference_type, str):
                try:
                    pref_type_enum = PreferenceType[preference_type.upper()]
                except KeyError:
                    # If direct mapping fails, try finding by value
                    found = False
                    # Convert preference_type to lower for case-insensitive comparison if it's a string
                    compare_pref_type = preference_type.lower() if isinstance(preference_type, str) else preference_type
                    for pt in PreferenceType:
                        if pt.value == compare_pref_type:
                            pref_type_enum = pt
                            found = True
                            break
                    if not found:
                        raise ValueError(f"Invalid preference type string: {preference_type}")
            elif isinstance(preference_type, PreferenceType):
                pref_type_enum = preference_type
            else:
                raise ValueError("preference_type must be a string or PreferenceType enum member.")

            success = await manager.set_preference(
                preference_key, value, pref_type_enum, user_explicit=True
            )
            return {
                'success': success,
                'preference_key': preference_key,
                'new_value': value,
                'timestamp': datetime.now().isoformat()
            }
        except ValueError as e:
            return {
                'success': False,
                'error': str(e),
                'valid_types': [t.value for t in PreferenceType]
            }

    async def get_user_behavioral_insights(self, user_id: str) -> Dict[str, Any]:
        """Get behavioral insights for a user"""
        manager = await self._get_preference_manager(user_id) # To ensure manager exists for user
        # In the doc, analyzer is created on the fly.
        # If BehavioralPatternAnalyzer is meant to be persistent per user like UserPreferenceManager,
        # it should be managed similarly in self.behavioral_analyzers dict.
        # For now, following the doc's approach of fresh instantiation.
        analyzer = BehavioralPatternAnalyzer(user_id)
        report = await analyzer.export_analysis_report()
        return {
            'user_id': user_id,
            'insights': report, # report is already a dict
            'preference_count': len(manager.preferences),
            'feedback_history_count': len(manager.feedback_history), # Assuming feedback_history is a list
            'last_analysis': datetime.now().isoformat()
        }

    async def trigger_preference_adaptation(self, user_id: str) -> Dict[str, Any]:
        """Manually trigger preference adaptation for a user"""
        adaptation_engine = await self._get_adaptation_engine(user_id)
        adaptation_session = await adaptation_engine.analyze_and_adapt()
        return {
            'user_id': user_id,
            'adaptation_session': adaptation_session,
            'timestamp': datetime.now().isoformat()
        }

    async def record_user_feedback(
        self,
        user_id: str,
        action: str,
        context: Dict[str, Any],
        feedback_type: str, # Changed to str to match doc
        value: Any = None
    ) -> Dict[str, Any]:
        """Record user feedback"""
        manager = await self._get_preference_manager(user_id)
        try:
            # Convert feedback_type string to LearningSignal Enum
            if isinstance(feedback_type, str):
                try:
                    signal_enum = LearningSignal[feedback_type.upper()]
                except KeyError:
                     # If direct mapping fails, try finding by value
                    found = False
                    # Convert feedback_type to lower for case-insensitive comparison if it's a string
                    compare_feedback_type = feedback_type.lower() if isinstance(feedback_type, str) else feedback_type
                    for ls_val in LearningSignal:
                        if ls_val.value == compare_feedback_type:
                            signal_enum = ls_val
                            found = True
                            break
                    if not found:
                        raise ValueError(f"Invalid feedback type string: {feedback_type}")
            elif isinstance(feedback_type, LearningSignal):
                signal_enum = feedback_type
            else:
                raise ValueError("feedback_type must be a string or LearningSignal enum member.")

            await manager.record_feedback(action, context, signal_enum, value)
            return {
                'success': True,
                'user_id': user_id,
                'action': action,
                'feedback_type': feedback_type, # Return original string
                'timestamp': datetime.now().isoformat()
            }
        except ValueError as e:
            return {
                'success': False,
                'error': str(e),
                'valid_types': [s.value for s in LearningSignal]
            }

    async def get_preference_recommendations(self, user_id: str) -> Dict[str, Any]:
        """Get preference recommendations based on behavioral analysis"""
        adaptation_engine = await self._get_adaptation_engine(user_id)
        # The doc implies recommendations come from the analyzer linked to the adaptation_engine
        recommendations = await adaptation_engine.behavioral_analyzer.get_pattern_recommendations()
        return {
            'user_id': user_id,
            'recommendations': recommendations, # recommendations is already a list of dicts
            'recommendation_count': len(recommendations),
            'timestamp': datetime.now().isoformat()
        }

    async def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """Export all user preference and behavioral data"""
        manager = await self._get_preference_manager(user_id)
        # Behavioral insights are fetched fresh, not from a persistent analyzer object here.
        insights_report = await self.get_user_behavioral_insights(user_id) # This calls analyzer internally

        return {
            'user_id': user_id,
            'export_timestamp': datetime.now().isoformat(),
            'preferences': await self.get_user_preferences(user_id), # Already formatted
            'behavioral_insights': insights_report, # Already formatted from get_user_behavioral_insights
            'feedback_history': [
                {
                    'action': fb.action,
                    'context': fb.context,
                    'feedback_type': fb.feedback_type.value if isinstance(fb.feedback_type, Enum) else str(fb.feedback_type),
                    'timestamp': fb.timestamp.isoformat(),
                    'value': fb.value
                }
                for fb in manager.feedback_history # Assuming feedback_history is a list of UserFeedback objects
            ]
        }

    async def reset_user_preferences(self, user_id: str, keep_explicit: bool = True) -> Dict[str, Any]:
        """Reset user preferences to defaults"""
        manager = await self._get_preference_manager(user_id)
        explicit_prefs_to_restore = {}
        if keep_explicit:
            for key, pref_item in manager.preferences.items():
                if pref_item.user_explicit:
                    explicit_prefs_to_restore[key] = pref_item

        # Reinitialize preferences (clears existing and sets defaults)
        manager._initialize_default_preferences() # This should clear and set defaults

        # Restore explicit preferences if they were backed up
        if keep_explicit:
            for key, pref_item in explicit_prefs_to_restore.items():
                manager.preferences[key] = pref_item # Directly assign the PreferenceItem object

        return {
            'success': True,
            'user_id': user_id,
            'reset_timestamp': datetime.now().isoformat(),
            'explicit_preferences_kept': len(explicit_prefs_to_restore) if keep_explicit else 0,
            'total_preferences': len(manager.preferences)
        }

    async def _get_preference_manager(self, user_id: str) -> UserPreferenceManager:
        """Get or create preference manager for user"""
        if user_id not in self.preference_managers:
            self.preference_managers[user_id] = UserPreferenceManager(user_id)
            # Potentially, load preferences from persistence here if UserPreferenceManager supports it
            # await self.preference_managers[user_id].load_preferences()
        return self.preference_managers[user_id]

    async def _get_adaptation_engine(self, user_id: str) -> PreferenceAdaptationEngine:
        """Get or create adaptation engine for user"""
        if user_id not in self.adaptation_engines:
            manager = await self._get_preference_manager(user_id)
            # Pass the specific manager instance to the engine
            self.adaptation_engines[user_id] = PreferenceAdaptationEngine(manager)
        return self.adaptation_engines[user_id]

# Global API instance (as per the document)
preference_api = PreferenceAPI()

# Example main for testing if this file is run directly (optional)
async def main_test():
    print("Testing PreferenceAPI...")
    test_user_id = "test_user_api"

    # Get initial preferences
    print("\n--- Get User Preferences (Initial) ---")
    prefs = await preference_api.get_user_preferences(test_user_id)
    print(f"Initial preferences for {test_user_id}: {prefs}")
    initial_pref_count = len(prefs)

    # Set a preference
    print("\n--- Set User Preference ---")
    set_result = await preference_api.set_user_preference(
        test_user_id,
        "theme",
        "dark",
        "GENERAL" # Using string value that should map to PreferenceType.GENERAL
    )
    print(f"Set preference result: {set_result}")
    assert set_result['success']

    set_result_domain = await preference_api.set_user_preference(
        test_user_id,
        "domain_weight_tech",
        0.85,
        PreferenceType.DOMAIN_WEIGHT.value # Using Enum value
    )
    print(f"Set domain weight result: {set_result_domain}")
    assert set_result_domain['success']


    # Get preferences again
    print("\n--- Get User Preferences (After Set) ---")
    prefs_after_set = await preference_api.get_user_preferences(test_user_id)
    print(f"Preferences for {test_user_id} after set: {prefs_after_set}")
    assert len(prefs_after_set) == initial_pref_count + 2
    assert prefs_after_set['theme']['value'] == 'dark'
    assert prefs_after_set['domain_weight_tech']['value'] == 0.85


    # Record feedback
    print("\n--- Record User Feedback ---")
    feedback_result = await preference_api.record_user_feedback(
        test_user_id,
        "article_like",
        {"article_id": "123", "tags": ["python", "ai"]},
        LearningSignal.POSITIVE.value, # Using Enum value
        value=True
    )
    print(f"Record feedback result: {feedback_result}")
    assert feedback_result['success']

    # Get behavioral insights
    print("\n--- Get Behavioral Insights ---")
    insights = await preference_api.get_user_behavioral_insights(test_user_id)
    print(f"Behavioral insights for {test_user_id}: {insights}")
    assert insights['user_id'] == test_user_id
    assert 'insights' in insights
    assert len(insights['insights']['detected_patterns']) > 0 # Based on placeholder

    # Get recommendations
    print("\n--- Get Preference Recommendations ---")
    recommendations = await preference_api.get_preference_recommendations(test_user_id)
    print(f"Recommendations for {test_user_id}: {recommendations}")
    assert recommendations['recommendation_count'] > 0 # Based on placeholder

    # Trigger adaptation
    print("\n--- Trigger Preference Adaptation ---")
    adaptation_result = await preference_api.trigger_preference_adaptation(test_user_id)
    print(f"Adaptation result for {test_user_id}: {adaptation_result}")
    assert 'adaptation_session' in adaptation_result

    # Export user data
    print("\n--- Export User Data ---")
    export_data = await preference_api.export_user_data(test_user_id)
    print(f"Export data for {test_user_id}: {export_data}")
    assert export_data['user_id'] == test_user_id
    assert 'preferences' in export_data
    assert 'behavioral_insights' in export_data
    assert 'feedback_history' in export_data
    assert len(export_data['feedback_history']) == 1

    # Reset preferences (keeping explicit)
    print("\n--- Reset User Preferences (Keep Explicit) ---")
    reset_keep_result = await preference_api.reset_user_preferences(test_user_id, keep_explicit=True)
    print(f"Reset (keep explicit) result: {reset_keep_result}")
    assert reset_keep_result['success']
    assert reset_keep_result['explicit_preferences_kept'] == 2 # theme and domain_weight_tech
    prefs_after_reset_keep = await preference_api.get_user_preferences(test_user_id)
    assert 'theme' in prefs_after_reset_keep
    assert 'domain_weight_tech' in prefs_after_reset_keep

    # Reset preferences (not keeping explicit)
    print("\n--- Reset User Preferences (Discard Explicit) ---")
    reset_discard_result = await preference_api.reset_user_preferences(test_user_id, keep_explicit=False)
    print(f"Reset (discard explicit) result: {reset_discard_result}")
    assert reset_discard_result['success']
    assert reset_discard_result['explicit_preferences_kept'] == 0
    prefs_after_reset_discard = await preference_api.get_user_preferences(test_user_id)
    # Check if it only contains default prefs now
    # This depends on what _initialize_default_preferences actually does in the real UserPreferenceManager
    # For the placeholder, it adds "default_pref"
    assert "default_pref" in prefs_after_reset_discard
    assert 'theme' not in prefs_after_reset_discard
    assert 'domain_weight_tech' not in prefs_after_reset_discard


    print("\nAll PreferenceAPI tests passed (using placeholders where necessary).")

if __name__ == '__main__':
    asyncio.run(main_test())
