# Phase 7: Testing and Validation - Unit Testing
## Comprehensive Unit Test Implementation

This document provides implementation for a comprehensive unit testing suite that validates all components of the intelligent data classification system.

## 🎯 Testing Strategy

### Test Coverage Goals
- **Code Coverage**: >90% line coverage
- **Branch Coverage**: >85% branch coverage
- **Component Coverage**: 100% of public APIs tested
- **Edge Case Coverage**: All error conditions tested

### Test Categories
1. **Unit Tests**: Individual component testing
2. **Integration Tests**: Component interaction testing
3. **Performance Tests**: Response time and throughput validation
4. **End-to-End Tests**: Complete workflow testing

## 🔧 Implementation

### Step 7.1: Create Unit Test Framework

**File:** `tests/test_classification_engine.py`
**Action:** Create new file

```python
"""
Unit tests for the intelligent classification engine
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from python.helpers.intelligent_classification_engine import IntelligentClassificationEngine
from python.helpers.classification_config import ClassificationConfig, ClassificationStrategy
from python.helpers.classification_engine import ClassificationMetrics, ClassificationResult

class TestIntelligentClassificationEngine:
    """Test suite for IntelligentClassificationEngine"""

    @pytest.fixture
    def config(self):
        """Create test configuration"""
        return ClassificationConfig()

    @pytest.fixture
    def engine(self, config):
        """Create test classification engine"""
        return IntelligentClassificationEngine(config)

    @pytest.mark.asyncio
    async def test_engine_initialization(self, engine):
        """Test engine initializes correctly"""
        assert engine is not None
        assert engine.config is not None
        assert engine.tfidf_vectorizer is not None
        assert isinstance(engine.entity_cache, dict)
        assert isinstance(engine.schema_cache, dict)
        assert isinstance(engine.domain_keywords, dict)

    @pytest.mark.asyncio
    async def test_extract_entities_basic(self, engine):
        """Test basic entity extraction"""
        content = "John Smith works at Microsoft Corporation. His <NAME_EMAIL>"

        entities = await engine.extract_entities(content)

        assert len(entities) > 0

        # Check for person entity
        person_entities = [e for e in entities if e['label'] == 'PERSON']
        assert len(person_entities) > 0

        # Check for email entity
        email_entities = [e for e in entities if e['label'] == 'EMAIL']
        assert len(email_entities) > 0
        assert email_entities[0]['text'] == '<EMAIL>'

        # Check for organization entity
        org_entities = [e for e in entities if e['label'] == 'ORGANIZATION']
        assert len(org_entities) > 0

    @pytest.mark.asyncio
    async def test_extract_entities_empty_content(self, engine):
        """Test entity extraction with empty content"""
        entities = await engine.extract_entities("")
        assert entities == []

    @pytest.mark.asyncio
    async def test_extract_entities_domain_specific(self, engine):
        """Test domain-specific entity extraction"""
        content = "Python function class variable API framework"

        entities = await engine.extract_entities(content)

        # Should find programming domain entities
        domain_entities = [e for e in entities if e['label'].startswith('DOMAIN_')]
        assert len(domain_entities) > 0

        programming_entities = [e for e in entities if 'PROGRAMMING' in e['label']]
        assert len(programming_entities) > 0

    @pytest.mark.asyncio
    async def test_calculate_semantic_overlap(self, engine):
        """Test semantic overlap calculation"""
        entities = [
            {'text': 'python', 'label': 'DOMAIN_PROGRAMMING'},
            {'text': 'function', 'label': 'DOMAIN_PROGRAMMING'},
            {'text': 'class', 'label': 'DOMAIN_PROGRAMMING'}
        ]

        existing_schemas = [
            {'entities': ['python', 'programming', 'code']},
            {'entities': ['medical', 'patient', 'diagnosis']}
        ]

        overlap = await engine.calculate_semantic_overlap(entities, existing_schemas)

        assert 0.0 <= overlap <= 1.0
        assert overlap > 0  # Should have some overlap with programming schema

    @pytest.mark.asyncio
    async def test_calculate_semantic_overlap_empty(self, engine):
        """Test semantic overlap with empty inputs"""
        overlap1 = await engine.calculate_semantic_overlap([], [])
        assert overlap1 == 0.0

        overlap2 = await engine.calculate_semantic_overlap([{'text': 'test'}], [])
        assert overlap2 == 0.0

    @pytest.mark.asyncio
    async def test_classify_content_programming(self, engine):
        """Test classification of programming content"""
        content = "def calculate_fibonacci(n): return n if n <= 1 else calculate_fibonacci(n-1) + calculate_fibonacci(n-2)"
        metadata = {'content_type': 'code'}

        result = await engine.classify_content(content, metadata)

        assert isinstance(result, ClassificationResult)
        assert result.strategy in [ClassificationStrategy.SHARED_ONTOLOGY, ClassificationStrategy.CUSTOM_DOMAIN]
        assert 0.0 <= result.confidence <= 1.0
        assert result.processing_time_ms > 0
        assert isinstance(result.metrics, ClassificationMetrics)

    @pytest.mark.asyncio
    async def test_classify_content_conversation(self, engine):
        """Test classification of conversation content"""
        content = "user: Hello, how are you?\nassistant: I'm doing well, thank you for asking!"
        metadata = {'content_type': 'conversation'}

        result = await engine.classify_content(content, metadata)

        assert isinstance(result, ClassificationResult)
        assert result.strategy in [ClassificationStrategy.ISOLATED_NAMESPACE, ClassificationStrategy.CUSTOM_DOMAIN]
        assert 0.0 <= result.confidence <= 1.0

    @pytest.mark.asyncio
    async def test_classify_content_with_privacy_flags(self, engine):
        """Test classification with privacy-sensitive content"""
        content = "My password is secret123 and my SSN is ***********"
        metadata = {'content_type': 'personal'}

        result = await engine.classify_content(content, metadata)

        assert result.strategy == ClassificationStrategy.ISOLATED_NAMESPACE
        assert result.metrics.privacy_flags == True
        assert result.metrics.privacy_score >= engine.config.thresholds.privacy_score_threshold

    @pytest.mark.asyncio
    async def test_classify_content_error_handling(self, engine):
        """Test classification error handling"""
        # Mock an error in entity extraction
        with patch.object(engine, 'extract_entities', side_effect=Exception("Test error")):
            result = await engine.classify_content("test content", {})

            assert result.strategy == ClassificationStrategy.ISOLATED_NAMESPACE
            assert result.confidence == 0.5
            assert result.metrics.privacy_flags == True
            assert "Classification failed" in result.reasoning

    @pytest.mark.asyncio
    async def test_performance_tracking(self, engine):
        """Test that performance is tracked correctly"""
        content = "Test content for performance tracking"
        metadata = {'test': True}

        # Clear any existing history
        engine.performance_tracker.metrics_history.clear()

        result = await engine.classify_content(content, metadata)

        # Check that performance was recorded
        assert len(engine.performance_tracker.metrics_history) == 1

        recorded_metric = engine.performance_tracker.metrics_history[0]
        assert recorded_metric['strategy'] == result.strategy.value
        assert recorded_metric['confidence'] == result.confidence
        assert recorded_metric['processing_time_ms'] == result.processing_time_ms

    def test_domain_keywords_initialization(self, engine):
        """Test domain keywords are properly initialized"""
        assert 'programming' in engine.domain_keywords
        assert 'medical' in engine.domain_keywords
        assert 'legal' in engine.domain_keywords
        assert 'finance' in engine.domain_keywords
        assert 'academic' in engine.domain_keywords

        # Check that each domain has keywords
        for domain, keywords in engine.domain_keywords.items():
            assert len(keywords) > 0
            assert all(isinstance(keyword, str) for keyword in keywords)

    @pytest.mark.asyncio
    async def test_entity_confidence_calculation(self, engine):
        """Test entity confidence calculation"""
        entities = [
            {'confidence_score': 0.9},
            {'confidence_score': 0.8},
            {'confidence_score': 0.7}
        ]

        confidence = engine._calculate_entity_confidence(entities)
        expected = (0.9 + 0.8 + 0.7) / 3
        assert abs(confidence - expected) < 0.001

    @pytest.mark.asyncio
    async def test_entity_confidence_empty(self, engine):
        """Test entity confidence with empty entities"""
        confidence = engine._calculate_entity_confidence([])
        assert confidence == 0.0

    @pytest.mark.asyncio
    async def test_domain_specificity_calculation(self, engine):
        """Test domain specificity calculation"""
        content = "Python programming function class variable API framework library"
        entities = [
            {'label': 'DOMAIN_PROGRAMMING', 'text': 'python'},
            {'label': 'DOMAIN_PROGRAMMING', 'text': 'function'}
        ]

        specificity, domain = engine._calculate_domain_specificity(content, entities)

        assert 0.0 <= specificity <= 1.0
        assert domain == 'programming'

    @pytest.mark.asyncio
    async def test_privacy_detection(self, engine):
        """Test privacy flag detection"""
        content = "This is confidential information with password secret123"
        entities = [
            {'label': 'EMAIL', 'text': '<EMAIL>'},
            {'label': 'PERSON', 'text': 'John Doe'}
        ]

        privacy_score, privacy_flags = engine._detect_privacy_flags(content, entities)

        assert privacy_score > 0
        assert privacy_flags == True

    @pytest.mark.asyncio
    async def test_temporal_relevance_calculation(self, engine):
        """Test temporal relevance calculation"""
        # Test with current timestamp
        current_relevance = engine._calculate_temporal_relevance(datetime.now().isoformat())
        assert current_relevance == 1.0

        # Test with old timestamp
        old_timestamp = (datetime.now() - timedelta(days=30)).isoformat()
        old_relevance = engine._calculate_temporal_relevance(old_timestamp)
        assert 0.0 < old_relevance < 1.0

        # Test with no timestamp
        no_timestamp_relevance = engine._calculate_temporal_relevance(None)
        assert no_timestamp_relevance == 1.0

    @pytest.mark.asyncio
    async def test_decision_matrix_shared_ontology(self, engine):
        """Test decision matrix for shared ontology classification"""
        metrics = ClassificationMetrics(
            semantic_overlap=0.8,  # High overlap
            entity_confidence=0.9,  # High confidence
            domain_specificity=0.3,  # Low specificity
            relationship_density=0.7,
            privacy_score=1,  # Low privacy score
            interconnectedness=0.8,
            temporal_relevance=0.9,
            related_entities_count=10,
            privacy_flags=False,
            user_specified_isolation=False
        )

        strategy, confidence, reasoning = engine._apply_decision_matrix(metrics)

        assert strategy == ClassificationStrategy.SHARED_ONTOLOGY
        assert confidence > 0.7
        assert "High semantic overlap" in reasoning

    @pytest.mark.asyncio
    async def test_decision_matrix_isolated_namespace(self, engine):
        """Test decision matrix for isolated namespace classification"""
        metrics = ClassificationMetrics(
            semantic_overlap=0.2,
            entity_confidence=0.8,
            domain_specificity=0.5,
            relationship_density=0.3,
            privacy_score=5,  # High privacy score
            interconnectedness=0.2,
            temporal_relevance=0.9,
            related_entities_count=3,
            privacy_flags=True,  # Privacy flags set
            user_specified_isolation=False
        )

        strategy, confidence, reasoning = engine._apply_decision_matrix(metrics)

        assert strategy == ClassificationStrategy.ISOLATED_NAMESPACE
        assert "privacy concerns" in reasoning.lower()

    @pytest.mark.asyncio
    async def test_decision_matrix_custom_domain(self, engine):
        """Test decision matrix for custom domain classification"""
        metrics = ClassificationMetrics(
            semantic_overlap=0.2,  # Low overlap
            entity_confidence=0.8,  # Good confidence
            domain_specificity=0.7,  # High specificity
            relationship_density=0.6,
            privacy_score=1,  # Low privacy score
            interconnectedness=0.5,
            temporal_relevance=0.9,
            related_entities_count=8,  # Sufficient entities
            privacy_flags=False,
            user_specified_isolation=False
        )

        strategy, confidence, reasoning = engine._apply_decision_matrix(metrics)

        assert strategy == ClassificationStrategy.CUSTOM_DOMAIN
        assert "Domain-specific content" in reasoning
```

**Validation:**
```python
# Run unit tests for classification engine
pytest tests/test_classification_engine.py -v --cov=python.helpers.intelligent_classification_engine --cov-report=html
```

### Step 7.2: Create Memory System Unit Tests

**File:** `tests/test_memory_tiers.py`
**Action:** Create new file

```python
"""
Unit tests for the hierarchical memory system
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from python.helpers.memory_tiers import (
    WorkingMemoryTier, LongTermMemoryTier, EpisodicMemoryTier,
    MemoryItem, MemoryQuery, MemoryTier
)

class TestWorkingMemoryTier:
    """Test suite for WorkingMemoryTier"""

    @pytest.fixture
    def working_memory(self):
        """Create test working memory tier"""
        return WorkingMemoryTier()

    @pytest.fixture
    def sample_item(self):
        """Create sample memory item"""
        return MemoryItem(
            id="test_001",
            content="Python programming tutorial",
            metadata={"type": "document", "author": "test"},
            tier=MemoryTier.WORKING,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.8
        )

    @pytest.mark.asyncio
    async def test_store_item(self, working_memory, sample_item):
        """Test storing item in working memory"""
        item_id = await working_memory.store(sample_item)

        assert item_id == sample_item.id
        assert sample_item.id in working_memory.items
        assert working_memory.items[sample_item.id] == sample_item

    @pytest.mark.asyncio
    async def test_retrieve_item(self, working_memory, sample_item):
        """Test retrieving item from working memory"""
        await working_memory.store(sample_item)

        query = MemoryQuery(
            query_text="Python programming",
            limit=5
        )

        results = await working_memory.retrieve(query)

        assert len(results) > 0
        assert sample_item in results

    @pytest.mark.asyncio
    async def test_retrieve_empty_query(self, working_memory):
        """Test retrieving with empty query"""
        query = MemoryQuery(query_text="", limit=5)
        results = await working_memory.retrieve(query)
        assert results == []

    @pytest.mark.asyncio
    async def test_update_access(self, working_memory, sample_item):
        """Test updating access statistics"""
        await working_memory.store(sample_item)
        original_count = sample_item.access_count
        original_time = sample_item.last_accessed

        await asyncio.sleep(0.01)  # Small delay
        await working_memory.update_access(sample_item.id)

        assert sample_item.access_count == original_count + 1
        assert sample_item.last_accessed > original_time

    @pytest.mark.asyncio
    async def test_capacity_management(self, working_memory):
        """Test that working memory respects capacity limits"""
        # Set small capacity for testing
        working_memory.max_items = 3

        # Add items beyond capacity
        items = []
        for i in range(5):
            item = MemoryItem(
                id=f"test_{i:03d}",
                content=f"Test content {i}",
                metadata={},
                tier=MemoryTier.WORKING,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                importance_score=0.5
            )
            items.append(item)
            await working_memory.store(item)

        # Should only have max_items in memory
        assert len(working_memory.items) <= working_memory.max_items

    @pytest.mark.asyncio
    async def test_cleanup_expired(self, working_memory):
        """Test cleanup of expired items"""
        # Create old item
        old_item = MemoryItem(
            id="old_001",
            content="Old content",
            metadata={},
            tier=MemoryTier.WORKING,
            created_at=datetime.now() - timedelta(days=10),
            last_accessed=datetime.now() - timedelta(days=10),
            access_count=1,
            importance_score=0.5
        )

        # Create recent item
        recent_item = MemoryItem(
            id="recent_001",
            content="Recent content",
            metadata={},
            tier=MemoryTier.WORKING,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.5
        )

        await working_memory.store(old_item)
        await working_memory.store(recent_item)

        # Cleanup expired items
        removed_count = await working_memory.cleanup_expired()

        assert removed_count > 0
        assert old_item.id not in working_memory.items
        assert recent_item.id in working_memory.items

    @pytest.mark.asyncio
    async def test_get_tier_stats(self, working_memory, sample_item):
        """Test getting tier statistics"""
        await working_memory.store(sample_item)

        stats = await working_memory.get_tier_stats()

        assert stats['tier'] == 'working'
        assert stats['item_count'] == 1
        assert 'avg_importance' in stats
        assert 'oldest_item_age_hours' in stats
        assert 'capacity_utilization' in stats

    @pytest.mark.asyncio
    async def test_time_range_filtering(self, working_memory):
        """Test time range filtering in queries"""
        # Create items with different timestamps
        old_item = MemoryItem(
            id="old_001",
            content="Old content",
            metadata={},
            tier=MemoryTier.WORKING,
            created_at=datetime.now() - timedelta(days=2),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.8
        )

        recent_item = MemoryItem(
            id="recent_001",
            content="Recent content",
            metadata={},
            tier=MemoryTier.WORKING,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.8
        )

        await working_memory.store(old_item)
        await working_memory.store(recent_item)

        # Query with time range for recent items only
        query = MemoryQuery(
            query_text="content",
            time_range=(datetime.now() - timedelta(hours=1), datetime.now()),
            limit=10
        )

        results = await working_memory.retrieve(query)

        # Should only get recent item
        assert len(results) == 1
        assert results[0].id == recent_item.id

class TestLongTermMemoryTier:
    """Test suite for LongTermMemoryTier"""

    @pytest.fixture
    def long_term_memory(self):
        """Create test long-term memory tier"""
        return LongTermMemoryTier()

    @pytest.fixture
    def sample_item(self):
        """Create sample memory item"""
        return MemoryItem(
            id="lt_test_001",
            content="Machine learning algorithms and neural networks",
            metadata={"type": "knowledge", "domain": "ai"},
            tier=MemoryTier.LONG_TERM,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=5,
            importance_score=0.9
        )

    @pytest.mark.asyncio
    async def test_store_and_cluster(self, long_term_memory, sample_item):
        """Test storing item and cluster creation"""
        item_id = await long_term_memory.store(sample_item)

        assert item_id == sample_item.id
        assert sample_item.id in long_term_memory.item_to_cluster

        cluster_id = long_term_memory.item_to_cluster[sample_item.id]
        assert cluster_id in long_term_memory.clusters
        assert sample_item.id in long_term_memory.clusters[cluster_id]['items']

    @pytest.mark.asyncio
    async def test_semantic_clustering(self, long_term_memory):
        """Test that similar items are clustered together"""
        # Create related items
        item1 = MemoryItem(
            id="ml_001",
            content="Machine learning neural networks",
            metadata={},
            tier=MemoryTier.LONG_TERM,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.8
        )

        item2 = MemoryItem(
            id="ml_002",
            content="Deep learning neural networks",
            metadata={},
            tier=MemoryTier.LONG_TERM,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.8
        )

        await long_term_memory.store(item1)
        await long_term_memory.store(item2)

        # Items should be in the same cluster due to similarity
        cluster1 = long_term_memory.item_to_cluster[item1.id]
        cluster2 = long_term_memory.item_to_cluster[item2.id]

        # They might be in the same cluster if similarity is high enough
        # This test checks that clustering logic is working
        assert cluster1 in long_term_memory.clusters
        assert cluster2 in long_term_memory.clusters

    @pytest.mark.asyncio
    async def test_retrieve_by_semantic_similarity(self, long_term_memory, sample_item):
        """Test retrieval by semantic similarity"""
        await long_term_memory.store(sample_item)

        query = MemoryQuery(
            query_text="artificial intelligence machine learning",
            limit=5
        )

        results = await long_term_memory.retrieve(query)

        # Should find the related item
        assert len(results) > 0
        assert any(item.id == sample_item.id for item in results)

class TestEpisodicMemoryTier:
    """Test suite for EpisodicMemoryTier"""

    @pytest.fixture
    def episodic_memory(self):
        """Create test episodic memory tier"""
        return EpisodicMemoryTier()

    @pytest.fixture
    def sample_episode(self):
        """Create sample episodic memory item"""
        return MemoryItem(
            id="ep_test_001",
            content="Meeting with client about project requirements",
            metadata={"type": "event", "location": "office", "participants": ["client", "team"]},
            tier=MemoryTier.EPISODIC,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.7
        )

    @pytest.mark.asyncio
    async def test_store_episode(self, episodic_memory, sample_episode):
        """Test storing episodic memory"""
        episode_id = await episodic_memory.store(sample_episode)

        assert episode_id == sample_episode.id
        assert sample_episode.id in episodic_memory.episodes

        # Check temporal indexing
        date_key = sample_episode.created_at.strftime('%Y-%m-%d')
        assert date_key in episodic_memory.temporal_index
        assert sample_episode.id in episodic_memory.temporal_index[date_key]

    @pytest.mark.asyncio
    async def test_temporal_retrieval(self, episodic_memory, sample_episode):
        """Test retrieval by time range"""
        await episodic_memory.store(sample_episode)

        # Query for today's episodes
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)

        query = MemoryQuery(
            query_text="meeting",
            time_range=(today_start, today_end),
            limit=10
        )

        results = await episodic_memory.retrieve(query)

        assert len(results) > 0
        assert sample_episode in results

    @pytest.mark.asyncio
    async def test_location_indexing(self, episodic_memory, sample_episode):
        """Test location-based indexing"""
        await episodic_memory.store(sample_episode)

        location = sample_episode.metadata['location']
        assert location in episodic_memory.location_index
        assert sample_episode.id in episodic_memory.location_index[location]

    @pytest.mark.asyncio
    async def test_event_chain_linking(self, episodic_memory):
        """Test event chain creation"""
        # Create related episodes
        episode1 = MemoryItem(
            id="chain_001",
            content="Project planning meeting",
            metadata={"type": "meeting"},
            tier=MemoryTier.EPISODIC,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.8
        )

        episode2 = MemoryItem(
            id="chain_002",
            content="Project review meeting",
            metadata={"type": "meeting"},
            tier=MemoryTier.EPISODIC,
            created_at=datetime.now() + timedelta(hours=2),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=0.8
        )

        await episodic_memory.store(episode1)
        await episodic_memory.store(episode2)

        # Check if event chains were created
        # (Implementation may create chains based on similarity and temporal proximity)
        assert len(episodic_memory.event_chains) >= 0  # May or may not create chains
```

---

**Next Step**: [Integration Testing](integration-testing.md)