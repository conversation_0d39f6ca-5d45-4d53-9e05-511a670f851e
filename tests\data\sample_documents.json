[{"id": "doc1", "text": "The quick brown fox jumps over the lazy dog. This is a classic sentence used for testing typewriters and fonts. It contains all letters of the English alphabet.", "metadata": {"source": "classic_pangram", "category": "typography", "timestamp": "2023-01-15T10:30:00Z", "tags": ["english", "alphabet", "testing"]}}, {"id": "doc2", "text": "Artificial intelligence (AI) is rapidly transforming various industries, from healthcare to finance. Machine learning, a subset of AI, focuses on developing algorithms that allow computers to learn from data.", "metadata": {"source": "tech_review_article", "category": "technology", "timestamp": "2023-03-22T14:45:10Z", "tags": ["AI", "machine learning", "tech trends"]}}, {"id": "doc3", "text": "The history of space exploration is marked by significant milestones, including the first human in space and the moon landing. Future missions aim to explore Mars and beyond.", "metadata": {"source": "educational_website", "category": "science", "timestamp": "2023-05-10T08:00:00Z", "tags": ["space", "history", "astronauts", "mars"]}}, {"id": "doc4", "text": "Climate change is a pressing global issue, with rising temperatures and extreme weather events becoming more frequent. International cooperation is crucial to address its impacts.", "metadata": {"source": "environmental_report", "category": "environment", "timestamp": "2023-04-05T11:20:30Z", "tags": ["climate change", "global warming", "sustainability"]}}, {"id": "doc5", "text": "Python is a versatile and widely-used programming language, known for its readability and extensive libraries. It's popular in web development, data science, and artificial intelligence.", "metadata": {"source": "programming_docs", "category": "software_development", "timestamp": "2023-02-28T16:00:00Z", "tags": ["python", "programming", "data science", "webdev"]}}]