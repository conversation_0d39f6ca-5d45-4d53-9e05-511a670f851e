# scripts/validate_env.py
"""
Validates the environment variables required for the MemoryV2 project.

This script checks for the presence and basic validity of essential environment
variables defined in the .env file. It's designed to be run as a preliminary
check to ensure the application has the necessary configuration to start.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
# This is particularly useful for development environments.
# In production, environment variables are typically set directly by the system.
load_dotenv()

# Define a list of essential environment variables and their validation rules.
# Rules can be simple presence checks or more complex pattern matching (e.g., for API keys).
# For this example, we'll primarily check for presence.
REQUIRED_ENV_VARS = {
    "LOG_LEVEL": lambda v: v in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
    "OPENAI_API_KEY": lambda v: v is not None and len(v) > 10, # Basic check for non-empty string
    # Pinecone variables are optional if another vector store is used.
    # However, if one is present, the others should be too.
    # This logic can be enhanced in a more sophisticated validation function.
    # "PINECONE_API_KEY": lambda v: v is not None and len(v) > 10 if os.getenv("PINECONE_ENVIRONMENT") else True,
    # "PINECONE_ENVIRONMENT": lambda v: v is not None and len(v) > 0 if os.getenv("PINECONE_API_KEY") else True,
    "PINECONE_INDEX_NAME": lambda v: v is not None and len(v) > 0,
}

# Optional environment variables - presence is not strictly required.
# Example: CHROMA_DB_PATH might only be needed if ChromaDB is the chosen vector store.
OPTIONAL_ENV_VARS = [
    "CHROMA_DB_PATH",
    "CHROMA_COLLECTION_NAME",
    "DEBUG_MODE"
]

def validate_environment():
    """
    Performs the validation of environment variables.

    Returns:
        bool: True if all essential validations pass, False otherwise.
        list: A list of error messages for any validation failures.
    """
    errors = []
    success = True

    print("Starting environment variable validation...")

    # Check required environment variables
    for var_name, validator in REQUIRED_ENV_VARS.items():
        value = os.getenv(var_name)
        if value is None:
            errors.append(f"Error: Essential environment variable '{var_name}' is not set.")
            success = False
        elif not validator(value):
            errors.append(f"Error: Environment variable '{var_name}' has an invalid value: '{value}'.")
            success = False
        else:
            print(f"  [OK] {var_name} is set and valid.")

    # Check for Pinecone conditional requirements
    pinecone_api_key = os.getenv("PINECONE_API_KEY")
    pinecone_environment = os.getenv("PINECONE_ENVIRONMENT")

    if pinecone_api_key and not pinecone_environment:
        errors.append("Error: PINECONE_API_KEY is set, but PINECONE_ENVIRONMENT is missing.")
        success = False
    elif not pinecone_api_key and pinecone_environment:
        errors.append("Error: PINECONE_ENVIRONMENT is set, but PINECONE_API_KEY is missing.")
        success = False
    elif pinecone_api_key and pinecone_environment:
        if not (len(pinecone_api_key) > 10):
             errors.append(f"Error: Environment variable 'PINECONE_API_KEY' has an invalid value.")
             success = False
        if not (len(pinecone_environment) > 0):
            errors.append(f"Error: Environment variable 'PINECONE_ENVIRONMENT' has an invalid value.")
            success = False
        print("  [OK] Pinecone API Key and Environment are set and appear valid.")


    # Check optional environment variables (currently just logs their presence)
    for var_name in OPTIONAL_ENV_VARS:
        value = os.getenv(var_name)
        if value is not None:
            print(f"  [INFO] Optional variable '{var_name}' is set: '{value}'.")
        else:
            print(f"  [INFO] Optional variable '{var_name}' is not set.")

    if success:
        print("\nEnvironment variable validation successful.")
    else:
        print("\nEnvironment variable validation failed with the following errors:")
        for error in errors:
            print(error)

    return success, errors

if __name__ == "__main__":
    validation_passed, validation_errors = validate_environment()
    if not validation_passed:
        # Exit with a non-zero status code to indicate failure, useful for CI/CD pipelines
        exit(1)
    exit(0)
