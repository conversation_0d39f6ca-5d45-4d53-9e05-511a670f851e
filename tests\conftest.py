# tests/conftest.py
"""
Global pytest fixtures and configuration for the MemoryV2 test suite.

This file is automatically discovered by pytest. Fixtures defined here
can be used by any test in the project without needing to import them.

Common use cases for conftest.py:
- Defining project-wide fixtures (e.g., database connections, test clients).
- Loading test data.
- Configuring pytest plugins.
- Setting up and tearing down global test state.
"""

import pytest
import os
import json
from dotenv import load_dotenv

# Load .env file before any tests run, especially for integration tests
# that might rely on environment variables (e.g., API keys).
# Ensure your .env file is correctly set up for testing (e.g., using test databases or mock APIs).
@pytest.fixture(scope="session", autouse=True)
def load_env_vars():
    """
    Pytest fixture to automatically load environment variables from .env
    at the beginning of the test session.
    """
    # Construct the path to the .env file, assuming it's in the project root
    # This conftest.py is in tests/, so .env is ../.env
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    if os.path.exists(dotenv_path):
        load_dotenv(dotenv_path)
        print(f"Loaded environment variables from: {dotenv_path}")
    else:
        print(f"Warning: .env file not found at {dotenv_path}. Tests might fail if they rely on env vars.")

@pytest.fixture(scope="session")
def project_root_dir():
    """
    Provides the absolute path to the project's root directory.
    Assumes conftest.py is in the 'tests' subdirectory of the project root.
    """
    return os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture(scope="session")
def sample_documents_path(project_root_dir):
    """
    Provides the path to the sample_documents.json file.
    """
    return os.path.join(project_root_dir, "tests", "data", "sample_documents.json")

@pytest.fixture(scope="session")
def sample_documents(sample_documents_path):
    """
    Loads sample documents from the JSON test data file.
    This fixture will load the data once per test session and cache it.
    """
    try:
        with open(sample_documents_path, 'r') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        pytest.fail(f"Test data file not found: {sample_documents_path}. "
                    "Ensure 'tests/data/sample_documents.json' exists.")
    except json.JSONDecodeError:
        pytest.fail(f"Error decoding JSON from file: {sample_documents_path}. "
                    "Ensure it's a valid JSON file.")

# Example of a simple fixture
@pytest.fixture
def example_fixture():
    """A simple example fixture."""
    return "example_value"

# You can add more fixtures here as your project grows.
# For example, a fixture to initialize a test database:
#
# @pytest.fixture(scope="session")
# def db_connection():
#     # Code to set up database connection
#     conn = ...
#     yield conn
#     # Code to tear down database connection
#     conn.close()

# To use a fixture in a test, simply include its name as an argument
# in your test function, e.g.:
#
# def test_something(example_fixture):
#     assert example_fixture == "example_value"

print("tests/conftest.py loaded by pytest.")
