import asyncio
import unittest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timezone, timedelta
import uuid

# Attempt to import the real classes; provide fallbacks if necessary during testing setup
try:
    from python.helpers.hierarchical_memory_manager import HierarchicalMemoryManager
    from python.helpers.memory_tiers import MemoryTier, MemoryItem, MemoryQuery, WorkingMemoryTier, LongTermMemoryTier, EpisodicMemoryTier
    # Try to import the placeholder ClassificationResult from HMM if real one is complex/unavailable
    from python.helpers.hierarchical_memory_manager import ClassificationResult as HMMClassificationResultPlaceholder
    # If the actual ClassificationResult is needed and available:
    # from python.helpers.classification_engine import ClassificationResult
    ClassificationResult = HMMClassificationResultPlaceholder # Use the HMM placeholder for tests
except ImportError as e:
    print(f"Test setup import error: {e}. Using dummy fallbacks for test structure.")
    # Minimal fallbacks to allow test structure to be defined
    class HierarchicalMemoryManager: pass
    class MemoryTier(Enum): WORKING="working"; LONG_TERM="long_term"; EPISODIC="episodic"
    class MemoryItem: pass
    class MemoryQuery: pass
    class WorkingMemoryTier: pass
    class LongTermMemoryTier: pass
    class EpisodicMemoryTier: pass
    class ClassificationResult: pass


# Mock ClassificationResult to be used in tests if the imported one is a placeholder
# This helps ensure the tests have a consistent object to pass to store_memory
class MockClassificationResult(ClassificationResult):
    def __init__(self, strategy=None, confidence=0.5, importance_score=None, is_event=False):
        self.strategy = strategy or MagicMock() # Assign a mock strategy if none provided
        if hasattr(self.strategy, 'value'): # If it's an enum-like mock
            self.strategy.value = "mock_strategy_value"

        self.confidence = confidence
        self.importance_score = importance_score if importance_score is not None else confidence
        self.metadata = {'is_event': is_event} # Simplified metadata for testing tier selection
        self.timestamp = datetime.now(timezone.utc)
        self.classification_id = uuid.uuid4().hex
        # Add other fields as per the actual/placeholder ClassificationResult definition if accessed by HMM
        self.metrics = MagicMock()
        self.reasoning = "Test reasoning"
        self.processing_time_ms = 10.0
        self.validation_warnings = []
        self.status = "COMPLETED"


class TestHierarchicalMemoryManager(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        self.manager = HierarchicalMemoryManager()
        # Mock the individual tiers for most tests to isolate manager logic
        self.manager.working_memory = AsyncMock(spec=WorkingMemoryTier)
        self.manager.long_term_memory = AsyncMock(spec=LongTermMemoryTier)
        self.manager.episodic_memory = AsyncMock(spec=EpisodicMemoryTier)
        await self.manager.initialize()

    async def test_initialize_manager(self):
        self.assertTrue(self.manager.is_initialized)
        self.assertIsNotNone(self.manager.working_memory)
        self.assertIsNotNone(self.manager.long_term_memory)
        self.assertIsNotNone(self.manager.episodic_memory)

    async def test_store_item_to_working_memory_by_metadata(self):
        item_id = "test_wm_item_meta"
        content = "Store this in working memory via metadata"
        metadata = {"id": item_id, "target_tier": "working"}
        mock_cr = MockClassificationResult(confidence=0.9)

        self.manager.working_memory.store.return_value = item_id

        stored_id = await self.manager.store_memory(content, metadata, mock_cr)

        self.assertEqual(stored_id, item_id)
        self.manager.working_memory.store.assert_called_once()
        self.manager.long_term_memory.store.assert_not_called()
        self.manager.episodic_memory.store.assert_not_called()

        # Check the MemoryItem passed to store
        args, _ = self.manager.working_memory.store.call_args
        stored_item: MemoryItem = args[0]
        self.assertEqual(stored_item.id, item_id)
        self.assertEqual(stored_item.content, content)
        self.assertEqual(stored_item.tier, MemoryTier.WORKING)
        self.assertEqual(stored_item.importance_score, 0.9)

    async def test_store_item_to_long_term_memory_default(self):
        item_id = "test_ltm_item"
        content = "Store this in long-term memory (default)"
        metadata = {"id": item_id}
        mock_cr = MockClassificationResult(confidence=0.6)

        self.manager.long_term_memory.store.return_value = item_id

        stored_id = await self.manager.store_memory(content, metadata, mock_cr)

        self.assertEqual(stored_id, item_id)
        self.manager.long_term_memory.store.assert_called_once()
        self.manager.working_memory.store.assert_not_called()
        self.manager.episodic_memory.store.assert_not_called()

        args, _ = self.manager.long_term_memory.store.call_args
        stored_item: MemoryItem = args[0]
        self.assertEqual(stored_item.tier, MemoryTier.LONG_TERM)
        self.assertEqual(stored_item.importance_score, 0.6)

    async def test_store_item_to_episodic_memory_by_event_flag(self):
        item_id = "test_em_item_event"
        content = "This is an event for episodic memory"
        metadata = {"id": item_id, "is_event": True} # Using is_event from HMM logic
        mock_cr = MockClassificationResult(confidence=0.5)

        self.manager.episodic_memory.store.return_value = item_id

        stored_id = await self.manager.store_memory(content, metadata, mock_cr)

        self.assertEqual(stored_id, item_id)
        self.manager.episodic_memory.store.assert_called_once()
        self.manager.working_memory.store.assert_not_called()
        self.manager.long_term_memory.store.assert_not_called()

        args, _ = self.manager.episodic_memory.store.call_args
        stored_item: MemoryItem = args[0]
        self.assertEqual(stored_item.tier, MemoryTier.EPISODIC)

    def _create_mock_item(self, id, content, tier, importance):
        return MemoryItem(
            id=id, content=content, metadata={}, tier=tier,
            created_at=datetime.now(timezone.utc), last_accessed=datetime.now(timezone.utc),
            access_count=1, importance_score=importance
        )

    async def test_retrieve_hierarchical(self):
        query = MemoryQuery(query_text="test query", limit=3)

        wm_item = self._create_mock_item("wm1", "Working memory item", MemoryTier.WORKING, 0.9)
        ltm_item = self._create_mock_item("ltm1", "Long-term memory item", MemoryTier.LONG_TERM, 0.8)
        em_item = self._create_mock_item("em1", "Episodic memory item", MemoryTier.EPISODIC, 0.7)

        self.manager.working_memory.retrieve.return_value = [wm_item]
        self.manager.long_term_memory.retrieve.return_value = [ltm_item]
        self.manager.episodic_memory.retrieve.return_value = [em_item]

        results = await self.manager.retrieve_memories(query, strategy="hierarchical")

        self.assertEqual(len(results), 3)
        self.manager.working_memory.retrieve.assert_called_once_with(query)
        self.manager.long_term_memory.retrieve.assert_called_once_with(query) # Query object is passed as is
        self.manager.episodic_memory.retrieve.assert_called_once_with(query)

        # Results should be sorted by importance score due to HMM's final sort
        self.assertEqual(results[0].id, "wm1")
        self.assertEqual(results[1].id, "ltm1")
        self.assertEqual(results[2].id, "em1")

    async def test_retrieve_with_tier_preference_working(self):
        query = MemoryQuery(query_text="test query", limit=5, tier_preference=MemoryTier.WORKING)
        wm_item = self._create_mock_item("wm1", "Working item", MemoryTier.WORKING, 0.9)

        self.manager.working_memory.retrieve.return_value = [wm_item]

        results = await self.manager.retrieve_memories(query)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].id, "wm1")
        self.manager.working_memory.retrieve.assert_called_once_with(query)
        self.manager.long_term_memory.retrieve.assert_not_called()
        self.manager.episodic_memory.retrieve.assert_not_called()

    async def test_retrieve_limit_hierarchical(self):
        query = MemoryQuery(query_text="test query", limit=1) # Limit is 1

        wm_item1 = self._create_mock_item("wm1", "Working item 1", MemoryTier.WORKING, 0.95)
        wm_item2 = self._create_mock_item("wm2", "Working item 2", MemoryTier.WORKING, 0.90) # Higher importance
        ltm_item = self._create_mock_item("ltm1", "Long-term item", MemoryTier.LONG_TERM, 0.8)

        self.manager.working_memory.retrieve.return_value = [wm_item1, wm_item2] # WM returns 2 items
        self.manager.long_term_memory.retrieve.return_value = [ltm_item]

        results = await self.manager.retrieve_memories(query, strategy="hierarchical")

        self.assertEqual(len(results), 1) # Should respect the query limit
        self.assertEqual(results[0].id, "wm1") # Because HMM sorts all collected items by importance

        self.manager.working_memory.retrieve.assert_called_once_with(query)
        # LTM might not be called if WM already satisfies limit and HMM optimizes,
        # but current HMM implementation calls LTM then sorts & limits.
        # Let's check based on current HMM implementation:
        self.manager.long_term_memory.retrieve.assert_called_once()


    async def test_get_hierarchy_stats(self):
        self.manager.working_memory.get_tier_stats.return_value = {"item_count": 10, "avg_response_time_ms": 50}
        self.manager.long_term_memory.get_tier_stats.return_value = {"total_items": 100, "avg_response_time_ms": 200}
        self.manager.episodic_memory.get_tier_stats.return_value = {"item_count": 5, "avg_response_time_ms": 300}

        stats = await self.manager.get_memory_hierarchy_stats()

        self.assertEqual(stats['hierarchy_overview']['total_items'], 115) # 10 + 100 + 5
        self.assertEqual(stats['tier_distribution'][MemoryTier.WORKING.value]['item_count'], 10)
        self.assertEqual(stats['tier_distribution'][MemoryTier.LONG_TERM.value]['total_items'], 100)
        self.assertEqual(stats['tier_distribution'][MemoryTier.EPISODIC.value]['item_count'], 5)
        self.assertTrue(stats['performance_summary']['targets_met']) # Currently placeholder

    async def test_run_periodic_cleanup(self):
        self.manager.working_memory.cleanup_expired.return_value = 1
        self.manager.long_term_memory.cleanup_expired.return_value = 5
        self.manager.episodic_memory.cleanup_expired.return_value = 0

        await self.manager.run_periodic_cleanup()

        self.manager.working_memory.cleanup_expired.assert_called_once()
        self.manager.long_term_memory.cleanup_expired.assert_called_once()
        self.manager.episodic_memory.cleanup_expired.assert_called_once()

    async def test_store_item_importance_from_classification(self):
        item_id = "item_importance"
        content = "Test importance score"
        metadata = {"id": item_id}
        # ClassificationResult has 'importance_score' attribute in HMM placeholder
        mock_cr = MockClassificationResult(confidence=0.5, importance_score=0.85)

        self.manager.long_term_memory.store.return_value = item_id
        await self.manager.store_memory(content, metadata, mock_cr)

        args, _ = self.manager.long_term_memory.store.call_args
        stored_item: MemoryItem = args[0]
        self.assertEqual(stored_item.importance_score, 0.85)

    async def test_store_item_importance_fallback_to_confidence(self):
        item_id = "item_confidence"
        content = "Test importance fallback"
        metadata = {"id": item_id}
        # Mocking that importance_score attribute might be missing or None
        mock_cr = MockClassificationResult(confidence=0.75, importance_score=None)

        self.manager.long_term_memory.store.return_value = item_id
        await self.manager.store_memory(content, metadata, mock_cr)

        args, _ = self.manager.long_term_memory.store.call_args
        stored_item: MemoryItem = args[0]
        self.assertEqual(stored_item.importance_score, 0.75)


if __name__ == '__main__':
    unittest.main()
