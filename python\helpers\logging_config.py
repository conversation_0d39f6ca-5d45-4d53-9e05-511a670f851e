# python/helpers/logging_config.py
"""
Configures logging for the MemoryV2 project.

This module sets up a standardized logging format and level, controllable
via the LOG_LEVEL environment variable. It ensures consistent logging
across different parts of the application.
"""

import logging
import os
from typing import Optional
from dotenv import load_dotenv

def setup_logging(log_level: Optional[str] = None):
    """
    Initializes and configures the root logger for the application.

    Args:
        log_level: Optional log level override. If not provided, uses LOG_LEVEL
                  environment variable or defaults to INFO.
    """
    # Load environment variables only when needed
    if log_level is None:
        # Try to load from .env file if it exists
        try:
            load_dotenv()
        except Exception:
            pass  # Silently continue if dotenv loading fails
        log_level = os.getenv("LOG_LEVEL", "INFO")

    log_level_str = log_level.upper()
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }

    if log_level_str not in log_levels:
        print(f"Warning: Invalid LOG_LEVEL '{log_level_str}'. Defaulting to INFO.")
        log_level_value = logging.INFO
    else:
        log_level_value = log_levels[log_level_str]

    # Configure the root logger
    logging.basicConfig(
        level=log_level_value,
        format="%(asctime)s - %(levelname)s - [%(name)s in %(module)s.%(funcName)s:%(lineno)d] - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        force=True  # Force reconfiguration if already configured
    )

    # Optionally, set higher levels for noisy libraries
    # logging.getLogger("some_library").setLevel(logging.WARNING)

    # Log the initial logging level
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured with level: {logging.getLevelName(log_level_value)}")

def setup_logging_if_needed():
    """
    Set up logging only if it hasn't been configured yet.
    This is safer for library usage.
    """
    # Check if logging has been configured
    root_logger = logging.getLogger()
    if not root_logger.handlers:
        setup_logging()

# Don't automatically configure logging on import
# Let the application explicitly call setup_logging() when ready

def get_logger(name: str) -> logging.Logger:
    """
    Returns a logger instance with the specified name.

    Args:
        name (str): The name for the logger (typically __name__ of the calling module).

    Returns:
        logging.Logger: Configured logger instance.
    """
    return logging.getLogger(name)

if __name__ == "__main__":
    # Example usage if this script is run directly
    # This demonstrates how other modules would get and use a logger.
    logger = get_logger("my_app_component") # or get_logger(__name__)

    logger.debug("This is a debug message.")
    logger.info("This is an info message.")
    logger.warning("This is a warning message.")
    logger.error("This is an error message.")
    logger.critical("This is a critical message.")

    # Example showing a logger from a different "module"
    another_logger = get_logger("another.module")
    another_logger.info("Info message from another module.")
