# python/helpers/logging_config.py
"""
Configures logging for the MemoryV2 project.

This module sets up a standardized logging format and level, controllable
via the LOG_LEVEL environment variable. It ensures consistent logging
across different parts of the application.
"""

import logging
import os
from dotenv import load_dotenv

# Load environment variables from .env file, especially for LOG_LEVEL
load_dotenv()

def setup_logging():
    """
    Initializes and configures the root logger for the application.

    The logging level is determined by the LOG_LEVEL environment variable.
    Defaults to INFO if not set or if an invalid value is provided.
    """
    log_level_str = os.getenv("LOG_LEVEL", "INFO").upper()
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }

    if log_level_str not in log_levels:
        print(f"Warning: Invalid LOG_LEVEL '{log_level_str}'. Defaulting to INFO.")
        log_level = logging.INFO
    else:
        log_level = log_levels[log_level_str]

    # Configure the root logger
    # Adjust the format for more or less detail as needed.
    # Example format: %(asctime)s - %(name)s - %(levelname)s - %(module)s.%(funcName)s:%(lineno)d - %(message)s
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(levelname)s - [%(name)s in %(module)s.%(funcName)s:%(lineno)d] - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    # Optionally, set higher levels for noisy libraries
    # logging.getLogger("some_library").setLevel(logging.WARNING)

    # Log the initial logging level
    # Use a logger instance to ensure it respects the configured format and level
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured with level: {logging.getLevelName(log_level)}")

# Call setup_logging() when this module is imported for the first time.
# This ensures that logging is configured as soon as any part of the app uses it.
# However, be mindful of import order if other modules configure logging too.
# For more complex applications, explicit initialization might be preferred.
_INITIALIZED = False
if not _INITIALIZED:
    setup_logging()
    _INITIALIZED = True

def get_logger(name: str) -> logging.Logger:
    """
    Returns a logger instance with the specified name.

    Args:
        name (str): The name for the logger (typically __name__ of the calling module).

    Returns:
        logging.Logger: Configured logger instance.
    """
    return logging.getLogger(name)

if __name__ == "__main__":
    # Example usage if this script is run directly
    # This demonstrates how other modules would get and use a logger.
    logger = get_logger("my_app_component") # or get_logger(__name__)

    logger.debug("This is a debug message.")
    logger.info("This is an info message.")
    logger.warning("This is a warning message.")
    logger.error("This is an error message.")
    logger.critical("This is a critical message.")

    # Example showing a logger from a different "module"
    another_logger = get_logger("another.module")
    another_logger.info("Info message from another module.")
