# scripts/environment_check.py
"""
Performs a comprehensive check of the Python environment for the MemoryV2 project.

This script verifies:
1. Python version compatibility.
2. Availability of essential libraries and their versions.
3. Accessibility of critical external services (e.g., OpenAI API, Pinecone).
4. Basic configuration settings from the .env file.

It's intended to be a more thorough diagnostic tool than `validate_env.py`.
"""

import sys
import os
import importlib
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configuration
MIN_PYTHON_VERSION = (3, 8)  # Example: Require Python 3.8 or higher
REQUIRED_LIBRARIES = {
    "openai": "0.28.0",  # Specify minimum or exact versions if necessary
    "pinecone-client": "2.2.0",
    "dotenv": "0.20.0",
    "numpy": "1.20.0",
    "fastapi": "0.90.0", # If using FastAPI for an API layer
    "uvicorn": "0.20.0", # If using FastAPI
    "pytest": "7.0.0",   # For running tests
    # Add other critical libraries here
}

def check_python_version():
    """Validates if the current Python version meets the minimum requirement."""
    print("\n--- Checking Python Version ---")
    current_version = sys.version_info
    if current_version >= MIN_PYTHON_VERSION:
        print(f"Python version {current_version.major}.{current_version.minor}.{current_version.micro} is compatible. [OK]")
        return True
    else:
        print(f"Error: Python version {current_version.major}.{current_version.minor}.{current_version.micro} is "
              f"below the minimum required version {MIN_PYTHON_VERSION[0]}.{MIN_PYTHON_VERSION[1]}.")
        return False

def check_libraries():
    """Checks for the presence and optionally versions of required libraries."""
    print("\n--- Checking Required Libraries ---")
    all_libraries_found = True
    for lib_name, required_version_str in REQUIRED_LIBRARIES.items():
        try:
            module = importlib.import_module(lib_name.split('-')[0]) # Handle names like pinecone-client -> pinecone
            installed_version_str = getattr(module, '__version__', 'unknown')

            # Basic version comparison (can be made more robust)
            # This example assumes versions are comparable strings like "X.Y.Z"
            # For more complex scenarios, consider using packaging.version
            version_ok = True
            if required_version_str and installed_version_str != 'unknown':
                # Simple check: installed >= required (lexicographical)
                if installed_version_str < required_version_str:
                    version_ok = False

            if version_ok:
                print(f"{lib_name} (version {installed_version_str}) is installed. [OK]")
            else:
                print(f"Warning: {lib_name} version {installed_version_str} is installed, "
                      f"but version {required_version_str} or newer is recommended.")
                # Decide if this is a critical failure or just a warning
                # all_libraries_found = False # Uncomment if strict version matching is required

        except ImportError:
            print(f"Error: Library '{lib_name}' is not installed.")
            all_libraries_found = False
        except Exception as e:
            print(f"Error: Could not check library '{lib_name}': {e}")
            all_libraries_found = False
    return all_libraries_found

def check_api_connectivity():
    """
    Placeholder for checking connectivity to external APIs (OpenAI, Pinecone).
    This would involve making simple API calls (e.g., listing models, checking index status).
    Requires API keys to be configured in the environment.
    """
    print("\n--- Checking API Connectivity (Conceptual) ---")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    pinecone_api_key = os.getenv("PINECONE_API_KEY")
    pinecone_env = os.getenv("PINECONE_ENVIRONMENT")

    if openai_api_key and openai_api_key != "your_openai_api_key_here":
        print("OpenAI API key is set. [INFO]")
        # TODO: Add a simple test call to OpenAI API, e.g., list models
        # try:
        #   import openai
        #   openai.api_key = openai_api_key
        #   openai.Model.list()
        #   print("Successfully connected to OpenAI API. [OK]")
        # except Exception as e:
        #   print(f"Error: Failed to connect to OpenAI API: {e}")
        #   return False
    else:
        print("OpenAI API key is not set or is default. Connectivity test skipped. [WARNING]")

    if pinecone_api_key and pinecone_api_key != "your_pinecone_api_key_here" and pinecone_env:
        print("Pinecone API key and environment are set. [INFO]")
        # TODO: Add a simple test call to Pinecone, e.g., describe index
        # try:
        #   import pinecone
        #   pinecone.init(api_key=pinecone_api_key, environment=pinecone_env)
        #   # pinecone.describe_index(os.getenv("PINECONE_INDEX_NAME", "memory-v2-index")) # Requires index to exist
        #   print("Successfully initialized Pinecone client (basic check). [OK]")
        # except Exception as e:
        #   print(f"Error: Failed to initialize Pinecone client: {e}")
        #   return False
    else:
        print("Pinecone credentials are not fully set or are default. Connectivity test skipped. [WARNING]")
    return True # Placeholder - actual connectivity tests would determine this

def check_env_variables():
    """Runs the validate_env.py script for a basic .env file check."""
    print("\n--- Checking .env File Configuration (via validate_env.py) ---")
    try:
        # Assuming validate_env.py is in the same directory
        from validate_env import validate_environment
        passed, errors = validate_environment()
        if passed:
            print("validate_env.py check passed. [OK]")
            return True
        else:
            print("validate_env.py check failed with the following issues:")
            for error in errors:
                print(f"  - {error}")
            return False
    except ImportError:
        print("Error: Could not import 'validate_env.py'. Make sure it's in the 'scripts' directory.")
        return False
    except Exception as e:
        print(f"Error running validate_env.py: {e}")
        return False

def main():
    """Runs all environment checks."""
    print("Starting comprehensive environment check for MemoryV2...")
    results = {
        "python_version": check_python_version(),
        "libraries": check_libraries(),
        "env_variables": check_env_variables(), # Run this before API connectivity
        "api_connectivity": check_api_connectivity(), # Depends on .env vars
    }

    print("\n--- Summary ---")
    all_ok = True
    for check_name, result in results.items():
        status = "[OK]" if result else "[FAIL]"
        print(f"{check_name.replace('_', ' ').title()}: {status}")
        if not result:
            all_ok = False

    if all_ok:
        print("\nEnvironment check completed successfully. All systems nominal.")
        sys.exit(0)
    else:
        print("\nEnvironment check revealed issues. Please address them before proceeding.")
        sys.exit(1)

if __name__ == "__main__":
    main()
