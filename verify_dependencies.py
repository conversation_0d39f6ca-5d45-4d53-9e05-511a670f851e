"""
Dependency verification script for intelligent classification system
"""

def verify_dependencies():
    """Verify all required dependencies are installed and working"""

    try:
        # Core scientific computing
        import numpy as np
        import scipy
        from sklearn.metrics.pairwise import cosine_similarity
        from sklearn.feature_extraction.text import TfidfVectorizer
        print("✅ Scientific computing libraries: OK")

        # Graph processing
        import networkx as nx
        print("✅ NetworkX: OK")

        # Date/time processing
        from dateutil import parser
        print("✅ Python-dateutil: OK")

        # Performance libraries
        try:
            import numba
            print("✅ Numba: OK")
        except ImportError:
            print("⚠️  Numba: Not installed (optional)")

        # Testing libraries
        import pytest
        print("✅ Pytest: OK")

        # Monitoring libraries
        import psutil
        print("✅ Psutil: OK")

        # Test basic functionality
        test_array = np.array([1, 2, 3, 4, 5])
        # Ensure cosine_similarity is tested with 2D arrays
        test_similarity_matrix = np.array([[1, 2, 3]])
        test_similarity = cosine_similarity(test_similarity_matrix, test_similarity_matrix)
        test_graph = nx.Graph()
        test_graph.add_edge(1, 2)

        print("✅ Basic functionality tests: PASSED")
        print("\n🎉 All dependencies verified successfully!")

        return True

    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

if __name__ == "__main__":
    success = verify_dependencies()
    exit(0 if success else 1)
