# Phase 1: Foundation Setup - Dependencies
## Install and Configure Dependencies

This document provides step-by-step instructions for installing and configuring all dependencies required for the intelligent data classification system.

## 📦 Core Dependencies Installation

### Step 1.1: Install Python Dependencies

**File:** `requirements.txt`
**Action:** Add new dependencies to existing requirements

```bash
# Navigate to project root
cd /path/to/agent-zero-v2

# Add classification system dependencies
cat >> requirements.txt << EOF

# Intelligent Classification System Dependencies
scikit-learn>=1.3.0
numpy>=1.24.0
scipy>=1.10.0
networkx>=3.1
python-dateutil>=2.8.2

# Performance and optimization
numba>=0.58.0
cython>=3.0.0

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
pytest-cov>=4.1.0

# Monitoring and profiling
memory-profiler>=0.61.0
psutil>=5.9.0
EOF

# Install all dependencies
pip install -r requirements.txt
```

### Step 1.2: Verify Installation

**Action:** Test that all dependencies are properly installed

```python
# Create verification script: verify_dependencies.py
"""
Dependency verification script for intelligent classification system
"""

def verify_dependencies():
    """Verify all required dependencies are installed and working"""
    
    try:
        # Core scientific computing
        import numpy as np
        import scipy
        from sklearn.metrics.pairwise import cosine_similarity
        from sklearn.feature_extraction.text import TfidfVectorizer
        print("✅ Scientific computing libraries: OK")
        
        # Graph processing
        import networkx as nx
        print("✅ NetworkX: OK")
        
        # Date/time processing
        from dateutil import parser
        print("✅ Python-dateutil: OK")
        
        # Performance libraries
        try:
            import numba
            print("✅ Numba: OK")
        except ImportError:
            print("⚠️  Numba: Not installed (optional)")
        
        # Testing libraries
        import pytest
        print("✅ Pytest: OK")
        
        # Monitoring libraries
        import psutil
        print("✅ Psutil: OK")
        
        # Test basic functionality
        test_array = np.array([1, 2, 3, 4, 5])
        test_similarity = cosine_similarity([[1, 2, 3]], [[1, 2, 3]])
        test_graph = nx.Graph()
        test_graph.add_edge(1, 2)
        
        print("✅ Basic functionality tests: PASSED")
        print("\n🎉 All dependencies verified successfully!")
        
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

if __name__ == "__main__":
    success = verify_dependencies()
    exit(0 if success else 1)
```

**Run verification:**
```bash
python verify_dependencies.py
```

## 🔧 Environment Configuration

### Step 1.3: Configure Environment Variables

**File:** `.env` (create in project root)
**Action:** Set up environment configuration

```bash
# Create .env file for configuration
cat > .env << EOF
# Intelligent Classification System Configuration

# API Keys
OPENAI_API_KEY=your-openai-api-key-here
GRAPHITI_API_KEY=your-graphiti-api-key-here

# Classification Configuration
CLASSIFICATION_BACKEND=faiss
CLASSIFICATION_SEMANTIC_OVERLAP_SHARED=0.70
CLASSIFICATION_SEMANTIC_OVERLAP_CUSTOM=0.30
CLASSIFICATION_ENTITY_CONFIDENCE_MIN=0.70
CLASSIFICATION_PRIVACY_THRESHOLD=3

# Performance Targets
CLASSIFICATION_WORKING_MEMORY_TARGET_MS=100
CLASSIFICATION_LONG_TERM_MEMORY_TARGET_MS=500
CLASSIFICATION_EPISODIC_MEMORY_TARGET_MS=1000
CLASSIFICATION_DECISION_TARGET_MS=50

# Memory Configuration
MEMORY_WORKING_SIZE=50000
MEMORY_WORKING_RETENTION_DAYS=7
MEMORY_BATCH_SIZE=100
MEMORY_BATCH_TIMEOUT=300

# Caching Configuration
CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=100000

# User Preferences
ADAPTIVE_LEARNING_ENABLED=true
PREFERENCE_LEARNING_RATE=0.1

# Development Settings
DEBUG_MODE=true
LOG_LEVEL=INFO
PERFORMANCE_MONITORING=true

# Testing Configuration
TEST_DATA_SIZE=1000
PERFORMANCE_TEST_SIZE=10000
EOF

# Set secure permissions
chmod 600 .env
```

### Step 1.4: Environment Loading Setup

**File:** `python/helpers/env_loader.py`
**Action:** Create environment variable loader

```python
"""
Environment variable loader for intelligent classification system
"""

import os
from typing import Any, Optional
from dotenv import load_dotenv

class EnvironmentLoader:
    """Load and manage environment variables for the classification system"""
    
    def __init__(self, env_file: str = ".env"):
        self.env_file = env_file
        self._load_environment()
    
    def _load_environment(self):
        """Load environment variables from .env file"""
        if os.path.exists(self.env_file):
            load_dotenv(self.env_file)
            print(f"✅ Environment loaded from {self.env_file}")
        else:
            print(f"⚠️  Environment file {self.env_file} not found, using system environment")
    
    def get_str(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get string environment variable"""
        return os.getenv(key, default)
    
    def get_int(self, key: str, default: int = 0) -> int:
        """Get integer environment variable"""
        try:
            value = os.getenv(key)
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    def get_float(self, key: str, default: float = 0.0) -> float:
        """Get float environment variable"""
        try:
            value = os.getenv(key)
            return float(value) if value is not None else default
        except (ValueError, TypeError):
            return default
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean environment variable"""
        value = os.getenv(key, "").lower()
        if value in ("true", "1", "yes", "on"):
            return True
        elif value in ("false", "0", "no", "off"):
            return False
        else:
            return default
    
    def get_list(self, key: str, separator: str = ",", default: Optional[list] = None) -> list:
        """Get list environment variable"""
        value = os.getenv(key)
        if value:
            return [item.strip() for item in value.split(separator)]
        return default or []
    
    def validate_required_vars(self, required_vars: list) -> bool:
        """Validate that all required environment variables are set"""
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing required environment variables: {missing_vars}")
            return False
        
        print("✅ All required environment variables are set")
        return True
    
    def get_classification_config(self) -> dict:
        """Get classification-specific configuration"""
        return {
            'backend': self.get_str('CLASSIFICATION_BACKEND', 'faiss'),
            'semantic_overlap_shared': self.get_float('CLASSIFICATION_SEMANTIC_OVERLAP_SHARED', 0.70),
            'semantic_overlap_custom': self.get_float('CLASSIFICATION_SEMANTIC_OVERLAP_CUSTOM', 0.30),
            'entity_confidence_min': self.get_float('CLASSIFICATION_ENTITY_CONFIDENCE_MIN', 0.70),
            'privacy_threshold': self.get_int('CLASSIFICATION_PRIVACY_THRESHOLD', 3),
        }
    
    def get_performance_config(self) -> dict:
        """Get performance target configuration"""
        return {
            'working_memory_target_ms': self.get_int('CLASSIFICATION_WORKING_MEMORY_TARGET_MS', 100),
            'long_term_memory_target_ms': self.get_int('CLASSIFICATION_LONG_TERM_MEMORY_TARGET_MS', 500),
            'episodic_memory_target_ms': self.get_int('CLASSIFICATION_EPISODIC_MEMORY_TARGET_MS', 1000),
            'decision_target_ms': self.get_int('CLASSIFICATION_DECISION_TARGET_MS', 50),
        }
    
    def get_memory_config(self) -> dict:
        """Get memory system configuration"""
        return {
            'working_size': self.get_int('MEMORY_WORKING_SIZE', 50000),
            'working_retention_days': self.get_int('MEMORY_WORKING_RETENTION_DAYS', 7),
            'batch_size': self.get_int('MEMORY_BATCH_SIZE', 100),
            'batch_timeout': self.get_int('MEMORY_BATCH_TIMEOUT', 300),
        }

# Global environment loader instance
env_loader = EnvironmentLoader()

# Validate critical environment variables
REQUIRED_VARS = [
    'OPENAI_API_KEY'
]

if not env_loader.validate_required_vars(REQUIRED_VARS):
    print("⚠️  Some required environment variables are missing. Please check your .env file.")
```

## 🧪 Dependency Testing

### Step 1.5: Create Dependency Test Suite

**File:** `tests/test_dependencies.py`
**Action:** Create comprehensive dependency tests

```python
"""
Test suite for dependency verification and functionality
"""

import pytest
import numpy as np
import scipy
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import networkx as nx
from dateutil import parser
import psutil
from datetime import datetime

class TestDependencies:
    """Test all required dependencies"""
    
    def test_numpy_functionality(self):
        """Test NumPy basic functionality"""
        # Create test arrays
        arr1 = np.array([1, 2, 3, 4, 5])
        arr2 = np.array([2, 4, 6, 8, 10])
        
        # Test operations
        result = arr1 + arr2
        expected = np.array([3, 6, 9, 12, 15])
        
        assert np.array_equal(result, expected)
        assert arr1.dtype == np.int64 or arr1.dtype == np.int32
    
    def test_scipy_functionality(self):
        """Test SciPy basic functionality"""
        from scipy import stats
        
        # Test statistical functions
        data = [1, 2, 3, 4, 5]
        mean = stats.tmean(data)
        
        assert mean == 3.0
    
    def test_sklearn_functionality(self):
        """Test scikit-learn functionality"""
        # Test TF-IDF vectorizer
        documents = ["hello world", "world of python", "python programming"]
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform(documents)
        
        assert tfidf_matrix.shape[0] == 3  # 3 documents
        assert tfidf_matrix.shape[1] > 0   # Features extracted
        
        # Test cosine similarity
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])
        assert 0 <= similarity[0][0] <= 1  # Similarity should be between 0 and 1
    
    def test_networkx_functionality(self):
        """Test NetworkX functionality"""
        # Create test graph
        G = nx.Graph()
        G.add_edge(1, 2)
        G.add_edge(2, 3)
        G.add_edge(3, 4)
        
        assert G.number_of_nodes() == 4
        assert G.number_of_edges() == 3
        
        # Test shortest path
        path = nx.shortest_path(G, 1, 4)
        assert len(path) == 4  # Path: 1 -> 2 -> 3 -> 4
    
    def test_dateutil_functionality(self):
        """Test python-dateutil functionality"""
        # Test date parsing
        date_str = "2024-06-18T10:30:00Z"
        parsed_date = parser.parse(date_str)
        
        assert parsed_date.year == 2024
        assert parsed_date.month == 6
        assert parsed_date.day == 18
    
    def test_psutil_functionality(self):
        """Test psutil functionality"""
        # Test system information
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory_info = psutil.virtual_memory()
        
        assert isinstance(cpu_percent, float)
        assert 0 <= cpu_percent <= 100
        assert memory_info.total > 0
    
    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """Test async/await functionality"""
        import asyncio
        
        async def async_function():
            await asyncio.sleep(0.01)
            return "async_result"
        
        result = await async_function()
        assert result == "async_result"

class TestEnvironmentConfiguration:
    """Test environment configuration"""
    
    def test_environment_loader(self):
        """Test environment variable loading"""
        from python.helpers.env_loader import env_loader
        
        # Test basic functionality
        config = env_loader.get_classification_config()
        assert isinstance(config, dict)
        assert 'backend' in config
        
        performance_config = env_loader.get_performance_config()
        assert isinstance(performance_config, dict)
        assert 'working_memory_target_ms' in performance_config
    
    def test_configuration_values(self):
        """Test that configuration values are reasonable"""
        from python.helpers.env_loader import env_loader
        
        perf_config = env_loader.get_performance_config()
        
        # Test performance targets are reasonable
        assert 10 <= perf_config['working_memory_target_ms'] <= 1000
        assert 100 <= perf_config['long_term_memory_target_ms'] <= 5000
        assert 500 <= perf_config['episodic_memory_target_ms'] <= 10000

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
```

**Run dependency tests:**
```bash
# Run all dependency tests
python -m pytest tests/test_dependencies.py -v

# Run with coverage
python -m pytest tests/test_dependencies.py --cov=python.helpers --cov-report=html
```

## ✅ Validation Checklist

Before proceeding to the next step, verify:

- [ ] All Python dependencies are installed without errors
- [ ] Dependency verification script runs successfully
- [ ] Environment variables are configured in `.env` file
- [ ] Environment loader works correctly
- [ ] All dependency tests pass
- [ ] No import errors when testing basic functionality
- [ ] Performance monitoring tools are available

## 🚨 Troubleshooting

### Common Installation Issues

**Dependency conflicts:**
```bash
# Clean install approach
pip uninstall -y scikit-learn numpy scipy
pip install --no-cache-dir --upgrade pip
pip install -r requirements.txt
```

**Permission issues:**
```bash
# Use user installation if needed
pip install --user -r requirements.txt
```

**Version conflicts:**
```bash
# Check for conflicts
pip check

# Show dependency tree
pip install pipdeptree
pipdeptree
```

---

**Next Step**: [Configuration System](configuration-system.md)
