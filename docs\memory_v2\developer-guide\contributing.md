# Contributing to Agent Zero v2.0

Welcome to the Agent Zero community! We're excited to have you contribute to the development of this advanced AI agent framework. This guide will help you get started with contributing code, documentation, and improvements to Agent Zero v2.0.

## Getting Started

### Development Environment Setup

**Prerequisites:**
- Python 3.11 or 3.12
- Docker Desktop
- Git
- Code editor (VS Code recommended)

**Clone and Setup:**
```bash
# Clone the repository
git clone https://github.com/frdel/agent-zero.git
cd agent-zero

# Create development environment
conda create -n agent-zero-dev python=3.12 -y
conda activate agent-zero-dev

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies

# Setup pre-commit hooks
pre-commit install
```

**Development Configuration:**
```bash
# Copy example environment
cp example.env .env

# Edit .env with your development settings
# Set DEBUG=true for development mode
# Configure your API keys and database connections
```

### Project Structure

```
agent-zero/
├── agent.py                 # Main agent class
├── initialize.py            # Initialization and configuration
├── python/                  # Core Python modules
│   ├── api/                # API endpoints and routes
│   ├── extensions/         # Framework extensions
│   ├── helpers/            # Utility functions and helpers
│   └── tools/              # Core tools and instruments
├── prompts/                # Agent prompts and templates
├── webui/                  # Web interface components
├── docs/                   # Documentation
│   ├── memory_v2/          # Current documentation
│   └── legacy/             # Legacy documentation
├── tests/                  # Test suite
├── docker/                 # Docker configurations
└── requirements.txt        # Python dependencies
```

## Contribution Guidelines

### Code Style and Standards

**Python Code Style:**
- Follow PEP 8 style guidelines
- Use type hints for all function parameters and return values
- Write docstrings for all public functions and classes
- Maximum line length: 100 characters

**Example:**
```python
from typing import List, Optional, Dict, Any
import asyncio

class MemoryManager:
    """Manages memory operations for Agent Zero.
    
    This class provides a unified interface for different memory backends
    and handles automatic memory recall and storage.
    """
    
    def __init__(self, config: MemoryConfig) -> None:
        """Initialize the memory manager.
        
        Args:
            config: Memory configuration object containing backend settings
        """
        self.config = config
        self.backend: Optional[MemoryBackend] = None
    
    async def save_memory(
        self, 
        content: str, 
        metadata: Dict[str, Any]
    ) -> str:
        """Save content to memory with associated metadata.
        
        Args:
            content: The text content to save
            metadata: Additional metadata for the memory
            
        Returns:
            The unique identifier for the saved memory
            
        Raises:
            MemoryError: If the memory backend is not initialized
        """
        if not self.backend:
            raise MemoryError("Memory backend not initialized")
        
        return await self.backend.save(content, metadata)
```

**JavaScript/TypeScript (Web UI):**
- Use ES6+ features
- Follow Prettier formatting
- Use TypeScript for type safety
- Component-based architecture

### Testing Requirements

**Test Coverage:**
- All new features must include comprehensive tests
- Aim for >90% code coverage
- Include both unit tests and integration tests

**Running Tests:**
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=python --cov-report=html

# Run specific test file
pytest tests/test_memory_system.py

# Run integration tests
pytest tests/integration/
```

**Writing Tests:**
```python
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from python.helpers.memory_abstraction import MemoryManager

class TestMemoryManager:
    """Test suite for MemoryManager class."""
    
    @pytest.fixture
    async def memory_manager(self):
        """Create a memory manager instance for testing."""
        config = Mock()
        config.backend_type = "faiss"
        manager = MemoryManager(config)
        await manager.initialize()
        return manager
    
    @pytest.mark.asyncio
    async def test_save_memory(self, memory_manager):
        """Test saving memory content."""
        content = "Test memory content"
        metadata = {"test": True}
        
        memory_id = await memory_manager.save_memory(content, metadata)
        
        assert memory_id is not None
        assert isinstance(memory_id, str)
    
    @pytest.mark.asyncio
    async def test_search_memory(self, memory_manager):
        """Test searching memory content."""
        # Setup test data
        await memory_manager.save_memory("Python programming", {"topic": "coding"})
        await memory_manager.save_memory("JavaScript development", {"topic": "coding"})
        
        # Search for memories
        results = await memory_manager.search_memories("programming", limit=10)
        
        assert len(results) > 0
        assert any("Python" in result.content for result in results)
```

### Documentation Standards

**Documentation Requirements:**
- All public APIs must be documented
- Include code examples for complex features
- Update relevant documentation for any changes
- Use clear, concise language

**Documentation Format:**
```markdown
# Feature Name

Brief description of the feature and its purpose.

## Usage

Basic usage example:

```python
# Example code
result = await feature.execute(parameters)
```

## Parameters

- `parameter1` (str): Description of parameter
- `parameter2` (int, optional): Description with default value

## Returns

Description of return value and type.

## Examples

### Basic Example

```python
# Detailed example with explanation
```

### Advanced Example

```python
# More complex usage scenario
```
```

## Development Workflow

### Branch Strategy

**Main Branches:**
- `main`: Stable release branch
- `develop`: Development integration branch
- `feat/*`: Feature development branches
- `fix/*`: Bug fix branches
- `docs/*`: Documentation updates

**Workflow:**
```bash
# Create feature branch
git checkout develop
git pull origin develop
git checkout -b feat/new-memory-backend

# Make changes and commit
git add .
git commit -m "feat: add new memory backend implementation"

# Push and create pull request
git push origin feat/new-memory-backend
```

### Commit Message Format

Use conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

**Examples:**
```
feat(memory): add Graphiti backend support

Implement Graphiti temporal knowledge graph backend with entity extraction
and relationship modeling capabilities.

Closes #123
```

```
fix(api): resolve memory search timeout issue

Increase default timeout for memory search operations and add proper
error handling for timeout scenarios.

Fixes #456
```

### Pull Request Process

**Before Submitting:**
1. Ensure all tests pass
2. Update documentation if needed
3. Add changelog entry
4. Rebase on latest develop branch

**Pull Request Template:**
```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
```

**Review Process:**
1. Automated checks (CI/CD)
2. Code review by maintainers
3. Testing in development environment
4. Approval and merge

## Contributing Areas

### Core Framework

**Memory Systems:**
- New memory backend implementations
- Performance optimizations
- Query capabilities enhancement
- Data migration tools

**Agent System:**
- Multi-agent coordination improvements
- New agent specializations
- Performance optimizations
- Error handling enhancements

**Tool System:**
- New tool implementations
- Tool reliability improvements
- Custom instrument frameworks
- Integration with external services

### Web Interface

**Frontend Development:**
- React component improvements
- User experience enhancements
- Mobile responsiveness
- Accessibility improvements

**API Development:**
- New API endpoints
- Performance optimizations
- Authentication improvements
- Documentation generation

### Documentation

**Technical Documentation:**
- API reference updates
- Architecture documentation
- Tutorial creation
- Code examples

**User Documentation:**
- Installation guides
- Configuration tutorials
- Troubleshooting guides
- Best practices

### Testing and Quality Assurance

**Test Development:**
- Unit test coverage improvement
- Integration test scenarios
- Performance testing
- Security testing

**Quality Improvements:**
- Code review processes
- Automated testing pipelines
- Performance monitoring
- Security audits

## Community Guidelines

### Code of Conduct

We are committed to providing a welcoming and inclusive environment:

- **Be respectful**: Treat all community members with respect
- **Be collaborative**: Work together constructively
- **Be inclusive**: Welcome newcomers and diverse perspectives
- **Be patient**: Help others learn and grow
- **Be constructive**: Provide helpful feedback and suggestions

### Communication Channels

**Development Discussions:**
- GitHub Issues: Bug reports and feature requests
- GitHub Discussions: General development discussions
- Pull Request Reviews: Code-specific discussions

**Community Support:**
- Discord: Real-time chat and support
- GitHub Discussions: Long-form discussions
- Documentation: Self-service help

### Recognition

**Contributors:**
- All contributors are recognized in the project README
- Significant contributions are highlighted in release notes
- Active contributors may be invited to join the core team

**Contribution Types:**
- Code contributions
- Documentation improvements
- Bug reports and testing
- Community support and mentoring
- Design and user experience improvements

## Release Process

### Version Numbering

We follow Semantic Versioning (SemVer):
- `MAJOR.MINOR.PATCH`
- Major: Breaking changes
- Minor: New features (backward compatible)
- Patch: Bug fixes (backward compatible)

### Release Cycle

**Regular Releases:**
- Minor releases: Monthly
- Patch releases: As needed for critical fixes
- Major releases: Quarterly or for significant changes

**Release Process:**
1. Feature freeze on develop branch
2. Release candidate testing
3. Documentation updates
4. Release notes preparation
5. Tag and release
6. Docker image updates

## Getting Help

### For Contributors

**Technical Questions:**
- Check existing documentation
- Search GitHub issues
- Ask in Discord #development channel
- Create GitHub discussion

**Process Questions:**
- Review this contributing guide
- Ask in Discord #contributors channel
- Contact maintainers directly

### For Maintainers

**Reviewing Contributions:**
- Use the pull request template
- Provide constructive feedback
- Test changes thoroughly
- Update documentation as needed

**Community Management:**
- Welcome new contributors
- Provide guidance and mentoring
- Maintain code quality standards
- Foster inclusive environment

## Next Steps

Ready to contribute? Here's how to get started:

1. **Set up development environment** following the setup guide above
2. **Find an issue** to work on in the GitHub issues
3. **Join the community** on Discord to introduce yourself
4. **Read the codebase** to understand the architecture
5. **Start small** with documentation or bug fixes
6. **Ask questions** when you need help

Thank you for contributing to Agent Zero! Your contributions help make this project better for everyone.
