# Configuration Guide - Agent Zero v2.0

This guide covers all configuration options for Agent Zero v2.0, including memory backends, model selection, and advanced settings.

## Configuration Methods

### Web Interface (Recommended)
Access settings through the web UI by clicking the gear icon in the sidebar.

### Environment Variables
Configure through `.env` file for persistent settings across restarts.

### Settings File
Direct editing of `settings.json` for advanced configurations.

## Core Configuration

### API Keys and Authentication

**OpenAI Configuration:**
```env
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORG_ID=your-org-id  # Optional
```

**Anthropic Configuration:**
```env
ANTHROPIC_API_KEY=your-anthropic-api-key
```

**Google Configuration:**
```env
GOOGLE_API_KEY=your-google-api-key
```

**Local Models (Ollama):**
```env
OLLAMA_HOST=http://localhost:11434
```

### Model Selection

Configure different models for different roles:

**Chat Model (Primary LLM):**
- **Purpose**: Main conversation and reasoning
- **Recommended**: GPT-4, Claude-3, or Llama-3.1-70B
- **Settings**: Higher context length, moderate temperature

**Utility Model (Background Tasks):**
- **Purpose**: Memory management, summarization, internal tasks
- **Recommended**: GPT-3.5-turbo, Claude-3-haiku, or Llama-3.1-8B
- **Settings**: Lower cost, faster response

**Embedding Model (Memory/Knowledge):**
- **Purpose**: Vector embeddings for memory and knowledge retrieval
- **Recommended**: text-embedding-3-small, text-embedding-ada-002
- **Note**: Changing this requires memory re-indexing

## Memory Backend Configuration

### Auto Backend (Default)
```env
MEMORY_BACKEND=auto
```
- Automatically selects best available backend
- Falls back from Graphiti to FAISS if needed
- Recommended for most users

### FAISS Backend
```env
MEMORY_BACKEND=faiss
FAISS_INDEX_TYPE=cpu  # or 'gpu' for GPU acceleration
FAISS_SIMILARITY_THRESHOLD=0.7
```

**FAISS Settings:**
- **Index Type**: `cpu` or `gpu` (requires CUDA)
- **Similarity Threshold**: 0.0-1.0 (higher = more strict matching)
- **Max Results**: Maximum memories returned per query

### Graphiti Backend
```env
MEMORY_BACKEND=graphiti

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your-password

# Graphiti Settings
GRAPHITI_GROUP_ID=agent-zero-default
GRAPHITI_BULK_BATCH_SIZE=100
GRAPHITI_BULK_TIMEOUT=300
```

**Graphiti Settings:**
- **Group ID**: Namespace for isolating different agent instances
- **Bulk Batch Size**: Number of episodes processed in bulk operations
- **Bulk Timeout**: Timeout for bulk processing operations

## Advanced Configuration

### Agent Behavior

**Prompts Configuration:**
```env
AGENT_PROMPTS_SUBDIR=default  # or 'hacker', 'research_agent'
```

**Memory Configuration:**
```env
AGENT_MEMORY_SUBDIR=main  # Memory isolation namespace
AGENT_KNOWLEDGE_SUBDIRS=default,custom  # Knowledge directories
```

**Multi-Agent Settings:**
```env
MAX_AGENT_DEPTH=5  # Maximum delegation depth
AGENT_TIMEOUT=300  # Agent execution timeout
```

### Performance Tuning

**Context Management:**
```env
CONTEXT_LENGTH=8192  # Model context window
CONTEXT_WINDOW_SPACE=0.7  # Fraction for chat history
MEMORY_RECALL_LIMIT=10  # Max memories per recall
```

**Processing Settings:**
```env
PARALLEL_PROCESSING=true
MAX_CONCURRENT_AGENTS=3
TOOL_EXECUTION_TIMEOUT=60
```

### Docker Configuration

**Resource Limits:**
```yaml
# docker-compose.yml
services:
  agent-zero:
    image: frdel/agent-zero-run:latest
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
```

**Volume Mounts:**
```bash
docker run -v ~/agent-zero-data:/a0 \
           -v ~/custom-knowledge:/a0/knowledge/custom \
           -v ~/custom-tools:/a0/instruments/custom \
           frdel/agent-zero-run:latest
```

## Web UI Configuration

### Interface Settings

**Authentication:**
```env
UI_LOGIN=admin
UI_PASSWORD=your-secure-password
```

**Appearance:**
```env
UI_THEME=dark  # or 'light'
UI_LANGUAGE=en  # Language code
```

**Features:**
```env
ENABLE_VOICE_INTERFACE=true
ENABLE_FILE_BROWSER=true
ENABLE_MEMORY_BROWSER=true
```

### Speech Configuration

**Speech-to-Text:**
```env
STT_MODEL_SIZE=base  # tiny, base, small, medium, large
STT_LANGUAGE=en
STT_SILENCE_THRESHOLD=0.5
STT_SILENCE_DURATION=2.0
```

**Text-to-Speech:**
```env
TTS_ENABLED=true
TTS_VOICE=alloy  # OpenAI TTS voices
TTS_SPEED=1.0
```

## Knowledge Management

### Document Processing

**Supported Formats:**
- PDF, TXT, MD, DOCX, HTML
- Code files (PY, JS, TS, etc.)
- Structured data (JSON, CSV, XML)

**Processing Settings:**
```env
KNOWLEDGE_CHUNK_SIZE=1000  # Characters per chunk
KNOWLEDGE_CHUNK_OVERLAP=200  # Overlap between chunks
KNOWLEDGE_AUTO_IMPORT=true  # Auto-process uploaded files
```

### Entity Extraction (Graphiti Only)

**Entity Settings:**
```env
ENTITY_EXTRACTION_ENABLED=true
ENTITY_CONFIDENCE_THRESHOLD=0.8
RELATIONSHIP_EXTRACTION_ENABLED=true
```

## Security Configuration

### Access Control

**Network Security:**
```env
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ORIGINS=http://localhost:3000,https://your-app.com
```

**Code Execution:**
```env
CODE_EXEC_DOCKER_ENABLED=true
CODE_EXEC_TIMEOUT=30
ALLOWED_CODE_LANGUAGES=python,javascript,bash
```

### Data Protection

**Encryption:**
```env
ENCRYPT_MEMORY=true
ENCRYPTION_KEY=your-32-character-key
```

**Privacy:**
```env
LOG_CONVERSATIONS=false
ANONYMIZE_LOGS=true
DATA_RETENTION_DAYS=90
```

## Environment-Specific Configurations

### Development Environment
```env
DEBUG=true
LOG_LEVEL=DEBUG
RELOAD_ON_CHANGE=true
ENABLE_PROFILING=true
```

### Production Environment
```env
DEBUG=false
LOG_LEVEL=INFO
ENABLE_METRICS=true
HEALTH_CHECK_ENABLED=true
```

### Testing Environment
```env
MEMORY_BACKEND=faiss  # Faster for tests
USE_MOCK_MODELS=true
DISABLE_EXTERNAL_APIS=true
```

## Configuration Validation

### Verify Settings
```bash
# Check configuration through API
curl http://localhost:8080/api/config/validate

# View current settings
curl http://localhost:8080/api/config/current
```

### Common Issues

**Memory Backend Errors:**
- Verify Neo4j connection for Graphiti
- Check API keys for embedding models
- Ensure sufficient disk space for FAISS

**Model Configuration:**
- Verify API keys are correct
- Check model names match provider specifications
- Ensure sufficient context length for tasks

**Performance Issues:**
- Increase Docker memory allocation
- Use GPU acceleration where available
- Optimize batch sizes for bulk operations

## Configuration Templates

### Minimal Setup (FAISS)
```env
OPENAI_API_KEY=your-key
MEMORY_BACKEND=faiss
```

### Advanced Setup (Graphiti)
```env
OPENAI_API_KEY=your-key
MEMORY_BACKEND=graphiti
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your-password
GRAPHITI_GROUP_ID=my-agent
```

### Local Models Only
```env
MEMORY_BACKEND=faiss
OLLAMA_HOST=http://localhost:11434
# No API keys required
```

## Next Steps

- **[Memory Systems Overview](../memory-systems/overview.md)**: Deep dive into memory backends
- **[Architecture Overview](../core-concepts/architecture.md)**: Understand system architecture
- **[Advanced Features](../advanced-features/)**: Explore advanced capabilities
- **[Troubleshooting](../developer-guide/troubleshooting.md)**: Solve common issues

For specific configuration scenarios, see the relevant sections in the advanced documentation.
