# Graphiti Backend - Temporal Knowledge Graph Memory

The Graphiti backend provides Agent Zero with advanced temporal knowledge graph capabilities, enabling sophisticated relationship modeling, entity extraction, and time-aware queries. This guide covers setup, configuration, and advanced usage of the Graphiti memory system.

## What is Graphiti?

Graphiti is a temporal knowledge graph system that represents information as interconnected facts with temporal context. Unlike traditional vector databases, Graphiti maintains:

- **Entities**: People, places, concepts, objects
- **Relationships**: Connections between entities with semantic meaning
- **Temporal Context**: When relationships were formed or changed
- **Episodes**: Time-stamped events and conversations

### Knowledge Graph Structure

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Entity    │    │Relationship │    │   Entity    │
│   "Python"  │◄──►│   "uses"    │◄──►│"Web Dev"    │
│             │    │ (2024-06)   │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────────────────────────────────────────────┐
│              Episode (Conversation)                 │
│ "User asked about Python for web development"      │
│ Timestamp: 2024-06-18T10:30:00Z                   │
└─────────────────────────────────────────────────────┘
```

## Setup and Installation

### Prerequisites

**Neo4j Database:**
- Neo4j 4.4+ or 5.x
- APOC plugin installed
- Minimum 2GB RAM allocated

**API Access:**
- OpenAI API key for embeddings
- Sufficient API quota for embedding generation

### Neo4j Installation

**Option 1: Docker (Recommended)**
```bash
# Create Neo4j container with APOC plugin
docker run -d \
  --name neo4j-graphiti \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/your-secure-password \
  -e NEO4J_PLUGINS='["apoc"]' \
  -e NEO4J_dbms_memory_heap_initial__size=2G \
  -e NEO4J_dbms_memory_heap_max__size=4G \
  -v neo4j-data:/data \
  neo4j:latest
```

**Option 2: Neo4j Desktop**
1. Download from [neo4j.com/download](https://neo4j.com/download/)
2. Create new database
3. Install APOC plugin from Graph Apps
4. Set memory allocation to at least 2GB

**Option 3: Neo4j AuraDB (Cloud)**
1. Create account at [neo4j.com/aura](https://neo4j.com/aura/)
2. Create new database instance
3. Note connection URI and credentials

### Agent Zero Configuration

**Environment Variables:**
```env
# Memory Backend
MEMORY_BACKEND=graphiti

# Neo4j Connection
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your-secure-password

# Graphiti Configuration
GRAPHITI_GROUP_ID=agent-zero-main
OPENAI_API_KEY=your-openai-api-key

# Performance Tuning
GRAPHITI_BULK_BATCH_SIZE=100
GRAPHITI_BULK_TIMEOUT=300
GRAPHITI_ENTITY_THRESHOLD=0.8
```

**Verification:**
```bash
# Test Neo4j connection
docker exec neo4j-graphiti cypher-shell -u neo4j -p your-password "RETURN 'Connection successful' as status"

# Check APOC installation
docker exec neo4j-graphiti cypher-shell -u neo4j -p your-password "RETURN apoc.version() as version"
```

## Core Features

### Entity Extraction

Graphiti automatically extracts entities from text and creates relationships:

**Input Text:**
```
"I'm learning Python for web development. Django is a popular framework that uses Python."
```

**Extracted Entities:**
- `Python` (Programming Language)
- `Web Development` (Domain)
- `Django` (Framework)
- `Framework` (Concept)

**Generated Relationships:**
- `Python` → `used_for` → `Web Development`
- `Django` → `is_a` → `Framework`
- `Django` → `uses` → `Python`

### Temporal Queries

Query information with time-based context:

```python
# Find recent discussions about Python
recent_python = await memory.search_temporal(
    query="Python programming",
    time_range="last_week",
    limit=10
)

# Track how understanding evolved
evolution = await memory.get_concept_evolution(
    entity="Python",
    time_range="last_month"
)

# Find what was discussed on specific dates
daily_topics = await memory.search_by_date(
    date="2024-06-18",
    entity_filter=["programming", "development"]
)
```

### Relationship Traversal

Explore connected concepts through the knowledge graph:

```python
# Find all concepts related to Python
related_concepts = await memory.find_related_entities(
    entity="Python",
    max_depth=2,
    relationship_types=["uses", "enables", "related_to"]
)

# Discover learning paths
learning_path = await memory.find_path(
    start_entity="Beginner Programming",
    end_entity="Web Development",
    path_type="learning_progression"
)

# Analyze concept clusters
clusters = await memory.analyze_entity_clusters(
    domain="programming",
    min_cluster_size=3
)
```

## Advanced Usage

### Bulk Knowledge Import

Efficiently import large knowledge bases:

```python
# Prepare documents for bulk import
documents = [
    {
        "content": "Python is a high-level programming language...",
        "source": "python_tutorial.pdf",
        "metadata": {"chapter": 1, "topic": "introduction"}
    },
    {
        "content": "Django is a web framework for Python...",
        "source": "django_guide.pdf", 
        "metadata": {"chapter": 3, "topic": "frameworks"}
    }
]

# Bulk import with entity extraction
results = await memory.bulk_import_documents(
    documents=documents,
    batch_size=50,
    extract_entities=True,
    extract_relationships=True,
    timeout=600
)

# Monitor progress
for result in results:
    print(f"Processed: {result['source']}")
    print(f"Entities: {len(result['entities'])}")
    print(f"Relationships: {len(result['relationships'])}")
```

### Custom Entity Types

Define custom entity types and relationships:

```python
# Configure custom entity extraction
entity_config = {
    "entity_types": {
        "programming_language": {
            "patterns": ["Python", "JavaScript", "Java"],
            "confidence_threshold": 0.9
        },
        "framework": {
            "patterns": ["Django", "React", "Spring"],
            "confidence_threshold": 0.8
        }
    },
    "relationship_types": {
        "implements": {"confidence_threshold": 0.7},
        "extends": {"confidence_threshold": 0.8},
        "depends_on": {"confidence_threshold": 0.6}
    }
}

await memory.configure_entity_extraction(entity_config)
```

### Graph Analytics

Analyze the knowledge graph structure:

```python
# Graph statistics
stats = await memory.get_graph_statistics()
print(f"Total entities: {stats['entity_count']}")
print(f"Total relationships: {stats['relationship_count']}")
print(f"Graph density: {stats['density']}")

# Find central concepts
central_entities = await memory.find_central_entities(
    metric="betweenness_centrality",
    limit=10
)

# Identify knowledge gaps
gaps = await memory.identify_knowledge_gaps(
    domain="programming",
    min_connection_strength=0.5
)

# Community detection
communities = await memory.detect_communities(
    algorithm="louvain",
    resolution=1.0
)
```

## Performance Optimization

### Indexing Strategy

**Automatic Indexing:**
```cypher
-- Graphiti automatically creates these indices
CREATE INDEX entity_name_index FOR (e:Entity) ON (e.name);
CREATE INDEX episode_timestamp_index FOR (ep:Episode) ON (ep.timestamp);
CREATE INDEX relationship_type_index FOR ()-[r:RELATIONSHIP]-() ON (r.type);
```

**Custom Indexing:**
```python
# Add custom indices for specific use cases
await memory.create_custom_index(
    node_type="Entity",
    property="domain",
    index_type="text"
)

await memory.create_custom_index(
    relationship_type="RELATED_TO",
    property="confidence",
    index_type="range"
)
```

### Query Optimization

**Efficient Queries:**
```python
# Use specific entity types to narrow search
results = await memory.search_entities(
    query="web development",
    entity_types=["framework", "library"],
    limit=20
)

# Limit relationship traversal depth
related = await memory.find_related_entities(
    entity="Python",
    max_depth=2,  # Limit depth to avoid expensive traversals
    min_confidence=0.7
)

# Use temporal bounds to limit search space
recent = await memory.search_temporal(
    query="programming concepts",
    start_time="2024-06-01",
    end_time="2024-06-18"
)
```

### Memory Management

**Cleanup Strategies:**
```python
# Remove low-confidence relationships
await memory.cleanup_weak_relationships(
    confidence_threshold=0.3,
    age_threshold="30_days"
)

# Consolidate duplicate entities
await memory.consolidate_duplicate_entities(
    similarity_threshold=0.9,
    merge_strategy="highest_confidence"
)

# Archive old episodes
await memory.archive_old_episodes(
    age_threshold="90_days",
    importance_threshold=0.2
)
```

## Monitoring and Maintenance

### Health Checks

```python
# Check system health
health = await memory.check_health()
print(f"Neo4j status: {health['neo4j_status']}")
print(f"Index health: {health['index_health']}")
print(f"Query performance: {health['avg_query_time']}")

# Validate graph integrity
integrity = await memory.validate_graph_integrity()
if not integrity['is_valid']:
    print(f"Issues found: {integrity['issues']}")
```

### Performance Monitoring

```python
# Query performance metrics
metrics = await memory.get_performance_metrics()
print(f"Average query time: {metrics['avg_query_time']}")
print(f"Slow queries: {metrics['slow_query_count']}")
print(f"Memory usage: {metrics['memory_usage']}")

# Entity growth tracking
growth = await memory.get_growth_metrics(
    time_range="last_month"
)
print(f"New entities: {growth['new_entities']}")
print(f"New relationships: {growth['new_relationships']}")
```

## Troubleshooting

### Common Issues

**Connection Problems:**
```bash
# Test Neo4j connectivity
docker logs neo4j-graphiti

# Check port accessibility
telnet localhost 7687

# Verify authentication
cypher-shell -a bolt://localhost:7687 -u neo4j -p your-password
```

**Performance Issues:**
```python
# Check index usage
await memory.analyze_query_performance(
    query="MATCH (e:Entity) WHERE e.name CONTAINS 'Python' RETURN e"
)

# Monitor memory usage
await memory.get_memory_usage_stats()

# Identify slow queries
slow_queries = await memory.get_slow_queries(
    threshold="1000ms",
    limit=10
)
```

**Data Consistency:**
```python
# Validate entity relationships
inconsistencies = await memory.find_data_inconsistencies()

# Repair broken relationships
await memory.repair_broken_relationships()

# Rebuild corrupted indices
await memory.rebuild_indices()
```

## Migration and Backup

### Data Export
```python
# Export knowledge graph
export_data = await memory.export_graph(
    format="json",
    include_embeddings=False
)

# Export specific domains
domain_data = await memory.export_domain(
    domain="programming",
    format="cypher"
)
```

### Data Import
```python
# Import from backup
await memory.import_graph(
    data=backup_data,
    merge_strategy="update_existing"
)

# Migrate from FAISS
await memory.migrate_from_faiss(
    faiss_data_path="/path/to/faiss/data",
    extract_entities=True
)
```

## Next Steps

- **[Memory Configuration](configuration.md)**: Advanced configuration options
- **[Knowledge Management](knowledge-management.md)**: Document processing and import
- **[FAISS Backend](faiss-backend.md)**: Alternative memory backend
- **[API Reference](../developer-guide/api-reference.md)**: Complete API documentation
