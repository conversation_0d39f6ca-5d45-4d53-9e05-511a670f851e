import asyncio
import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
from typing import Dict, Any, List

# Attempt to import the engine and its dependencies
try:
    from python.helpers.intelligent_classification_engine import IntelligentClassificationEngine
    from python.helpers.classification_config import ClassificationConfig, ClassificationStrategy, ClassificationThresholds, CacheConfiguration
    from python.helpers.classification_engine import ClassificationResult, ClassificationMetrics, ClassificationStatus # Assuming these are base classes/structs
    # For preference related objects that the engine now interacts with:
    from python.helpers.user_preferences import UserPreferenceManager, PreferenceItem, PreferenceType
    from python.helpers.behavioral_analyzer import BehavioralPatternAnalyzer
except ImportError as e:
    print(f"test_intelligent_classification_engine.py: Error importing modules: {e}. Tests may fail or not run.")
    # Minimal placeholders if imports fail
    class IntelligentClassificationEngine: pass
    class ClassificationConfig: pass
    class ClassificationStrategy(MagicMock): pass # Mocking enums
    class ClassificationResult(MagicMock): pass
    class ClassificationMetrics(MagicMock): pass
    class ClassificationStatus(MagicMock): pass
    class UserPreferenceManager(AsyncMock): pass # AsyncMock for methods like get_preference
    class PreferenceItem(MagicMock): pass
    class PreferenceType(MagicMock): pass # Mocking enums
    class BehavioralPatternAnalyzer(AsyncMock): pass # AsyncMock for methods like record_interaction
    # Keep placeholder aliases for tests if needed, but ensure main setup uses correct names
    class ThresholdsConfigPlaceholder(MagicMock):pass # Renamed placeholder to avoid conflict
    class CacheConfigPlaceholder(MagicMock):pass # Renamed placeholder


# Helper to run async tests
def async_test(coro):
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

class TestIntelligentClassificationEngineWithPreferences(unittest.TestCase):

    def setUp(self):
        # Create a default config for the engine for each test
        self.test_config = ClassificationConfig(
            cache=CacheConfiguration(enabled=False, l1_memory_size=100), # Disable cache for most unit tests unless testing cache
            thresholds=ClassificationThresholds() # Default thresholds
        )
        self.engine = IntelligentClassificationEngine(config=self.test_config)
        # Ensure engine is initialized for tests that need it (most will)
        # Some tests might want to test initialization itself.
        # For simplicity, let's assume it's initialized here.
        # If initialize has significant async ops, setUp might need to be async_test itself.
        # However, initialize() in the provided engine code is synchronous after the first await.
        # Using asyncio.run() for setup/teardown async calls if they are not part of the main test loop.
        if not self.engine.is_initialized:
             asyncio.run(self.engine.initialize())


        self.test_user_id = "test_user_engine"
        self.test_content = "This is a test document about AI in healthcare and financial technology."
        self.test_metadata = {"source": "test_suite", "content_type": "text_document"}

        # Mock instances for UserPreferenceManager and BehavioralPatternAnalyzer
        self.mock_upm = AsyncMock(spec=UserPreferenceManager)
        self.mock_bpa = AsyncMock(spec=BehavioralPatternAnalyzer)

        # Common default preference values
        self.mock_upm.preferences = {} # Default to no specific preferences
        # Adjust side_effect to handle PreferenceItem structure if that's what UPM returns
        async def mock_get_preference_side_effect(key, default):
            item = self.mock_upm.preferences.get(key)
            if item:
                # Assuming item is a PreferenceItem or similar structure with a 'value'
                return item.value if hasattr(item, 'value') else item # Fallback if it's just a direct value
            return default

        self.mock_upm.get_preference = AsyncMock(side_effect=mock_get_preference_side_effect)


    def tearDown(self):
        # Clean up any resources if necessary
        # Stop any patches started with self.patcher.start() if not using with context manager
        patch.stopall()
        if self.engine.is_initialized: # Ensure it was initialized before trying to shut down
            asyncio.run(self.engine.shutdown())


    @async_test
    async def test_get_user_preference_manager_caching(self):
        # Test that UPM instances are created and cached per user_id
        with patch('python.helpers.intelligent_classification_engine.UserPreferenceManager', new=AsyncMock(return_value=self.mock_upm)) as MockUpmClassForCacheTest:
            # Clear internal dict for this test to ensure clean state
            self.engine.preference_managers = {}

            upm_instance1 = await self.engine._get_user_preference_manager("user1_cache")
            self.assertIsNotNone(upm_instance1)
            self.assertIn("user1_cache", self.engine.preference_managers)
            self.assertEqual(self.engine.preference_managers["user1_cache"], upm_instance1)
            MockUpmClassForCacheTest.assert_called_once_with("user1_cache")


            # Call again for same user, should return cached instance
            MockUpmClassForCacheTest.reset_mock() # Reset call count for the class mock
            upm_instance2 = await self.engine._get_user_preference_manager("user1_cache")
            self.assertIs(upm_instance1, upm_instance2)
            MockUpmClassForCacheTest.assert_not_called()


    @async_test
    async def test_get_behavioral_analyzer_caching(self):
        # Test that BPA instances are created and cached per user_id
        with patch('python.helpers.intelligent_classification_engine.BehavioralPatternAnalyzer', new=AsyncMock(return_value=self.mock_bpa)) as MockBpaClassForCacheTest:
            self.engine.behavioral_analyzers = {} # Clear for test

            bpa_instance1 = await self.engine._get_behavioral_analyzer("user1_bpa_cache")
            self.assertIsNotNone(bpa_instance1)
            self.assertIn("user1_bpa_cache", self.engine.behavioral_analyzers)
            MockBpaClassForCacheTest.assert_called_once_with("user1_bpa_cache")

            MockBpaClassForCacheTest.reset_mock()
            bpa_instance2 = await self.engine._get_behavioral_analyzer("user1_bpa_cache")
            self.assertIs(bpa_instance1, bpa_instance2)
            MockBpaClassForCacheTest.assert_not_called()


    @async_test
    async def test_get_domain_weights_from_preferences(self):
        # Setup mock UPM with specific PreferenceItem instances
        # This requires PreferenceItem and PreferenceType to be properly defined or mocked for instantiation
        # For simplicity, if PreferenceItem is complex, mock its structure or use dicts if _get_domain_weights handles it

        # Create mock PreferenceItem instances (or dicts if your _get_domain_weights is robust to it)
        # Ensure PreferenceType.DOMAIN_WEIGHT and PreferenceType.GENERAL are valid (e.g., actual enums or mocks)
        mock_pref_item_tech = MagicMock(spec=PreferenceItem)
        mock_pref_item_tech.value = "0.9" # Stored as string
        mock_pref_item_tech.preference_type = PreferenceType.DOMAIN_WEIGHT

        mock_pref_item_finance = MagicMock(spec=PreferenceItem)
        mock_pref_item_finance.value = 0.75 # Stored as float
        mock_pref_item_finance.preference_type = PreferenceType.DOMAIN_WEIGHT

        mock_pref_item_general = MagicMock(spec=PreferenceItem)
        mock_pref_item_general.value = "some_value"
        mock_pref_item_general.preference_type = PreferenceType.GENERAL


        self.mock_upm.preferences = {
            "domain_weight_tech": mock_pref_item_tech,
            "domain_weight_finance": mock_pref_item_finance,
            "some_other_pref": mock_pref_item_general
        }

        domain_weights = await self.engine._get_domain_weights(self.mock_upm)
        self.assertEqual(domain_weights.get("tech"), 0.9)
        self.assertEqual(domain_weights.get("finance"), 0.75)
        self.assertNotIn("some_other_pref", domain_weights) # Should only pick up DOMAIN_WEIGHT
        self.assertEqual(len(domain_weights), 2)

    @async_test
    async def test_adjust_domain_specificity_with_weights(self):
        content = "A document about cloud technology and software development."
        entities = []

        self.engine.domain_keywords = {
            "technology": ["cloud", "software", "api"],
            "finance": ["stock", "ipo"]
        }

        domain_weights_tech_focus = {"technology": 0.8, "finance": 0.2}
        specificity_tech = await self.engine._adjust_domain_specificity(content, entities, domain_weights_tech_focus)

        domain_weights_finance_focus = {"technology": 0.2, "finance": 0.8}
        specificity_finance = await self.engine._adjust_domain_specificity(content, entities, domain_weights_finance_focus)

        self.assertTrue(0.0 <= specificity_tech <= 1.0)
        self.assertTrue(0.0 <= specificity_finance <= 1.0)
        self.assertGreater(specificity_tech, specificity_finance)


    @async_test
    async def test_adjust_privacy_scoring_preference(self):
        base_score_high_pii = 3
        base_score_no_pii = 0

        self.assertEqual(self.engine._adjust_privacy_scoring(base_score_high_pii, "high", True), 5)
        self.assertEqual(self.engine._adjust_privacy_scoring(base_score_no_pii, "high", False), 0)

        self.assertEqual(self.engine._adjust_privacy_scoring(base_score_high_pii, "medium", True), 4)

        self.assertEqual(self.engine._adjust_privacy_scoring(base_score_high_pii, "low", True), 2)
        self.assertEqual(self.engine._adjust_privacy_scoring(5, "low", True), 4)

    @async_test
    async def test_apply_user_preferences_modifies_metrics(self):
        base_metrics = ClassificationMetrics( # Assuming ClassificationMetrics is a class that can be instantiated
            semantic_overlap=0.5, entity_confidence=0.8, domain_specificity=0.3,
            relationship_density=0.0, privacy_score=1, privacy_flags=True,
            interconnectedness=0.0, temporal_relevance=0.0, related_entities_count=0,
            content_length=100, entity_types=[]
        )

        # Mock PreferenceItem creation or structure
        mock_privacy_pref = MagicMock(spec=PreferenceItem)
        mock_privacy_pref.value = "high"
        mock_privacy_pref.preference_type = PreferenceType.PRIVACY_SENSITIVITY

        mock_tech_pref = MagicMock(spec=PreferenceItem)
        mock_tech_pref.value = 0.9
        mock_tech_pref.preference_type = PreferenceType.DOMAIN_WEIGHT

        self.mock_upm.preferences = {
            "privacy_sensitivity_level": mock_privacy_pref,
            "domain_weight_tech": mock_tech_pref
        }
        content_for_test = "tech keyword one two"
        self.engine.domain_keywords = {"tech": ["keyword"]}

        with patch.object(self.engine, '_adjust_domain_specificity', new_callable=AsyncMock, return_value=0.85) as mock_adj_domain, \
             patch.object(self.engine, '_adjust_privacy_scoring', return_value=4) as mock_adj_privacy:

            adjusted_metrics = await self.engine._apply_user_preferences(base_metrics, self.mock_upm, content_for_test, [])

            mock_adj_domain.assert_called_once()
            mock_adj_privacy.assert_called_once_with(base_metrics.privacy_score, "high", base_metrics.privacy_flags)

            self.assertEqual(adjusted_metrics.domain_specificity, 0.85)
            self.assertEqual(adjusted_metrics.privacy_score, 4)
            self.assertEqual(adjusted_metrics.semantic_overlap, 0.5)


    @async_test
    async def test_classify_content_with_preferences_flow(self):
        user_id = "pref_flow_user"

        with patch.object(self.engine, '_get_user_preference_manager', return_value=self.mock_upm) as mock_get_upm, \
             patch.object(self.engine, '_get_behavioral_analyzer', return_value=self.mock_bpa) as mock_get_bpa, \
             patch.object(self.engine, '_apply_user_preferences', new_callable=AsyncMock) as mock_apply_prefs, \
             patch.object(self.engine, '_record_classification_interaction', new_callable=AsyncMock) as mock_record_interaction:

            expected_adjusted_metrics = ClassificationMetrics(domain_specificity=0.99, privacy_score=5, privacy_flags=True, semantic_overlap=0.0, entity_confidence=0.0, relationship_density=0.0, interconnectedness=0.0, temporal_relevance=0.0, related_entities_count=0,content_length=0,entity_types=[])
            mock_apply_prefs.return_value = expected_adjusted_metrics

            result = await self.engine.classify_content_with_preferences(user_id, self.test_content, self.test_metadata)

            mock_get_upm.assert_called_once_with(user_id)

            mock_apply_prefs.assert_called_once()
            call_args_apply_prefs = mock_apply_prefs.call_args[0]
            self.assertIsInstance(call_args_apply_prefs[0], ClassificationMetrics)
            self.assertEqual(call_args_apply_prefs[1], self.mock_upm)

            self.assertEqual(result.metrics.domain_specificity, 0.99)
            self.assertEqual(result.metrics.privacy_score, 5)

            mock_record_interaction.assert_called_once()
            call_args_record_interaction = mock_record_interaction.call_args[0]
            self.assertEqual(call_args_record_interaction[0], user_id)
            self.assertEqual(call_args_record_interaction[1], result)
            self.assertIsInstance(call_args_record_interaction[2], dict)

            self.assertFalse(result.cache_hit)


    @async_test
    async def test_record_classification_interaction_calls_analyzer(self):
        user_id = "record_user"
        mock_result = MagicMock(spec=ClassificationResult)
        mock_result.classification_id = "cls_test_123"
        # Ensure strategy is a mock that has a 'value' attribute if accessed
        mock_result.strategy = MagicMock()
        mock_result.strategy.value = "TEST_STRATEGY"
        mock_result.confidence = 0.88
        # Ensure metrics is a mock that can be converted to dict if needed by record_interaction
        mock_result.metrics = MagicMock()
        mock_result.metrics.__dict__ = {"sample_metric": 1}


        content_info = {'length': 123, 'type': 'test'}

        with patch.object(self.engine, '_get_behavioral_analyzer', return_value=self.mock_bpa) as mock_get_bpa:
            await self.engine._record_classification_interaction(user_id, mock_result, content_info)

            mock_get_bpa.assert_called_once_with(user_id)
            self.mock_bpa.record_interaction.assert_called_once()


    @async_test
    async def test_classify_content_with_preferences_caching_if_key_includes_prefs(self):
        self.engine.config.cache.enabled = True
        self.engine.cache = {}

        user_id_caching = "user_cache_prefs"

        mock_tech_pref_cache = MagicMock(spec=PreferenceItem)
        mock_tech_pref_cache.value = 0.9
        mock_tech_pref_cache.preference_type = PreferenceType.DOMAIN_WEIGHT
        self.mock_upm.preferences = {"domain_weight_tech": mock_tech_pref_cache}

        with patch.object(self.engine, '_get_user_preference_manager', return_value=self.mock_upm), \
             patch.object(self.engine, '_generate_cache_key') as mock_gen_cache_key:

            cache_key_user_prefs = "cache_key_for_user_prefs"
            mock_gen_cache_key.return_value = cache_key_user_prefs

            result1 = await self.engine.classify_content_with_preferences(user_id_caching, self.test_content, self.test_metadata)
            self.assertFalse(result1.cache_hit)
            self.assertIn(cache_key_user_prefs, self.engine.cache)

            result2 = await self.engine.classify_content_with_preferences(user_id_caching, self.test_content, self.test_metadata)
            self.assertTrue(result2.cache_hit)
            self.assertEqual(result1.classification_id, result2.classification_id)

            self.assertEqual(mock_gen_cache_key.call_count, 2)

        self.engine.config.cache.enabled = False

if __name__ == '__main__':
    unittest.main()
