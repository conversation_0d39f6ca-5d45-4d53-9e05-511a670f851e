"""
Three-tier hierarchical memory system implementation
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple, Set, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone # Added timezone
from enum import Enum # Added Enum import
import asyncio # Not explicitly used in markdown but good for async stubs
import time
import json # Not explicitly used in markdown code, but good for potential serialization
import hashlib # Not explicitly used in markdown code, but good for ID generation
from collections import deque, defaultdict

# Attempt to import classification_config, provide a fallback for isolated testing
try:
    from .classification_config import classification_config
except ImportError:
    print("WARN: memory_tiers.py: classification_config not found, using placeholder for performance targets.")
    @dataclass
    class PerfTargetsPlaceholder:
        working_memory_response_ms: int = 100
        long_term_memory_response_ms: int = 500
        episodic_memory_response_ms: int = 1000 # Added for completeness
    @dataclass
    class ClassificationConfigPlaceholder:
        performance_targets: PerfTargetsPlaceholder = field(default_factory=PerfTargetsPlaceholder)
    classification_config = ClassificationConfigPlaceholder()


class MemoryTier(Enum):
    """Memory tier types"""
    WORKING = "working"
    LONG_TERM = "long_term"
    EPISODIC = "episodic"

@dataclass
class MemoryItem:
    """Individual memory item with metadata"""
    id: str
    content: Any # Can be text, structured data, embeddings, etc.
    metadata: Dict[str, Any]
    tier: MemoryTier
    created_at: datetime
    last_accessed: datetime
    access_count: int
    importance_score: float # Normalized 0.0 to 1.0

    size_bytes: int = 0
    tags: List[str] = field(default_factory=list)
    relationships: List[str] = field(default_factory=list) # e.g., IDs of related items

    def __post_init__(self):
        if self.size_bytes == 0 and isinstance(self.content, (str, bytes)):
            self.size_bytes = len(str(self.content).encode('utf-8'))
        elif self.size_bytes == 0: # Fallback for non-string/bytes content
            try:
                self.size_bytes = len(json.dumps(self.content).encode('utf-8'))
            except (TypeError, OverflowError): # Handle non-serializable content gracefully
                self.size_bytes = 100 # Arbitrary fallback size


@dataclass
class MemoryQuery:
    """Query for memory retrieval"""
    query_text: str # Can be natural language, keywords, or a structured query
    tier_preference: Optional[MemoryTier] = None
    time_range: Optional[Tuple[datetime, datetime]] = None
    importance_threshold: float = 0.0 # Minimum importance for items to be considered
    limit: int = 10
    include_metadata: bool = True # Whether to return full metadata with items

    # Advanced query options
    semantic_similarity_threshold: float = 0.5 # For vector/semantic search components
    exact_match: bool = False # If query_text should be an exact match
    fuzzy_search: bool = True # Allow fuzzy matching for keywords
    target_entity_types: Optional[List[str]] = None # e.g., ["PERSON", "ORG"]
    required_tags: Optional[List[str]] = None


class MemoryTierInterface(ABC):
    """Abstract interface for memory tiers"""

    @abstractmethod
    async def store(self, item: MemoryItem) -> str:
        """Store memory item and return item ID"""
        pass

    @abstractmethod
    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Retrieve memory items matching query"""
        pass

    @abstractmethod
    async def update_access(self, item_id: str, access_time: datetime) -> None:
        """Update access statistics for item"""
        pass

    @abstractmethod
    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get tier statistics"""
        pass

    @abstractmethod
    async def cleanup_expired(self) -> int:
        """Clean up expired items and return count removed"""
        pass

class WorkingMemoryTier(MemoryTierInterface):
    """
    Working Memory: Recent, frequently accessed items.
    Uses LRU and importance for eviction.
    Target: <100ms response time (95th percentile)
    """
    def __init__(self, max_items: int = 5000, retention_days: int = 7): # Reduced max_items from doc for typical local dev
        self.max_items = max_items
        self.retention_days = retention_days
        self.items: Dict[str, MemoryItem] = {}
        self.access_order: deque[str] = deque() # Stores item IDs for LRU

        # Performance tracking
        self.query_count = 0
        self.hit_count = 0 # Successful retrievals
        self.total_response_time_ms = 0.0 # Sum of response times in ms

    async def store(self, item: MemoryItem) -> str:
        if item.id in self.items: # Item already exists, update it (refresh)
            existing_item = self.items[item.id]
            existing_item.content = item.content # Update content
            existing_item.metadata = item.metadata # Update metadata
            existing_item.importance_score = item.importance_score # Update importance
            existing_item.last_accessed = item.created_at # Treat store as an access
            existing_item.access_count +=1
            self.access_order.remove(item.id) # Move to end of LRU
            self.access_order.append(item.id)
        else: # New item
            if len(self.items) >= self.max_items:
                await self._evict_items(len(self.items) - self.max_items + 1)
            self.items[item.id] = item
            self.access_order.append(item.id)
        return item.id

    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        start_time_ns = time.perf_counter_ns()
        self.query_count += 1

        if not query.query_text.strip():
            return []

        candidate_items = []
        query_text_lower = query.query_text.lower()

        for item_id in reversed(self.access_order): # Check more recent items first
            item = self.items.get(item_id)
            if not item: continue

            if item.importance_score < query.importance_threshold:
                continue
            if query.time_range:
                start_tr, end_tr = query.time_range
                if not (start_tr <= item.created_at <= end_tr):
                    continue
            if query.required_tags and not all(tag in item.tags for tag in query.required_tags):
                continue

            relevance = self._calculate_relevance(item, query_text_lower, query)
            if relevance > 0: # Using 0 as a threshold, could be configurable
                candidate_items.append((item, relevance))

        candidate_items.sort(key=lambda x: x[1], reverse=True) # Sort by relevance

        result_items = []
        for item_obj, _ in candidate_items[:query.limit]:
            await self.update_access(item_obj.id, datetime.now(timezone.utc))
            result_items.append(item_obj)

        response_time_ms = (time.perf_counter_ns() - start_time_ns) / 1_000_000
        self.total_response_time_ms += response_time_ms

        if result_items: self.hit_count += 1

        if response_time_ms > classification_config.performance_targets.working_memory_response_ms:
            print(f"Warning: Working memory query '{query.query_text[:50]}...' took {response_time_ms:.2f}ms, "
                  f"target is {classification_config.performance_targets.working_memory_response_ms}ms")
        return result_items

    def _calculate_relevance(self, item: MemoryItem, query_text_lower: str, query: MemoryQuery) -> float:
        content_str = str(item.content).lower()
        score = 0.0

        if query.exact_match:
            if query_text_lower == content_str: score = 1.0
        else:
            # Simple keyword matching, could be improved with fuzzywuzzy or TF-IDF for Working Memory
            query_words = set(query_text_lower.split())
            content_words = set(content_str.split())
            common_words = query_words.intersection(content_words)
            if query_words: # Avoid division by zero
                 score = len(common_words) / len(query_words)

        # Factor in importance and recency (last_accessed already part of sort key in retrieve)
        score = score * 0.7 + item.importance_score * 0.3
        return score


    async def update_access(self, item_id: str, access_time: datetime) -> None:
        if item_id in self.items:
            item = self.items[item_id]
            item.last_accessed = access_time
            item.access_count += 1
            if item_id in self.access_order: # Should always be true if in items
                self.access_order.remove(item_id)
            self.access_order.append(item_id) # Move to end (most recently accessed)

    async def get_tier_stats(self) -> Dict[str, Any]:
        oldest_item_age_hours = 0
        if self.items and self.access_order:
            oldest_item_id = self.access_order[0] # LRU item is at the start of deque
            oldest_item = self.items[oldest_item_id]
            oldest_item_age_hours = (datetime.now(timezone.utc) - oldest_item.created_at).total_seconds() / 3600

        return {
            'tier': MemoryTier.WORKING.value,
            'item_count': len(self.items),
            'capacity_utilization': len(self.items) / self.max_items if self.max_items > 0 else 1.0,
            'avg_importance': sum(it.importance_score for it in self.items.values()) / len(self.items) if self.items else 0.0,
            'oldest_item_age_hours': oldest_item_age_hours,
            'hit_rate': self.hit_count / self.query_count if self.query_count > 0 else 0.0,
            'avg_response_time_ms': self.total_response_time_ms / self.query_count if self.query_count > 0 else 0.0,
            'total_queries': self.query_count,
        }

    async def cleanup_expired(self) -> int:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.retention_days)
        expired_ids = [
            item_id for item_id, item in self.items.items()
            if item.last_accessed < cutoff_date # Changed from created_at to last_accessed for true LRU style
        ]
        for item_id in expired_ids:
            await self._remove_item(item_id)
        return len(expired_ids)

    async def _evict_items(self, count: int):
        """Evicts 'count' least recently used items."""
        evicted_count = 0
        for _ in range(count):
            if not self.access_order: break
            lru_item_id = self.access_order.popleft() # Remove from front (LRU)
            if lru_item_id in self.items:
                del self.items[lru_item_id]
                evicted_count += 1
        # print(f"WorkingMemory: Evicted {evicted_count} items.")

    async def _remove_item(self, item_id: str): # Not directly in markdown, but good helper
        if item_id in self.items:
            del self.items[item_id]
            if item_id in self.access_order:
                self.access_order.remove(item_id)

class LongTermMemoryTier(MemoryTierInterface):
    """
    Long-term Memory: Semantic clusters with consolidated knowledge.
    Target: <500ms retrieval.
    """
    def __init__(self, consolidation_threshold: int = 10, similarity_threshold: float = 0.6):
        self.clusters: Dict[str, Dict[str, Any]] = {}  # cluster_id -> cluster_data
        self.item_to_cluster: Dict[str, str] = {}      # item_id -> cluster_id
        self.semantic_index: Dict[str, List[str]] = defaultdict(list) # concept -> list of cluster_ids
        self.consolidation_threshold = consolidation_threshold
        self.similarity_threshold = similarity_threshold # For finding existing cluster
        self.query_count = 0
        self.total_response_time_ms = 0.0

    async def store(self, item: MemoryItem) -> str:
        cluster_id = await self._find_or_create_cluster(item)

        if cluster_id not in self.clusters:
            self.clusters[cluster_id] = {
                'id': cluster_id,
                'items': {},
                'consolidated_summary': '',
                'concepts': set(),
                'created_at': datetime.now(timezone.utc),
                'last_updated': datetime.now(timezone.utc),
                'importance_sum': 0.0 # For avg importance
            }

        cluster = self.clusters[cluster_id]
        cluster['items'][item.id] = item
        cluster['last_updated'] = datetime.now(timezone.utc)
        cluster['importance_sum'] += item.importance_score
        self.item_to_cluster[item.id] = cluster_id

        await self._update_cluster_concepts(cluster_id, item)

        if len(cluster['items']) >= self.consolidation_threshold and not cluster['consolidated_summary']:
            await self._consolidate_cluster(cluster_id)
        return item.id

    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        start_time_ns = time.perf_counter_ns()
        self.query_count += 1

        relevant_cluster_scores = await self._find_relevant_clusters(query)
        candidate_items_with_scores: List[Tuple[MemoryItem, float]] = []

        for cluster_id, cluster_relevance_score in relevant_cluster_scores:
            cluster = self.clusters.get(cluster_id)
            if not cluster: continue

            for item_id in cluster['items']:
                item = cluster['items'][item_id]
                if item.importance_score < query.importance_threshold: continue
                if query.time_range:
                    start_tr, end_tr = query.time_range
                    if not (start_tr <= item.created_at <= end_tr): continue
                if query.required_tags and not all(tag in item.tags for tag in query.required_tags):
                    continue

                item_relevance_to_query = await self._calculate_semantic_relevance(item, query) # Added await
                # Score as per markdown logic: product of item's semantic relevance to query and cluster's relevance to query
                item_score = item_relevance_to_query * cluster_relevance_score

                # The markdown does not show an explicit threshold check here,
                # but relies on sorting by this item_score and then limiting.
                # If item_score is very low, it might naturally fall out after sorting.
                # Let's keep items if their score is > 0 for now, or simply collect all and sort.
                # For testing, let's ensure we add if score > 0, to avoid all zero scores if concepts don't match at all.
                if item_score > 0: # Only add if there's some relevance
                    candidate_items_with_scores.append((item, item_score))

        candidate_items_with_scores.sort(key=lambda x: x[1], reverse=True)

        result_items = []
        for item_obj, _ in candidate_items_with_scores[:query.limit]:
            await self.update_access(item_obj.id, datetime.now(timezone.utc))
            result_items.append(item_obj)

        response_time_ms = (time.perf_counter_ns() - start_time_ns) / 1_000_000
        self.total_response_time_ms += response_time_ms

        if response_time_ms > classification_config.performance_targets.long_term_memory_response_ms:
             print(f"Warning: Long-term memory query '{query.query_text[:50]}...' took {response_time_ms:.2f}ms, "
                  f"target is {classification_config.performance_targets.long_term_memory_response_ms}ms")
        return result_items

    async def update_access(self, item_id: str, access_time: datetime) -> None:
        cluster_id = self.item_to_cluster.get(item_id)
        if cluster_id and cluster_id in self.clusters:
            cluster = self.clusters[cluster_id]
            item = cluster['items'].get(item_id)
            if item:
                item.last_accessed = access_time
                item.access_count += 1
                cluster['last_updated'] = access_time # Also update cluster's last_updated

    async def get_tier_stats(self) -> Dict[str, Any]:
        total_items = sum(len(c['items']) for c in self.clusters.values())
        consolidated_clusters = sum(1 for c in self.clusters.values() if c['consolidated_summary'])
        return {
            'tier': MemoryTier.LONG_TERM.value,
            'cluster_count': len(self.clusters),
            'total_items': total_items,
            'consolidated_clusters': consolidated_clusters,
            'avg_cluster_size': total_items / len(self.clusters) if self.clusters else 0,
            'semantic_concepts_indexed': len(self.semantic_index),
            'avg_response_time_ms': self.total_response_time_ms / self.query_count if self.query_count > 0 else 0.0,
        }

    async def cleanup_expired(self) -> int: # More advanced cleanup based on cluster activity/importance
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=90) # Example: 90 days for LTM clusters
        expired_item_count = 0
        clusters_to_remove = []

        for cid, cluster in self.clusters.items():
            if cluster['last_updated'] < cutoff_date:
                # Consider average importance of items in cluster
                avg_importance = (cluster['importance_sum'] / len(cluster['items'])) if cluster['items'] else 0
                if avg_importance < 0.2: # Threshold for removing low-importance old clusters
                    clusters_to_remove.append(cid)
                    expired_item_count += len(cluster['items'])

        for cid in clusters_to_remove:
            await self._remove_cluster(cid)
        return expired_item_count

    async def _find_or_create_cluster(self, item: MemoryItem) -> str:
        item_concepts = await self._extract_concepts(item.content)
        if not item_concepts: # If no concepts, assign to a generic cluster or handle error
            return f"generic_cluster_{int(time.time())}"

        best_cluster_id: Optional[str] = None
        max_similarity: float = 0.0

        for cid, cluster_data in self.clusters.items():
            similarity = self._calculate_cluster_similarity(item_concepts, cluster_data['concepts'])
            if similarity > max_similarity:
                max_similarity = similarity
                best_cluster_id = cid

        if best_cluster_id and max_similarity >= self.similarity_threshold:
            return best_cluster_id
        else: # Create new cluster
            new_cid = f"cluster_{len(self.clusters)}_{int(time.time_ns() / 1000)}"
            self.clusters[new_cid] = {
                'id': new_cid, 'items': {}, 'consolidated_summary': '',
                'concepts': set(), 'created_at': datetime.now(timezone.utc),
                'last_updated': datetime.now(timezone.utc), 'importance_sum': 0.0
            }
            return new_cid

    async def _extract_concepts(self, content: Any) -> Set[str]: # Changed from content: str
        # Simple keyword extraction, placeholder for more advanced NLP
        text_content = str(content).lower()
        # Basic stopword list, can be expanded
        stopwords = {"is", "a", "the", "and", "of", "to", "in", "it", "that", "this"}
        words = [word for word in text_content.split() if word.isalnum() and word not in stopwords and len(word) > 3]
        await asyncio.sleep(0.001) # simulate async work
        return set(words[:15]) # Take top N words as concepts for simplicity

    def _calculate_cluster_similarity(self, item_concepts: Set[str], cluster_concepts: Set[str]) -> float:
        if not item_concepts or not cluster_concepts: return 0.0
        intersection = len(item_concepts.intersection(cluster_concepts))
        union = len(item_concepts.union(cluster_concepts))
        return intersection / union if union > 0 else 0.0

    async def _update_cluster_concepts(self, cluster_id: str, item: MemoryItem):
        item_concepts = await self._extract_concepts(item.content)
        self.clusters[cluster_id]['concepts'].update(item_concepts)
        for concept in item_concepts:
            if cluster_id not in self.semantic_index[concept]: # Avoid duplicates
                self.semantic_index[concept].append(cluster_id)

    async def _consolidate_cluster(self, cluster_id: str):
        cluster = self.clusters[cluster_id]
        if cluster['consolidated_summary']: return # Already done

        # Placeholder: simple summarization (e.g., join first few words of each item's content)
        # A real implementation would use an LLM or text summarization algorithm.
        summary_parts = []
        for item_obj in list(cluster['items'].values())[:5]: # Summarize from first 5 items
            summary_parts.append(str(item_obj.content)[:50]) # First 50 chars

        cluster['consolidated_summary'] = f"Summary of {len(cluster['items'])} items. Concepts: {', '.join(list(cluster['concepts'])[:3])}. Content excerpts: {' | '.join(summary_parts)}..."
        cluster['last_updated'] = datetime.now(timezone.utc)
        # print(f"Consolidated cluster {cluster_id}")

    async def _find_relevant_clusters(self, query: MemoryQuery) -> List[Tuple[str, float]]:
        query_concepts = await self._extract_concepts(query.query_text)
        cluster_scores: Dict[str, float] = defaultdict(float)

        for concept in query_concepts:
            for cluster_id in self.semantic_index.get(concept, []):
                # Score based on number of matching concepts with query
                cluster_scores[cluster_id] += 1.0

        # Normalize scores (e.g., by total concepts in cluster or query)
        for cid in list(cluster_scores.keys()): # Iterate over list of keys for safe modification
            cluster_data = self.clusters.get(cid)
            if cluster_data and cluster_data['concepts']: # Ensure concepts exist
                 cluster_scores[cid] /= len(cluster_data['concepts']) # Normalize by cluster concept count
            else: # Should not happen if _update_cluster_concepts is correct
                del cluster_scores[cid]


        # Return top N clusters, sorted by score
        return sorted(cluster_scores.items(), key=lambda item: item[1], reverse=True)[:10]


    async def _calculate_semantic_relevance(self, item: MemoryItem, query: MemoryQuery) -> float: # Made async
        # This is a simplified version. Real version would use embeddings.
        item_concepts = await self._extract_concepts(item.content) # Used await
        query_concepts = await self._extract_concepts(query.query_text) # Used await

        if not item_concepts or not query_concepts: return 0.0

        intersection = len(item_concepts.intersection(query_concepts))
        union = len(item_concepts.union(query_concepts))
        base_similarity = intersection / union if union > 0 else 0.0

        # Consider importance and recency for final relevance
        recency_score = 1.0 - min(1.0, (datetime.now(timezone.utc) - item.last_accessed).days / 30.0) # Full score if accessed today, decays over 30 days
        return base_similarity * 0.6 + item.importance_score * 0.3 + recency_score * 0.1


    async def _remove_cluster(self, cluster_id: str):
        if cluster_id not in self.clusters: return

        cluster_to_remove = self.clusters.pop(cluster_id)
        for item_id in cluster_to_remove['items']:
            if item_id in self.item_to_cluster:
                del self.item_to_cluster[item_id]

        for concept in cluster_to_remove['concepts']:
            if concept in self.semantic_index and cluster_id in self.semantic_index[concept]:
                self.semantic_index[concept].remove(cluster_id)
                if not self.semantic_index[concept]: # If list is empty, remove concept key
                    del self.semantic_index[concept]


class EpisodicMemoryTier(MemoryTierInterface):
    """
    Episodic Memory: Temporal events with time-based indexing.
    Placeholder implementation.
    """
    def __init__(self): # Removed config argument as it's not used
        self.item_count = 0 # Basic stat for placeholder
        print("EpisodicMemoryTier (stub) initialized.")
        pass

    async def store(self, item: MemoryItem) -> str:
        # print(f"EpisodicMemoryTier (stub): Storing item {item.id}")
        self.item_count +=1
        await asyncio.sleep(0.001) # Simulate async I/O
        return item.id

    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        # print(f"EpisodicMemoryTier (stub): Retrieving for query '{query.query_text[:50]}...'")
        await asyncio.sleep(0.001)
        return []

    async def update_access(self, item_id: str, access_time: datetime) -> None:
        # print(f"EpisodicMemoryTier (stub): Updating access for item {item_id}")
        await asyncio.sleep(0.001)
        pass

    async def get_tier_stats(self) -> Dict[str, Any]:
        return {'tier': MemoryTier.EPISODIC.value, 'item_count': self.item_count, 'retention_policy': 'N/A (stub)'}

    async def cleanup_expired(self) -> int:
        # print("EpisodicMemoryTier (stub): Cleaning up expired items.")
        await asyncio.sleep(0.001)
        # In a real implementation, items would be removed based on their timestamp and a retention policy.
        # For a stub, assume no items are ever "expired" or removed this way.
        return 0


if __name__ == "__main__":
    async def test_memory_tiers():
        print("--- Testing Memory Tiers ---")

        # Test Working Memory
        print("\n--- Working Memory Tier Test ---")
        working_memory = WorkingMemoryTier(max_items=100, retention_days=1)

        # Create some sample items
        base_time = datetime.now(timezone.utc)
        item1_wm = MemoryItem(
            id="wm_item_001", content="Frequent access data for Python project",
            metadata={"project": "alpha", "category": "code_notes"}, tier=MemoryTier.WORKING,
            created_at=base_time - timedelta(hours=2), last_accessed=base_time - timedelta(minutes=10),
            access_count=5, importance_score=0.9
        )
        item2_wm = MemoryItem(
            id="wm_item_002", content="Short-term notes on API design for project X",
            metadata={"project": "alpha", "category": "api_design"}, tier=MemoryTier.WORKING,
            created_at=base_time - timedelta(hours=1), last_accessed=base_time - timedelta(minutes=5),
            access_count=10, importance_score=0.7
        )
        item3_wm_old = MemoryItem( # This one should survive the first cleanup
            id="wm_item_003", content="Data created/accessed 12 hours ago",
            metadata={"project": "test_cleanup"}, tier=MemoryTier.WORKING,
            created_at=base_time - timedelta(hours=12), last_accessed=base_time - timedelta(hours=12),
            access_count=1, importance_score=0.3
        )

        await working_memory.store(item1_wm)
        await working_memory.store(item2_wm)
        await working_memory.store(item3_wm_old)
        print(f"WM: Stored {len(working_memory.items)} items.")

        query_wm = MemoryQuery(query_text="Python project API", limit=5, importance_threshold=0.5)
        results_wm = await working_memory.retrieve(query_wm)
        print(f"WM: Retrieved {len(results_wm)} results for query '{query_wm.query_text}'. First hit: {results_wm[0].content[:50] if results_wm else 'N/A'}")
        assert len(results_wm) > 0, "Working memory retrieval failed for relevant query"

        stats_wm = await working_memory.get_tier_stats()
        print(f"WM Stats: {stats_wm}")
        assert stats_wm['item_count'] == 3

        cleaned_wm = await working_memory.cleanup_expired()
        print(f"WM: Cleaned {cleaned_wm} items in first pass (should be 0 if item3 created < retention_days ago).")

        # Now, check if item3 still exists before trying to modify it
        if "wm_item_003" in working_memory.items:
            print("WM: Item 'wm_item_003' survived first cleanup as expected. Now manually aging it.")
            # Manually make item3_wm_old seem very old to test cleanup
            working_memory.items["wm_item_003"].last_accessed = base_time - timedelta(days=3) # Older than retention_days (1)
            cleaned_wm_again = await working_memory.cleanup_expired()
            print(f"WM: Cleaned {cleaned_wm_again} expired items after manually aging 'wm_item_003'.")
            assert cleaned_wm_again == 1, "Working memory cleanup did not remove 'wm_item_003' after aging."
        else:
            print("WM: Item 'wm_item_003' was unexpectedly cleaned in the first pass. Check test setup or cleanup logic.")
            # This path would indicate a flaw if we expected it to survive.
            # For this corrected test, we expect it to survive the first pass.
            assert False, "Test item wm_item_003 was cleaned prematurely."



        # Test Long-Term Memory
        print("\n--- Long-Term Memory Tier Test ---")
        long_term_memory = LongTermMemoryTier(consolidation_threshold=2, similarity_threshold=0.1) # Low thresholds for test

        item1_ltm = MemoryItem(
            id="ltm_item_001", content="Detailed analysis of Python performance characteristics.",
            metadata={"topic": "python_performance", "year": 2023}, tier=MemoryTier.LONG_TERM,
            created_at=base_time - timedelta(days=30), last_accessed=base_time - timedelta(days=5),
            access_count=3, importance_score=0.85
        )
        item2_ltm = MemoryItem(
            id="ltm_item_002", content="Python concurrency models and async programming guide.",
            metadata={"topic": "python_concurrency", "year": 2022}, tier=MemoryTier.LONG_TERM,
            created_at=base_time - timedelta(days=60), last_accessed=base_time - timedelta(days=10),
            access_count=2, importance_score=0.75
        )
        item3_ltm_related = MemoryItem( # Related to item1
            id="ltm_item_003", content="Advanced Python optimization techniques and performance tuning.",
            metadata={"topic": "python_performance", "year": 2024}, tier=MemoryTier.LONG_TERM,
            created_at=base_time - timedelta(days=10), last_accessed=base_time - timedelta(days=1),
            access_count=5, importance_score=0.9
        )

        await long_term_memory.store(item1_ltm)
        await long_term_memory.store(item2_ltm)
        print(f"LTM: Stored 2 items. Clusters: {len(long_term_memory.clusters)}")
        await long_term_memory.store(item3_ltm_related) # Should cluster with item1_ltm
        print(f"LTM: Stored 3rd item (related). Clusters: {len(long_term_memory.clusters)}")
        # Depending on _extract_concepts, item1 and item3 might form a cluster
        # For this test, we hope they do.
        assert len(long_term_memory.clusters) <= 2, "LTM: Items expected to cluster did not."


        query_ltm = MemoryQuery(query_text="Python performance", limit=5, importance_threshold=0.7)
        results_ltm = await long_term_memory.retrieve(query_ltm)
        print(f"LTM: Retrieved {len(results_ltm)} results for query '{query_ltm.query_text}'.")
        for res_item in results_ltm:
            print(f"  - ID: {res_item.id}, Content: {res_item.content[:50]}...")
        assert len(results_ltm) >= 1, "Long-term memory retrieval failed for relevant query"
        if len(results_ltm) > 0:
             assert "performance" in results_ltm[0].content.lower()

        stats_ltm = await long_term_memory.get_tier_stats()
        print(f"LTM Stats: {stats_ltm}")
        assert stats_ltm['total_items'] == 3
        if len(long_term_memory.clusters) == 1 : # If all clustered together (unlikely with simple concepts)
             assert stats_ltm['consolidated_clusters'] == 1, "LTM consolidation error"
        elif len(long_term_memory.clusters) == 2: # If item1 & item3 clustered, item2 separate
            # Check if the performance cluster got consolidated
            test_item1_cluster_id = long_term_memory.item_to_cluster.get(item1_ltm.id)
            if test_item1_cluster_id and len(long_term_memory.clusters[test_item1_cluster_id]['items']) >= 2:
                 assert long_term_memory.clusters[test_item1_cluster_id]['consolidated_summary'] != "", "LTM consolidation error for performance cluster"


        # Test Episodic Memory (Stub)
        print("\n--- Episodic Memory Tier Test (Stub) ---")
        episodic_memory = EpisodicMemoryTier()
        item1_em = MemoryItem(
            id="em_item_001", content="Event: System startup sequence initiated.",
            metadata={"event_type": "system_log", "severity": "INFO"}, tier=MemoryTier.EPISODIC,
            created_at=base_time - timedelta(seconds=30), last_accessed=base_time - timedelta(seconds=30),
            access_count=1, importance_score=0.5
        )
        await episodic_memory.store(item1_em)
        query_em = MemoryQuery(query_text="system startup")
        results_em = await episodic_memory.retrieve(query_em)
        print(f"EM: Retrieved {len(results_em)} results (stub).")
        assert len(results_em) == 0
        stats_em = await episodic_memory.get_tier_stats()
        print(f"EM Stats: {stats_em}")
        assert stats_em['item_count'] == 1

        print("\n--- Memory Tier Tests Completed ---")

    asyncio.run(test_memory_tiers())
