import asyncio
import unittest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

# Adjust imports based on actual file structure
try:
    from python.helpers.preference_adaptation import PreferenceAdaptationEngine
    from python.helpers.user_preferences import UserPreferenceManager, PreferenceType
    from python.helpers.behavioral_analyzer import BehavioralPatternAnalyzer
except ImportError:
    print("WARN: test_preference_adaptation.py: Could not import dependencies. Using placeholders.")
    from enum import Enum
    class PreferenceType(Enum): DOMAIN_WEIGHT = "domain_weight"; SEARCH_PREFERENCE = "search_preference"; CLASSIFICATION_THRESHOLD = "classification_threshold"
    class UserPreferenceManager: user_id = "test"; async def get_preference(self,k,d=None): return d; async def set_preference(self,k,v,pt,ue=False): return True
    class BehavioralPatternAnalyzer: user_id = "test"; async def analyze_patterns(self): return []; async def get_pattern_recommendations(self): return []
    class PreferenceAdaptationEngine:
        def __init__(self, upm, bpa=None): self.preference_manager = upm; self.behavioral_analyzer = bpa or BehavioralPatternAnalyzer(upm.user_id); self.adaptation_history = []
        async def analyze_and_adapt(self): return {'adaptations_applied_count':0, 'adaptations':[]}


class TestPreferenceAdaptationEngine(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        self.mock_upm = AsyncMock(spec=UserPreferenceManager)
        self.mock_upm.user_id = "test_user_adaptation" # Ensure user_id is set on the mock

        self.mock_bpa = AsyncMock(spec=BehavioralPatternAnalyzer)

        # Instantiate the engine with mocks
        self.engine = PreferenceAdaptationEngine(self.mock_upm, self.mock_bpa)

    async def test_initialization(self):
        self.assertIsNotNone(self.engine.preference_manager)
        self.assertIsNotNone(self.engine.behavioral_analyzer)
        self.assertEqual(len(self.engine.adaptation_history), 0)

    async def test_analyze_and_adapt_no_recommendations(self):
        self.mock_bpa.analyze_patterns.return_value = [] # No patterns
        self.mock_bpa.get_pattern_recommendations.return_value = [] # No recommendations

        session_summary = await self.engine.analyze_and_adapt()

        self.mock_bpa.analyze_patterns.assert_called_once()
        self.mock_bpa.get_pattern_recommendations.assert_called_once()
        self.assertEqual(session_summary['adaptations_applied_count'], 0)
        self.assertEqual(len(session_summary['adaptations']), 0)
        self.assertEqual(len(self.engine.adaptation_history), 1)

    async def test_apply_domain_weight_recommendation(self):
        recommendation = {
            'type': 'increase_domain_weight', 'domain': 'tech',
            'recommended_weight_increase': 0.1, 'confidence': 0.8,
            'reason': 'Strong tech preference'
        }
        self.mock_upm.get_preference.return_value = 0.5 # Current weight for domain_weight_tech

        adaptation_detail = await self.engine._apply_recommendation(recommendation)

        self.mock_upm.get_preference.assert_called_once_with('domain_weight_tech', default_value=0.5)
        self.mock_upm.set_preference.assert_called_once_with(
            'domain_weight_tech', 0.6, PreferenceType.DOMAIN_WEIGHT, user_explicit=False
        )
        self.assertIsNotNone(adaptation_detail)
        self.assertEqual(adaptation_detail['new_value'], 0.6)

    async def test_apply_search_limit_recommendation(self):
        recommendation = {
            'type': 'adjust_search_limit', 'recommended_limit': 5,
            'confidence': 0.7, 'reason': 'User uses few results'
        }
        self.mock_upm.get_preference.return_value = 10 # Current search_result_limit

        adaptation_detail = await self.engine._apply_recommendation(recommendation)

        self.mock_upm.get_preference.assert_called_once_with('search_result_limit', default_value=10)
        self.mock_upm.set_preference.assert_called_once_with(
            'search_result_limit', 5, PreferenceType.SEARCH_PREFERENCE, user_explicit=False
        )
        self.assertIsNotNone(adaptation_detail)
        self.assertEqual(adaptation_detail['new_value'], 5)

    async def test_apply_classification_threshold_recommendation(self):
        recommendation = {
            'type': 'review_classification_thresholds',
            'recommended_action': 'lower_confidence_threshold',
            'confidence': 0.6, 'reason': 'High error rate'
        }
        self.mock_upm.get_preference.return_value = 0.7 # Current classification_confidence_threshold

        adaptation_detail = await self.engine._apply_recommendation(recommendation)

        expected_new_threshold = 0.7 - 0.05 # 0.65
        self.mock_upm.get_preference.assert_called_once_with('classification_confidence_threshold', default_value=0.7)
        self.mock_upm.set_preference.assert_called_once_with(
            'classification_confidence_threshold', expected_new_threshold, PreferenceType.CLASSIFICATION_THRESHOLD, user_explicit=False
        )
        self.assertIsNotNone(adaptation_detail)
        self.assertAlmostEqual(adaptation_detail['new_value'], expected_new_threshold)

    async def test_analyze_and_adapt_with_multiple_recommendations(self):
        recommendations = [
            {'type': 'increase_domain_weight', 'domain': 'finance', 'recommended_weight_increase': 0.2, 'confidence': 0.9},
            {'type': 'adjust_search_limit', 'recommended_limit': 8, 'confidence': 0.75},
            {'type': 'unknown_recommendation_type'} # Should be skipped
        ]
        self.mock_bpa.get_pattern_recommendations.return_value = recommendations
        self.mock_bpa.analyze_patterns.return_value = [MagicMock()] # Some patterns

        # Mock return values for get_preference for each key
        def get_pref_side_effect(key, default_value=None):
            if key == 'domain_weight_finance': return 0.4
            if key == 'search_result_limit': return 10
            return default_value
        self.mock_upm.get_preference.side_effect = get_pref_side_effect
        self.mock_upm.set_preference.return_value = True # Assume all sets are successful

        session_summary = await self.engine.analyze_and_adapt()

        self.assertEqual(session_summary['adaptations_applied_count'], 2) # 2 valid recommendations
        self.assertEqual(len(session_summary['adaptations']), 2)

        # Check calls to set_preference
        # Call for domain_weight_finance: 0.4 + 0.2 = 0.6
        # Call for search_result_limit: 8
        self.mock_upm.set_preference.assert_any_call('domain_weight_finance', 0.6, PreferenceType.DOMAIN_WEIGHT, user_explicit=False)
        self.mock_upm.set_preference.assert_any_call('search_result_limit', 8, PreferenceType.SEARCH_PREFERENCE, user_explicit=False)
        self.assertEqual(self.mock_upm.set_preference.call_count, 2)


    async def test_adaptation_does_not_occur_if_no_meaningful_change(self):
        # Domain weight: increase is too small
        recommendation_domain = {
            'type': 'increase_domain_weight', 'domain': 'tinychange',
            'recommended_weight_increase': 0.001, 'confidence': 0.8
        }
        self.mock_upm.get_preference.return_value = 0.5

        adaptation_detail_domain = await self.engine._apply_recommendation(recommendation_domain)
        self.assertIsNone(adaptation_detail_domain, "Adaptation should be None if change is too small.")
        self.mock_upm.set_preference.assert_not_called() # set_preference should not be called

        # Search limit: new limit is same as current
        self.mock_upm.reset_mock() # Reset mocks for next test case
        recommendation_search = {
            'type': 'adjust_search_limit', 'recommended_limit': 10, 'confidence': 0.7
        }
        self.mock_upm.get_preference.return_value = 10

        adaptation_detail_search = await self.engine._apply_recommendation(recommendation_search)
        self.assertIsNone(adaptation_detail_search, "Adaptation should be None if new value is same as current.")
        self.mock_upm.set_preference.assert_not_called()


if __name__ == '__main__':
    unittest.main()
