"""
Memory Service Registry and Initialization

This module provides service registration and initialization for the memory system.
It sets up all memory-related services in the correct order and manages their dependencies.
"""

from typing import Dict, Any, Optional
import asyncio

# Import foundation layer
from .env_loader import env_loader
from .classification_config import classification_config, config_manager
from .logging_config import setup_logging, get_logger
from .service_container import memory_service_container, ServiceLifecycle

# Import memory system components (these will be registered as services)
# Note: We'll import these conditionally to avoid circular dependencies during setup

logger = get_logger(__name__)

class MemoryServiceRegistry:
    """
    Registry for memory system services.
    
    Manages the registration and initialization of all memory-related services
    in the correct dependency order.
    """
    
    def __init__(self):
        self.container = memory_service_container
        self._initialized = False
        self._initialization_lock = asyncio.Lock()
    
    def register_foundation_services(self):
        """Register foundation layer services."""
        logger.info("Registering foundation layer services...")
        
        # Register env_loader (no dependencies)
        self.container.register_service(
            "env_loader", 
            type(env_loader), 
            instance=env_loader,
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        # Register classification_config (depends on env_loader)
        self.container.register_service(
            "classification_config",
            type(classification_config),
            instance=classification_config,
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        # Register config_manager (depends on env_loader)
        self.container.register_service(
            "config_manager",
            type(config_manager),
            instance=config_manager,
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        logger.info("Foundation layer services registered successfully")
    
    def register_memory_services(self):
        """Register memory system services."""
        logger.info("Registering memory system services...")
        
        # Register memory tiers (depend on classification_config)
        self.container.register_factory(
            "working_memory_tier",
            self._get_working_memory_tier_class(),
            factory=self._create_working_memory_tier,
            dependencies=["classification_config"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        self.container.register_factory(
            "long_term_memory_tier",
            self._get_long_term_memory_tier_class(),
            factory=self._create_long_term_memory_tier,
            dependencies=["classification_config"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        self.container.register_factory(
            "episodic_memory_tier",
            self._get_episodic_memory_tier_class(),
            factory=self._create_episodic_memory_tier,
            dependencies=["classification_config"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        # Register hierarchical memory manager (depends on memory tiers)
        self.container.register_factory(
            "hierarchical_memory_manager",
            self._get_hierarchical_memory_manager_class(),
            factory=self._create_hierarchical_memory_manager,
            dependencies=["working_memory_tier", "long_term_memory_tier", "episodic_memory_tier"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        logger.info("Memory system services registered successfully")
    
    def register_classification_services(self):
        """Register classification system services."""
        logger.info("Registering classification system services...")
        
        # Register entity relationship analyzer
        self.container.register_factory(
            "entity_relationship_analyzer",
            self._get_entity_relationship_analyzer_class(),
            factory=self._create_entity_relationship_analyzer,
            dependencies=["classification_config"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        # Register behavioral analyzer
        self.container.register_factory(
            "behavioral_analyzer",
            self._get_behavioral_analyzer_class(),
            factory=self._create_behavioral_analyzer,
            dependencies=["classification_config"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        # Register intelligent classification engine
        self.container.register_factory(
            "intelligent_classification_engine",
            self._get_intelligent_classification_engine_class(),
            factory=self._create_intelligent_classification_engine,
            dependencies=["classification_config", "entity_relationship_analyzer", "behavioral_analyzer"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        logger.info("Classification system services registered successfully")
    
    def register_integration_services(self):
        """Register integration layer services."""
        logger.info("Registering integration layer services...")
        
        # Register user preferences manager
        self.container.register_factory(
            "user_preferences_manager",
            self._get_user_preferences_manager_class(),
            factory=self._create_user_preferences_manager,
            dependencies=["classification_config"],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        # Register memory abstraction layer
        self.container.register_factory(
            "memory_abstraction_layer",
            self._get_memory_abstraction_layer_class(),
            factory=self._create_memory_abstraction_layer,
            dependencies=[
                "intelligent_classification_engine",
                "hierarchical_memory_manager", 
                "user_preferences_manager"
            ],
            lifecycle=ServiceLifecycle.SINGLETON
        )
        
        logger.info("Integration layer services registered successfully")
    
    async def initialize_all_services(self) -> bool:
        """
        Initialize all registered services in dependency order.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        async with self._initialization_lock:
            if self._initialized:
                return True
                
            try:
                logger.info("Starting memory service initialization...")
                
                # Validate dependencies first
                errors = self.container.validate_dependencies()
                if errors:
                    logger.error(f"Dependency validation failed: {errors}")
                    return False
                
                # Register all services
                self.register_foundation_services()
                self.register_memory_services()
                self.register_classification_services()
                self.register_integration_services()
                
                # Test that we can get the main service
                memory_abstraction = self.container.get_service("memory_abstraction_layer")
                logger.info("Memory abstraction layer initialized successfully")
                
                self._initialized = True
                logger.info("All memory services initialized successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to initialize memory services: {e}")
                return False
    
    def get_service(self, name: str) -> Any:
        """Get a service from the container."""
        return self.container.get_service(name)
    
    def is_initialized(self) -> bool:
        """Check if services are initialized."""
        return self._initialized
    
    # Factory methods (these will import the actual classes when needed)
    def _get_working_memory_tier_class(self):
        from .memory_tiers import WorkingMemoryTier
        return WorkingMemoryTier
    
    def _get_long_term_memory_tier_class(self):
        from .memory_tiers import LongTermMemoryTier
        return LongTermMemoryTier
    
    def _get_episodic_memory_tier_class(self):
        from .memory_tiers import EpisodicMemoryTier
        return EpisodicMemoryTier
    
    def _get_hierarchical_memory_manager_class(self):
        from .hierarchical_memory_manager import HierarchicalMemoryManager
        return HierarchicalMemoryManager
    
    def _get_entity_relationship_analyzer_class(self):
        from .entity_relationship_analyzer import EntityRelationshipAnalyzer
        return EntityRelationshipAnalyzer
    
    def _get_behavioral_analyzer_class(self):
        from .behavioral_analyzer import BehavioralAnalyzer
        return BehavioralAnalyzer
    
    def _get_intelligent_classification_engine_class(self):
        from .intelligent_classification_engine import IntelligentClassificationEngine
        return IntelligentClassificationEngine
    
    def _get_user_preferences_manager_class(self):
        from .user_preferences import UserPreferenceManager
        return UserPreferenceManager
    
    def _get_memory_abstraction_layer_class(self):
        from .memory_abstraction import EnhancedMemoryAbstractionLayer
        return EnhancedMemoryAbstractionLayer
    
    # Factory functions
    def _create_working_memory_tier(self, classification_config):
        cls = self._get_working_memory_tier_class()
        return cls()
    
    def _create_long_term_memory_tier(self, classification_config):
        cls = self._get_long_term_memory_tier_class()
        return cls()
    
    def _create_episodic_memory_tier(self, classification_config):
        cls = self._get_episodic_memory_tier_class()
        return cls()
    
    def _create_hierarchical_memory_manager(self, working_memory_tier, long_term_memory_tier, episodic_memory_tier):
        cls = self._get_hierarchical_memory_manager_class()
        # Create instance and inject dependencies
        manager = cls()
        manager.working_memory = working_memory_tier
        manager.long_term_memory = long_term_memory_tier
        manager.episodic_memory = episodic_memory_tier
        return manager
    
    def _create_entity_relationship_analyzer(self, classification_config):
        cls = self._get_entity_relationship_analyzer_class()
        return cls(classification_config)
    
    def _create_behavioral_analyzer(self, classification_config):
        cls = self._get_behavioral_analyzer_class()
        return cls(classification_config)
    
    def _create_intelligent_classification_engine(self, classification_config, entity_relationship_analyzer, behavioral_analyzer):
        cls = self._get_intelligent_classification_engine_class()
        return cls(classification_config, entity_relationship_analyzer, behavioral_analyzer)
    
    def _create_user_preferences_manager(self, classification_config):
        cls = self._get_user_preferences_manager_class()
        return cls()
    
    def _create_memory_abstraction_layer(self, intelligent_classification_engine, hierarchical_memory_manager, user_preferences_manager):
        cls = self._get_memory_abstraction_layer_class()
        # Create instance and inject dependencies
        layer = cls()
        layer.classification_engine = intelligent_classification_engine
        layer.memory_manager = hierarchical_memory_manager
        layer.user_preferences = user_preferences_manager
        return layer

# Global registry instance
memory_registry = MemoryServiceRegistry()
