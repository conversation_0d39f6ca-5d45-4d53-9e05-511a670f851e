# Phase 1: Foundation Setup - Memory Abstraction
## Enhanced Memory Abstraction Layer

This document provides implementation for extending the existing memory abstraction layer to support the intelligent data classification system.

## 🎯 Integration Strategy

### Approach
- **Extend existing**: Build upon current `memory_abstraction.py`
- **Backward compatible**: Maintain existing API contracts
- **Gradual migration**: Allow incremental adoption
- **Performance focused**: Optimize for target response times

## 🔧 Implementation

### Step 1.4: Extend Memory Abstraction Layer

**File:** `python/helpers/memory_abstraction.py`
**Action:** Extend existing file

```python
"""
Enhanced Memory Abstraction Layer with Intelligent Classification
"""

# Add these imports to existing file
from .intelligent_classification_engine import IntelligentClassificationEngine
from .hierarchical_memory_manager import HierarchicalMemoryManager
from .user_preferences import UserPreferenceManager
from .data_separation_framework import DataSeparationFramework
from .boundary_enforcement import BoundaryEnforcementSystem
from .performance_monitoring import performance_monitor, PerformanceTimer
from .classification_config import classification_config

class EnhancedMemoryAbstractionLayer:
    """
    Enhanced memory abstraction layer with intelligent classification
    """
    
    def __init__(self, user_id: str = "default_user"):
        # Core components
        self.classification_engine = IntelligentClassificationEngine(classification_config)
        self.memory_manager = HierarchicalMemoryManager()
        self.user_preferences = UserPreferenceManager(user_id)
        self.data_separation = DataSeparationFramework()
        self.boundary_enforcement = BoundaryEnforcementSystem()
        
        # Legacy compatibility
        self.legacy_backend = None
        self.compatibility_mode = True
        
        # Initialization state
        self.is_initialized = False
        self.initialization_lock = asyncio.Lock()
    
    async def initialize(self):
        """Initialize all components"""
        async with self.initialization_lock:
            if self.is_initialized:
                return
            
            # Initialize components in order
            await self.classification_engine.initialize()
            await self.memory_manager.initialize()
            await performance_monitor.start_monitoring()
            
            # Load user preferences
            await self._load_user_preferences()
            
            self.is_initialized = True
            print("Enhanced Memory Abstraction Layer initialized")
    
    async def _ensure_initialized(self):
        """Ensure system is initialized before operations"""
        if not self.is_initialized:
            await self.initialize()
    
    async def classify_and_route_content(
        self, 
        content: str, 
        content_type: str, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, ClassificationResult]:
        """
        Classify content and route to appropriate storage system
        
        Returns:
            Tuple of (item_id, classification_result)
        """
        await self._ensure_initialized()
        
        async with PerformanceTimer('classify_and_route_total'):
            # Prepare metadata
            full_metadata = {
                'content_type': content_type,
                'timestamp': datetime.now().isoformat(),
                'user_id': self.user_preferences.user_id,
                **(metadata or {})
            }
            
            # Step 1: Classify content
            async with PerformanceTimer('classification_decision'):
                classification_result = await self.classification_engine.classify_content(
                    content, full_metadata
                )
            
            # Step 2: Apply data separation
            async with PerformanceTimer('data_separation'):
                separation_result = await self.data_separation.separate_mixed_data(
                    content, full_metadata
                )
            
            # Step 3: Validate boundaries
            async with PerformanceTimer('boundary_validation'):
                all_items = (separation_result.history_items + 
                           separation_result.knowledge_items + 
                           separation_result.mixed_items)
                
                boundary_check = await self.boundary_enforcement.check_data_boundaries(all_items)
                
                if not boundary_check.is_valid:
                    # Log violations but continue with safe fallback
                    print(f"Boundary violations detected: {len(boundary_check.violations)}")
                    classification_result.strategy = ClassificationStrategy.ISOLATED_NAMESPACE
                    classification_result.validation_warnings.extend([
                        v.description for v in boundary_check.violations
                    ])
            
            # Step 4: Store in appropriate memory tier
            async with PerformanceTimer('memory_storage'):
                item_id = await self.memory_manager.store_memory(
                    content, full_metadata, classification_result
                )
            
            # Step 5: Update user preferences based on interaction
            await self._record_user_interaction(classification_result, full_metadata)
            
            return item_id, classification_result
    
    async def search_memories(
        self, 
        query: str, 
        search_strategy: str = "hierarchical",
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 10
    ) -> List[MemoryItem]:
        """
        Search across all memory tiers with intelligent routing
        """
        await self._ensure_initialized()
        
        async with PerformanceTimer('memory_search_total'):
            # Create memory query
            from .memory_tiers import MemoryQuery
            
            memory_query = MemoryQuery(
                query_text=query,
                limit=limit,
                importance_threshold=await self.user_preferences.get_preference(
                    'memory_importance_threshold', 0.5
                )
            )
            
            # Apply user preference filters
            if filters:
                memory_query.time_range = filters.get('time_range')
                memory_query.tier_preference = filters.get('tier_preference')
            
            # Search using memory manager
            results = await self.memory_manager.retrieve_memories(memory_query, search_strategy)
            
            # Record search interaction for preference learning
            await self.user_preferences.record_user_feedback(
                'search_result',
                {
                    'query': query,
                    'strategy': search_strategy,
                    'results_count': len(results),
                    'filters': filters
                },
                LearningSignal.IMPLICIT_USAGE
            )
            
            return results
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        await self._ensure_initialized()
        
        # Get component statuses
        engine_status = await self.classification_engine.get_engine_status()
        memory_stats = await self.memory_manager.get_memory_hierarchy_stats()
        preference_stats = await self.user_preferences.get_preference_stats()
        performance_summary = performance_monitor.get_performance_summary()
        
        return {
            'system_initialized': self.is_initialized,
            'classification_engine': {
                'initialized': engine_status['is_initialized'],
                'error_count': engine_status['error_count'],
                'performance': engine_status['performance_summary']
            },
            'memory_hierarchy': {
                'total_items': memory_stats['hierarchy_overview']['total_items'],
                'tier_distribution': memory_stats['tier_distribution'],
                'performance_targets_met': memory_stats['performance_summary']['targets_met']
            },
            'user_preferences': {
                'total_preferences': preference_stats['total_preferences'],
                'adaptation_enabled': preference_stats['adaptation_enabled'],
                'recent_feedback': preference_stats['recent_feedback_count']
            },
            'performance_monitoring': {
                'active': performance_summary['monitoring_active'],
                'metrics_tracked': performance_summary['metrics_tracked'],
                'active_alerts': performance_summary['active_alerts']
            }
        }
    
    async def _load_user_preferences(self):
        """Load and apply user preferences"""
        
        # Get domain weights for classification
        domain_weights = await self.user_preferences.get_domain_weights()
        
        # Get classification thresholds
        thresholds = await self.user_preferences.get_classification_thresholds()
        
        # Update classification config with user preferences
        classification_config.update_from_user_preferences({
            'domain_weights': domain_weights,
            'classification_thresholds': thresholds
        })
    
    async def _record_user_interaction(
        self, 
        classification_result: ClassificationResult, 
        metadata: Dict[str, Any]
    ):
        """Record user interaction for preference learning"""
        
        # Determine feedback type based on classification confidence
        if classification_result.confidence > 0.8:
            feedback_type = LearningSignal.POSITIVE_FEEDBACK
        elif classification_result.confidence < 0.5:
            feedback_type = LearningSignal.NEGATIVE_FEEDBACK
        else:
            feedback_type = LearningSignal.IMPLICIT_USAGE
        
        # Record classification feedback
        await self.user_preferences.record_user_feedback(
            'classification_result',
            {
                'strategy': classification_result.strategy.value,
                'confidence': classification_result.confidence,
                'processing_time_ms': classification_result.processing_time_ms,
                'content_type': metadata.get('content_type')
            },
            feedback_type
        )
    
    # Legacy compatibility methods
    async def store(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Legacy store method for backward compatibility"""
        content_type = metadata.get('type', 'unknown') if metadata else 'unknown'
        item_id, _ = await self.classify_and_route_content(content, content_type, metadata)
        return item_id
    
    async def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Legacy search method for backward compatibility"""
        results = await self.search_memories(query, limit=limit)
        
        # Convert to legacy format
        legacy_results = []
        for item in results:
            legacy_results.append({
                'id': item.id,
                'content': item.content,
                'metadata': item.metadata,
                'score': item.importance_score,
                'created_at': item.created_at.isoformat()
            })
        
        return legacy_results
    
    async def update_user_preference(self, key: str, value: Any) -> bool:
        """Update user preference"""
        from .user_preferences import PreferenceType
        
        # Map common preference keys to types
        preference_type_map = {
            'domain_weight': PreferenceType.DOMAIN_WEIGHT,
            'privacy_sensitivity': PreferenceType.PRIVACY_SENSITIVITY,
            'classification_threshold': PreferenceType.CLASSIFICATION_THRESHOLD,
            'memory_retention': PreferenceType.MEMORY_RETENTION,
            'search_preference': PreferenceType.SEARCH_PREFERENCE
        }
        
        # Determine preference type
        pref_type = PreferenceType.SEARCH_PREFERENCE  # Default
        for key_part, mapped_type in preference_type_map.items():
            if key_part in key:
                pref_type = mapped_type
                break
        
        success = await self.user_preferences.set_preference(key, value, pref_type)
        
        if success:
            # Reload preferences to apply changes
            await self._load_user_preferences()
        
        return success
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for monitoring"""
        
        # Get classification performance
        engine_status = await self.classification_engine.get_engine_status()
        classification_perf = engine_status['performance_summary']
        
        # Get memory performance
        memory_stats = await self.memory_manager.get_memory_hierarchy_stats()
        memory_perf = memory_stats['performance_summary']
        
        # Get overall system performance
        system_perf = performance_monitor.get_performance_summary()
        
        return {
            'classification': {
                'average_response_time_ms': classification_perf['average_response_time_ms'],
                'success_rate': classification_perf['success_rate'],
                'cache_hit_rate': classification_perf['cache_hit_rate']
            },
            'memory': {
                'working_memory_avg_ms': memory_perf['working_memory_avg_response_ms'],
                'long_term_memory_avg_ms': memory_perf['long_term_memory_avg_response_ms'],
                'episodic_memory_avg_ms': memory_perf['episodic_memory_avg_response_ms'],
                'targets_met': memory_perf['targets_met']
            },
            'system': {
                'cpu_usage_percent': system_perf['system_health'].get('cpu_percent', 0),
                'memory_usage_percent': system_perf['system_health'].get('memory_percent', 0),
                'active_alerts': system_perf['active_alerts']
            }
        }
    
    async def export_user_data(self) -> Dict[str, Any]:
        """Export user data for backup or migration"""
        
        # Export user preferences
        preferences_data = await self.user_preferences.export_preferences()
        
        # Export memory data (summary only for privacy)
        memory_stats = await self.memory_manager.get_memory_hierarchy_stats()
        
        return {
            'user_id': self.user_preferences.user_id,
            'export_timestamp': datetime.now().isoformat(),
            'preferences': preferences_data,
            'memory_summary': {
                'total_items': memory_stats['hierarchy_overview']['total_items'],
                'tier_distribution': memory_stats['tier_distribution']
            },
            'system_version': classification_config.config_version
        }
    
    async def import_user_data(self, import_data: Dict[str, Any]) -> bool:
        """Import user data from backup"""
        
        try:
            # Import preferences
            if 'preferences' in import_data:
                success = await self.user_preferences.import_preferences(import_data['preferences'])
                if not success:
                    return False
                
                # Reload preferences
                await self._load_user_preferences()
            
            return True
            
        except Exception as e:
            print(f"Error importing user data: {e}")
            return False
    
    async def shutdown(self):
        """Gracefully shutdown the system"""
        
        if self.is_initialized:
            # Shutdown components
            await self.classification_engine.shutdown()
            await self.memory_manager.shutdown()
            await performance_monitor.stop_monitoring()
            
            self.is_initialized = False
            print("Enhanced Memory Abstraction Layer shutdown complete")

# Global instance for backward compatibility
enhanced_memory_layer = None

async def get_enhanced_memory_layer(user_id: str = "default_user") -> EnhancedMemoryAbstractionLayer:
    """Get or create enhanced memory layer instance"""
    global enhanced_memory_layer
    
    if enhanced_memory_layer is None:
        enhanced_memory_layer = EnhancedMemoryAbstractionLayer(user_id)
        await enhanced_memory_layer.initialize()
    
    return enhanced_memory_layer

# Backward compatibility functions
async def store_memory(content: str, metadata: Dict[str, Any] = None) -> str:
    """Legacy function for storing memory"""
    layer = await get_enhanced_memory_layer()
    return await layer.store(content, metadata)

async def search_memory(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Legacy function for searching memory"""
    layer = await get_enhanced_memory_layer()
    return await layer.search(query, limit)
```

**Integration with Existing Code:**

Add this to the end of existing `memory_abstraction.py`:

```python
# Enhanced memory layer integration
try:
    from .enhanced_memory_abstraction import EnhancedMemoryAbstractionLayer
    ENHANCED_MEMORY_AVAILABLE = True
except ImportError:
    ENHANCED_MEMORY_AVAILABLE = False

class MemoryAbstractionLayer:
    """Original memory abstraction layer with optional enhancement"""
    
    def __init__(self, backend_type: str = "faiss", user_id: str = "default_user"):
        self.backend_type = backend_type
        self.user_id = user_id
        
        # Try to use enhanced layer if available
        if ENHANCED_MEMORY_AVAILABLE and backend_type == "enhanced":
            self.enhanced_layer = EnhancedMemoryAbstractionLayer(user_id)
            self.use_enhanced = True
        else:
            self.enhanced_layer = None
            self.use_enhanced = False
            
        # Initialize original backend
        self._initialize_original_backend()
    
    async def store(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Store content using appropriate backend"""
        if self.use_enhanced:
            return await self.enhanced_layer.store(content, metadata)
        else:
            # Use original implementation
            return await self._store_original(content, metadata)
    
    async def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search content using appropriate backend"""
        if self.use_enhanced:
            return await self.enhanced_layer.search(query, limit)
        else:
            # Use original implementation
            return await self._search_original(query, limit)
```

**Validation:**
```python
# Test enhanced memory abstraction layer
from python.helpers.memory_abstraction import get_enhanced_memory_layer

# Initialize enhanced layer
layer = await get_enhanced_memory_layer("test_user")

# Test classification and routing
item_id, classification = await layer.classify_and_route_content(
    "def hello_world(): print('Hello, World!')",
    "code",
    {"language": "python"}
)

print(f"Stored item {item_id} with strategy {classification.strategy.value}")

# Test search
results = await layer.search_memories("hello world", limit=5)
print(f"Found {len(results)} results")

# Test system status
status = await layer.get_system_status()
print(f"System initialized: {status['system_initialized']}")
```

---

**Phase 1 Foundation Complete!** 

✅ **Validation Checklist:**
- [ ] All dependencies installed and verified
- [ ] Configuration system working with environment variables
- [ ] Base classification framework implemented
- [ ] Enhanced memory abstraction layer integrated
- [ ] Backward compatibility maintained
- [ ] Performance monitoring enabled

**Next Step**: [Phase 2: Classification Engine Implementation](../02-classification-engine/intelligent-engine.md)
