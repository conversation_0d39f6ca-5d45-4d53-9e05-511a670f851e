# Phase 6: Performance Optimization - Caching System
## Multi-Level Caching Implementation

This document provides implementation for a comprehensive multi-level caching system designed to meet the performance targets of the intelligent data classification system.

## 🎯 Performance Targets

- **Working Memory**: <100ms response time (95th percentile)
- **Long-term Memory**: <500ms response time (95th percentile)
- **Episodic Memory**: <1s response time (90th percentile)
- **Classification Engine**: <50ms decision time (99th percentile)
- **Cache Hit Rate**: >85% target

## 🏗️ Caching Architecture

### Multi-Level Cache Hierarchy

```mermaid
graph TB
    A[Query Request] --> B[L1: Memory Cache]
    B -->|Hit| C[Return Result]
    B -->|Miss| D[L2: Redis Cache]
    D -->|Hit| E[Update L1] --> C
    D -->|Miss| F[L3: Persistent Cache]
    F -->|Hit| G[Update L2] --> E
    F -->|Miss| H[Database Query]
    H --> I[Update All Levels] --> C
```

## 🔧 Implementation

### Step 6.1: Create Multi-Level Cache System

**File:** `python/helpers/caching_system.py`
**Action:** Create new file

```python
"""
Multi-Level Caching System for Performance Optimization
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import asyncio
import time
import json
import hashlib
from datetime import datetime, timedelta
import weakref

class CacheLevel(Enum):
    """Cache level definitions"""
    L1_MEMORY = "l1_memory"
    L2_REDIS = "l2_redis"
    L3_PERSISTENT = "l3_persistent"

class CachePolicy(Enum):
    """Cache eviction policies"""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    FIFO = "fifo"  # First In First Out

@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: Optional[int]
    size_bytes: int
    metadata: Dict[str, Any]

@dataclass
class CacheStats:
    """Cache performance statistics"""
    hits: int
    misses: int
    evictions: int
    total_requests: int
    hit_rate: float
    avg_response_time_ms: float
    memory_usage_bytes: int
    entry_count: int

class CacheInterface(ABC):
    """Abstract interface for cache implementations"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set value in cache"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete value from cache"""
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """Clear all cache entries"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        pass

class MemoryCache(CacheInterface):
    """
    L1 Memory Cache - Fastest access, limited size
    """
    
    def __init__(self, max_size: int = 10000, policy: CachePolicy = CachePolicy.LRU):
        self.max_size = max_size
        self.policy = policy
        self.cache: Dict[str, CacheEntry] = {}
        self.access_order: List[str] = []  # For LRU
        self.access_frequency: Dict[str, int] = {}  # For LFU
        
        # Statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0,
            'response_times': []
        }
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from memory cache"""
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        if key in self.cache:
            entry = self.cache[key]
            
            # Check TTL
            if entry.ttl_seconds:
                age = (datetime.now() - entry.created_at).total_seconds()
                if age > entry.ttl_seconds:
                    await self.delete(key)
                    self.stats['misses'] += 1
                    return None
            
            # Update access statistics
            entry.last_accessed = datetime.now()
            entry.access_count += 1
            
            # Update access order for LRU
            if self.policy == CachePolicy.LRU:
                if key in self.access_order:
                    self.access_order.remove(key)
                self.access_order.append(key)
            
            # Update frequency for LFU
            if self.policy == CachePolicy.LFU:
                self.access_frequency[key] = self.access_frequency.get(key, 0) + 1
            
            self.stats['hits'] += 1
            response_time = (time.time() - start_time) * 1000
            self.stats['response_times'].append(response_time)
            
            return entry.value
        
        self.stats['misses'] += 1
        return None
    
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set value in memory cache"""
        
        # Calculate size (approximate)
        size_bytes = len(str(value).encode('utf-8'))
        
        # Check if we need to evict entries
        if len(self.cache) >= self.max_size and key not in self.cache:
            await self._evict_entry()
        
        # Create cache entry
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            ttl_seconds=ttl_seconds,
            size_bytes=size_bytes,
            metadata={}
        )
        
        self.cache[key] = entry
        
        # Update access tracking
        if self.policy == CachePolicy.LRU:
            if key in self.access_order:
                self.access_order.remove(key)
            self.access_order.append(key)
        
        if self.policy == CachePolicy.LFU:
            self.access_frequency[key] = 1
        
        return True
    
    async def delete(self, key: str) -> bool:
        """Delete value from memory cache"""
        if key in self.cache:
            del self.cache[key]
            
            if key in self.access_order:
                self.access_order.remove(key)
            
            if key in self.access_frequency:
                del self.access_frequency[key]
            
            return True
        return False
    
    async def clear(self) -> bool:
        """Clear all cache entries"""
        self.cache.clear()
        self.access_order.clear()
        self.access_frequency.clear()
        return True
    
    async def _evict_entry(self):
        """Evict entry based on policy"""
        if not self.cache:
            return
        
        key_to_evict = None
        
        if self.policy == CachePolicy.LRU:
            # Evict least recently used
            key_to_evict = self.access_order[0] if self.access_order else list(self.cache.keys())[0]
        
        elif self.policy == CachePolicy.LFU:
            # Evict least frequently used
            min_frequency = min(self.access_frequency.values()) if self.access_frequency else 0
            for key, freq in self.access_frequency.items():
                if freq == min_frequency:
                    key_to_evict = key
                    break
        
        elif self.policy == CachePolicy.TTL:
            # Evict expired entries first, then oldest
            now = datetime.now()
            for key, entry in self.cache.items():
                if entry.ttl_seconds:
                    age = (now - entry.created_at).total_seconds()
                    if age > entry.ttl_seconds:
                        key_to_evict = key
                        break
            
            if not key_to_evict:
                # Evict oldest entry
                oldest_entry = min(self.cache.values(), key=lambda x: x.created_at)
                key_to_evict = oldest_entry.key
        
        elif self.policy == CachePolicy.FIFO:
            # Evict first inserted (oldest)
            oldest_entry = min(self.cache.values(), key=lambda x: x.created_at)
            key_to_evict = oldest_entry.key
        
        if key_to_evict:
            await self.delete(key_to_evict)
            self.stats['evictions'] += 1
    
    async def get_stats(self) -> CacheStats:
        """Get memory cache statistics"""
        total_requests = self.stats['total_requests']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0.0
        
        avg_response_time = (
            sum(self.stats['response_times']) / len(self.stats['response_times'])
            if self.stats['response_times'] else 0.0
        )
        
        memory_usage = sum(entry.size_bytes for entry in self.cache.values())
        
        return CacheStats(
            hits=self.stats['hits'],
            misses=self.stats['misses'],
            evictions=self.stats['evictions'],
            total_requests=total_requests,
            hit_rate=hit_rate,
            avg_response_time_ms=avg_response_time,
            memory_usage_bytes=memory_usage,
            entry_count=len(self.cache)
        )

class RedisCache(CacheInterface):
    """
    L2 Redis Cache - Network-based, larger capacity
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379", key_prefix: str = "agent_zero:"):
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.redis_client = None
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0,
            'response_times': []
        }
    
    async def _get_redis_client(self):
        """Get or create Redis client"""
        if self.redis_client is None:
            try:
                import redis.asyncio as redis
                self.redis_client = redis.from_url(self.redis_url)
            except ImportError:
                print("Redis not available, using fallback")
                return None
        return self.redis_client
    
    def _make_key(self, key: str) -> str:
        """Create prefixed key"""
        return f"{self.key_prefix}{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        redis_client = await self._get_redis_client()
        if not redis_client:
            self.stats['misses'] += 1
            return None
        
        try:
            redis_key = self._make_key(key)
            value = await redis_client.get(redis_key)
            
            if value is not None:
                self.stats['hits'] += 1
                response_time = (time.time() - start_time) * 1000
                self.stats['response_times'].append(response_time)
                
                # Deserialize value
                return json.loads(value.decode('utf-8'))
            else:
                self.stats['misses'] += 1
                return None
                
        except Exception as e:
            print(f"Redis get error: {e}")
            self.stats['misses'] += 1
            return None
    
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set value in Redis cache"""
        redis_client = await self._get_redis_client()
        if not redis_client:
            return False
        
        try:
            redis_key = self._make_key(key)
            serialized_value = json.dumps(value, default=str)
            
            if ttl_seconds:
                await redis_client.setex(redis_key, ttl_seconds, serialized_value)
            else:
                await redis_client.set(redis_key, serialized_value)
            
            return True
            
        except Exception as e:
            print(f"Redis set error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from Redis cache"""
        redis_client = await self._get_redis_client()
        if not redis_client:
            return False
        
        try:
            redis_key = self._make_key(key)
            result = await redis_client.delete(redis_key)
            return result > 0
            
        except Exception as e:
            print(f"Redis delete error: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries with prefix"""
        redis_client = await self._get_redis_client()
        if not redis_client:
            return False
        
        try:
            pattern = f"{self.key_prefix}*"
            keys = await redis_client.keys(pattern)
            if keys:
                await redis_client.delete(*keys)
            return True
            
        except Exception as e:
            print(f"Redis clear error: {e}")
            return False
    
    async def get_stats(self) -> CacheStats:
        """Get Redis cache statistics"""
        total_requests = self.stats['total_requests']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0.0
        
        avg_response_time = (
            sum(self.stats['response_times']) / len(self.stats['response_times'])
            if self.stats['response_times'] else 0.0
        )
        
        # Try to get memory usage from Redis
        memory_usage = 0
        entry_count = 0
        
        redis_client = await self._get_redis_client()
        if redis_client:
            try:
                pattern = f"{self.key_prefix}*"
                keys = await redis_client.keys(pattern)
                entry_count = len(keys)
                
                # Estimate memory usage (rough approximation)
                if keys:
                    sample_key = keys[0]
                    sample_value = await redis_client.get(sample_key)
                    if sample_value:
                        avg_size = len(sample_value)
                        memory_usage = avg_size * entry_count
                        
            except Exception:
                pass
        
        return CacheStats(
            hits=self.stats['hits'],
            misses=self.stats['misses'],
            evictions=self.stats['evictions'],
            total_requests=total_requests,
            hit_rate=hit_rate,
            avg_response_time_ms=avg_response_time,
            memory_usage_bytes=memory_usage,
            entry_count=entry_count
        )

class PersistentCache(CacheInterface):
    """
    L3 Persistent Cache - File-based, largest capacity
    """
    
    def __init__(self, cache_dir: str = "cache", max_size_mb: int = 1000):
        import os
        self.cache_dir = cache_dir
        self.max_size_mb = max_size_mb
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0,
            'response_times': []
        }
        
        # Create cache directory
        os.makedirs(cache_dir, exist_ok=True)
    
    def _get_cache_path(self, key: str) -> str:
        """Get file path for cache key"""
        import os
        # Hash key to create safe filename
        key_hash = hashlib.sha256(key.encode()).hexdigest()
        return os.path.join(self.cache_dir, f"{key_hash}.cache")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from persistent cache"""
        import os
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        cache_path = self._get_cache_path(key)
        
        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                
                # Check TTL
                if cache_data.get('ttl_seconds'):
                    created_at = datetime.fromisoformat(cache_data['created_at'])
                    age = (datetime.now() - created_at).total_seconds()
                    if age > cache_data['ttl_seconds']:
                        await self.delete(key)
                        self.stats['misses'] += 1
                        return None
                
                self.stats['hits'] += 1
                response_time = (time.time() - start_time) * 1000
                self.stats['response_times'].append(response_time)
                
                return cache_data['value']
                
            except Exception as e:
                print(f"Persistent cache read error: {e}")
                self.stats['misses'] += 1
                return None
        
        self.stats['misses'] += 1
        return None
    
    async def set(self, key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Set value in persistent cache"""
        cache_path = self._get_cache_path(key)
        
        try:
            cache_data = {
                'key': key,
                'value': value,
                'created_at': datetime.now().isoformat(),
                'ttl_seconds': ttl_seconds
            }
            
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, default=str)
            
            # Check cache size and evict if necessary
            await self._check_cache_size()
            
            return True
            
        except Exception as e:
            print(f"Persistent cache write error: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete value from persistent cache"""
        import os
        cache_path = self._get_cache_path(key)
        
        try:
            if os.path.exists(cache_path):
                os.remove(cache_path)
                return True
            return False
            
        except Exception as e:
            print(f"Persistent cache delete error: {e}")
            return False
    
    async def clear(self) -> bool:
        """Clear all cache entries"""
        import os
        import shutil
        
        try:
            if os.path.exists(self.cache_dir):
                shutil.rmtree(self.cache_dir)
                os.makedirs(self.cache_dir, exist_ok=True)
            return True
            
        except Exception as e:
            print(f"Persistent cache clear error: {e}")
            return False
    
    async def _check_cache_size(self):
        """Check cache size and evict old entries if necessary"""
        import os
        
        try:
            total_size = 0
            cache_files = []
            
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.cache'):
                    filepath = os.path.join(self.cache_dir, filename)
                    file_size = os.path.getsize(filepath)
                    file_mtime = os.path.getmtime(filepath)
                    
                    total_size += file_size
                    cache_files.append((filepath, file_size, file_mtime))
            
            # Convert to MB
            total_size_mb = total_size / (1024 * 1024)
            
            # Evict oldest files if over limit
            if total_size_mb > self.max_size_mb:
                # Sort by modification time (oldest first)
                cache_files.sort(key=lambda x: x[2])
                
                for filepath, file_size, _ in cache_files:
                    os.remove(filepath)
                    total_size_mb -= file_size / (1024 * 1024)
                    self.stats['evictions'] += 1
                    
                    if total_size_mb <= self.max_size_mb * 0.8:  # Leave some headroom
                        break
                        
        except Exception as e:
            print(f"Cache size check error: {e}")
    
    async def get_stats(self) -> CacheStats:
        """Get persistent cache statistics"""
        import os
        
        total_requests = self.stats['total_requests']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0.0
        
        avg_response_time = (
            sum(self.stats['response_times']) / len(self.stats['response_times'])
            if self.stats['response_times'] else 0.0
        )
        
        # Calculate actual cache size and entry count
        memory_usage = 0
        entry_count = 0
        
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.cache'):
                    filepath = os.path.join(self.cache_dir, filename)
                    memory_usage += os.path.getsize(filepath)
                    entry_count += 1
        except Exception:
            pass
        
        return CacheStats(
            hits=self.stats['hits'],
            misses=self.stats['misses'],
            evictions=self.stats['evictions'],
            total_requests=total_requests,
            hit_rate=hit_rate,
            avg_response_time_ms=avg_response_time,
            memory_usage_bytes=memory_usage,
            entry_count=entry_count
        )
```

**Validation:**
```python
# Test multi-level caching system
from python.helpers.caching_system import MemoryCache, RedisCache, PersistentCache

# Test memory cache
memory_cache = MemoryCache(max_size=100)
await memory_cache.set("test_key", "test_value", ttl_seconds=60)
value = await memory_cache.get("test_key")
print(f"Memory cache test: {value}")

# Test Redis cache (if available)
redis_cache = RedisCache()
await redis_cache.set("test_key", {"data": "test"}, ttl_seconds=300)
value = await redis_cache.get("test_key")
print(f"Redis cache test: {value}")

# Test persistent cache
persistent_cache = PersistentCache()
await persistent_cache.set("test_key", [1, 2, 3, 4, 5])
value = await persistent_cache.get("test_key")
print(f"Persistent cache test: {value}")

# Get statistics
stats = await memory_cache.get_stats()
print(f"Memory cache hit rate: {stats.hit_rate:.2%}")
```

---

**Next Step**: [Query Optimization](query-optimization.md)
