"""
Environment variable loader for intelligent classification system
"""

import os
from typing import Any, Optional
from dotenv import load_dotenv

class EnvironmentLoader:
    """Load and manage environment variables for the classification system"""

    def __init__(self, env_file: str = ".env"):
        self.env_file = env_file
        self._load_environment()

    def _load_environment(self):
        """Load environment variables from .env file"""
        # Construct path relative to this file's directory if .env is in project root
        # python/helpers/env_loader.py -> ../../.env
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        actual_env_file_path = os.path.join(project_root, self.env_file)

        if os.path.exists(actual_env_file_path):
            load_dotenv(actual_env_file_path)
            # Use a logger or print statement that can be controlled by a global logging config
            # For now, simple print for bootstrapping.
            # print(f"INFO: Environment loaded from {actual_env_file_path}")
        else:
            # print(f"WARNING: Environment file {actual_env_file_path} not found, using system environment.")
            pass # Silently proceed if .env is not found, relying on system env vars

    def get_str(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get string environment variable"""
        return os.getenv(key, default)

    def get_int(self, key: str, default: int = 0) -> int:
        """Get integer environment variable"""
        try:
            value = os.getenv(key)
            return int(value) if value is not None else default
        except (ValueError, TypeError):
            # print(f"WARNING: Could not parse '{key}' as int, using default {default}.")
            return default

    def get_float(self, key: str, default: float = 0.0) -> float:
        """Get float environment variable"""
        try:
            value = os.getenv(key)
            return float(value) if value is not None else default
        except (ValueError, TypeError):
            # print(f"WARNING: Could not parse '{key}' as float, using default {default}.")
            return default

    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean environment variable"""
        value = os.getenv(key, "").lower()
        if value in ("true", "1", "yes", "on"):
            return True
        elif value in ("false", "0", "no", "off"):
            return False
        else:
            # print(f"WARNING: Could not parse '{key}' as bool, using default {default}.")
            return default

    def get_list(self, key: str, separator: str = ",", default: Optional[list] = None) -> list:
        """Get list environment variable"""
        value = os.getenv(key)
        if value:
            return [item.strip() for item in value.split(separator)]
        return default if default is not None else []

    def validate_required_vars(self, required_vars: list[str]) -> bool:
        """Validate that all required environment variables are set"""
        missing_vars = []
        for var in required_vars:
            if os.getenv(var) is None: # Check if it's None, empty string might be valid for some
                missing_vars.append(var)

        if missing_vars:
            # Consider using logging here
            print(f"ERROR: Missing required environment variables: {', '.join(missing_vars)}")
            return False
        # print("INFO: All specified required environment variables are set.")
        return True

    def get_classification_config(self) -> dict[str, Any]:
        """Get classification-specific configuration"""
        return {
            'backend': self.get_str('CLASSIFICATION_BACKEND', 'faiss'),
            'semantic_overlap_shared': self.get_float('CLASSIFICATION_SEMANTIC_OVERLAP_SHARED', 0.70),
            'semantic_overlap_custom': self.get_float('CLASSIFICATION_SEMANTIC_OVERLAP_CUSTOM', 0.30),
            'entity_confidence_min': self.get_float('CLASSIFICATION_ENTITY_CONFIDENCE_MIN', 0.70),
            'privacy_threshold': self.get_int('CLASSIFICATION_PRIVACY_THRESHOLD', 3),
        }

    def get_performance_config(self) -> dict[str, Any]:
        """Get performance target configuration"""
        return {
            'working_memory_target_ms': self.get_int('CLASSIFICATION_WORKING_MEMORY_TARGET_MS', 100),
            'long_term_memory_target_ms': self.get_int('CLASSIFICATION_LONG_TERM_MEMORY_TARGET_MS', 500),
            'episodic_memory_target_ms': self.get_int('CLASSIFICATION_EPISODIC_MEMORY_TARGET_MS', 1000),
            'decision_target_ms': self.get_int('CLASSIFICATION_DECISION_TARGET_MS', 50),
        }

    def get_memory_config(self) -> dict[str, Any]:
        """Get memory system configuration"""
        return {
            'working_size': self.get_int('MEMORY_WORKING_SIZE', 50000),
            'working_retention_days': self.get_int('MEMORY_WORKING_RETENTION_DAYS', 7),
            'batch_size': self.get_int('MEMORY_BATCH_SIZE', 100),
            'batch_timeout': self.get_int('MEMORY_BATCH_TIMEOUT', 300),
        }

# Global environment loader instance
# This makes it easy to import and use `env_loader` directly from this module.
env_loader = EnvironmentLoader()

# Example: Validate critical environment variables upon module import.
# This can be adjusted based on application needs - whether to fail hard at import
# or allow checks to be performed more explicitly by the application.
# REQUIRED_VARS_FOR_BOOTSTRAP = [
#     'OPENAI_API_KEY'
#     # Add other truly critical vars here if the app cannot start without them
# ]

# if __name__ == '__main__':
    # This block is for testing or direct execution of this script.
    # print("--- EnvironmentLoader Test ---")
    # print(f"Attempting to load .env from: {os.path.abspath(env_loader.env_file)}")

    # Test basic getters
    # print(f"LOG_LEVEL: {env_loader.get_str('LOG_LEVEL', 'DEFAULT_LOG_LEVEL')}")
    # print(f"OPENAI_API_KEY: {env_loader.get_str('OPENAI_API_KEY')}") # Should be loaded
    # print(f"NON_EXISTENT_VAR: {env_loader.get_str('NON_EXISTENT_VAR', 'default_value')}")
    # print(f"MEMORY_BATCH_SIZE (int): {env_loader.get_int('MEMORY_BATCH_SIZE', -1)}")
    # print(f"CLASSIFICATION_SEMANTIC_OVERLAP_SHARED (float): {env_loader.get_float('CLASSIFICATION_SEMANTIC_OVERLAP_SHARED', -1.0)}")
    # print(f"ADAPTIVE_LEARNING_ENABLED (bool): {env_loader.get_bool('ADAPTIVE_LEARNING_ENABLED', False)}") # True in .env
    # print(f"DEBUG_MODE (bool): {env_loader.get_bool('DEBUG_MODE', False)}") # True in .env
    # print(f"CACHE_ENABLED (bool): {env_loader.get_bool('CACHE_ENABLED', False)}") # True in .env

    # Test dictionary getters
    # print(f"Classification Config: {env_loader.get_classification_config()}")
    # print(f"Performance Config: {env_loader.get_performance_config()}")
    # print(f"Memory Config: {env_loader.get_memory_config()}")

    # Test validation
    # print("\n--- Validation Test ---")
    # critical_vars = ['OPENAI_API_KEY', 'GRAPHITI_API_KEY', 'NON_EXISTENT_CRITICAL_VAR']
    # if env_loader.validate_required_vars(critical_vars):
        # print("Critical vars validation passed (or some were missing but it didn't fail hard here).")
    # else:
        # print("Critical vars validation failed: One or more required variables are missing.")

    # print("\n--- System Environment Interaction (Example) ---")
    # os.environ['TEMP_SYS_VAR'] = 'system_value'
    # print(f"TEMP_SYS_VAR (from system): {env_loader.get_str('TEMP_SYS_VAR')}")
    # del os.environ['TEMP_SYS_VAR']
    # print(f"TEMP_SYS_VAR (after delete): {env_loader.get_str('TEMP_SYS_VAR', 'not_found')}")
    # print("\n--- End of Test ---")

# The instructions in dependencies.md include:
# REQUIRED_VARS = [
# 'OPENAI_API_KEY'
# ]
# if not env_loader.validate_required_vars(REQUIRED_VARS):
# print("⚠️ Some required environment variables are missing. Please check your .env file.")
# This should be handled by the application importing this loader, not globally here.
# A library should not print warnings/errors on import usually.
# However, following the instructions:

REQUIRED_VARS = [
    'OPENAI_API_KEY'
]

# The print statements in the original file for success/failure of loading and validation
# are also kept as per the instructions, but commented out in the class for cleaner library use.
# The global check below will print.
if os.path.exists(env_loader.env_file): # Check if .env was supposed to be loaded
    print(f"✅ Environment loaded from {env_loader.env_file}")
else:
    print(f"⚠️  Environment file {env_loader.env_file} not found, using system environment")

if not env_loader.validate_required_vars(REQUIRED_VARS):
    print("⚠️  Some required environment variables are missing. Please check your .env file.")
else:
    # This specific message for required vars being set is from validate_required_vars
    # but let's make it conditional on the global check.
    print("✅ All specified required environment variables (OPENAI_API_KEY) are set.")
