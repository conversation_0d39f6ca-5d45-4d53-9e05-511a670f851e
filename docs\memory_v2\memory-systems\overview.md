# Memory Systems Overview - Agent Zero v2.0

Agent Zero v2.0 features a sophisticated memory architecture that supports multiple backends, each optimized for different use cases. This overview explains the memory system design, capabilities, and how to choose the right backend for your needs.

## Memory Architecture

### Unified Memory Abstraction Layer

Agent Zero v2.0 introduces a unified memory abstraction that provides a consistent interface across different memory backends:

```
┌─────────────────────────────────────────────────────────────┐
│                    Agent Core Logic                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              Memory Abstraction Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Content Type    │  │ Backend Router  │  │ Auto Recall  │ │
│  │ Detection       │  │                 │  │ System       │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
        ┌─────────────┴─────────────┐
        │                           │
┌───────▼────────┐         ┌────────▼────────┐
│ FAISS Backend  │         │ Graphiti Backend│
│                │         │                 │
│ Vector Search  │         │ Knowledge Graph │
│ Fast Retrieval │         │ Temporal Queries│
│ Local Storage  │         │ Entity Relations│
└────────────────┘         └─────────────────┘
```

### Key Features

**Automatic Backend Selection:**
- Intelligent fallback from advanced to basic backends
- Runtime switching based on availability and requirements
- Graceful degradation when advanced features aren't available

**Content Type Detection:**
- Automatic classification of agent memory vs. knowledge documents
- Different processing pipelines for different content types
- Optimized storage and retrieval strategies

**Unified Query Interface:**
- Single API for all memory operations regardless of backend
- Consistent results format across different backends
- Transparent backend switching without code changes

## Memory Backends Comparison

| Feature | FAISS Backend | Graphiti Backend | Auto Backend |
|---------|---------------|------------------|--------------|
| **Setup Complexity** | Low | High | Low |
| **External Dependencies** | None | Neo4j, OpenAI API | None required |
| **Query Types** | Similarity search | Temporal, relational, similarity | Both |
| **Relationship Modeling** | Limited | Advanced | Advanced when available |
| **Entity Extraction** | No | Yes | Yes when available |
| **Time-based Queries** | No | Yes | Yes when available |
| **Bulk Processing** | Basic | Advanced | Advanced when available |
| **Performance** | Fast | Moderate | Optimized per backend |
| **Storage** | Local files | Graph database | Hybrid |

## Memory Types

### Agent Memory (Episodes)
**Purpose**: Store conversation history, agent thoughts, and decision-making context

**Characteristics:**
- Chronological organization
- Context-aware retrieval
- Automatic summarization
- Relevance-based filtering

**Storage Format:**
```json
{
  "id": "episode_uuid",
  "content": "Agent conversation or thought",
  "timestamp": "2024-06-18T10:30:00Z",
  "area": "main",
  "metadata": {
    "agent_id": "agent_0",
    "conversation_id": "chat_123",
    "importance": 0.8
  }
}
```

### Knowledge Documents
**Purpose**: Store imported documents, reference materials, and structured knowledge

**Characteristics:**
- Entity and relationship extraction (Graphiti only)
- Hierarchical organization
- Cross-document linking
- Semantic search capabilities

**Storage Format:**
```json
{
  "id": "document_uuid",
  "content": "Document content",
  "source_uri": "file://path/to/document.pdf",
  "entities": ["Entity1", "Entity2"],
  "relationships": [
    {"from": "Entity1", "to": "Entity2", "type": "related_to"}
  ],
  "metadata": {
    "document_type": "pdf",
    "import_date": "2024-06-18T10:30:00Z",
    "chunk_index": 0
  }
}
```

## Memory Operations

### Core Operations

**Save Memory:**
```python
# Agent memory (simple episode)
memory_id = await memory.save_memory(
    text="User asked about Python programming",
    area="main",
    metadata={"importance": 0.7}
)

# Knowledge document (with entity extraction)
doc_id = await memory.import_knowledge(
    content="Python is a programming language...",
    source="python_guide.pdf",
    extract_entities=True
)
```

**Retrieve Memories:**
```python
# Similarity-based retrieval
memories = await memory.search(
    query="Python programming",
    limit=10,
    threshold=0.7
)

# Temporal queries (Graphiti only)
recent_memories = await memory.search_temporal(
    query="programming languages",
    time_range="last_week",
    limit=5
)
```

**Bulk Operations:**
```python
# Bulk import for large knowledge bases
results = await memory.bulk_import(
    documents=document_list,
    batch_size=100,
    extract_entities=True
)
```

### Advanced Queries (Graphiti Backend)

**Entity-based Queries:**
```python
# Find all memories related to specific entities
memories = await memory.search_by_entities(
    entities=["Python", "programming"],
    relationship_types=["uses", "related_to"]
)
```

**Temporal Relationship Queries:**
```python
# Find how relationships evolved over time
evolution = await memory.get_relationship_evolution(
    entity1="Python",
    entity2="web_development",
    time_range="last_month"
)
```

**Graph Traversal:**
```python
# Find connected concepts
connected = await memory.find_connected_entities(
    start_entity="Python",
    max_depth=3,
    relationship_types=["uses", "enables", "related_to"]
)
```

## Memory Configuration

### Backend Selection Strategy

**Auto Backend (Recommended):**
```python
# Attempts Graphiti first, falls back to FAISS
config = {
    "backend_type": "auto",
    "fallback_strategy": "graceful",
    "prefer_advanced": True
}
```

**Explicit Backend Selection:**
```python
# Force specific backend
config = {
    "backend_type": "graphiti",  # or "faiss"
    "fail_on_unavailable": False
}
```

### Performance Tuning

**FAISS Optimization:**
```python
config = {
    "index_type": "gpu",  # Use GPU acceleration
    "similarity_threshold": 0.75,
    "max_results": 20,
    "enable_compression": True
}
```

**Graphiti Optimization:**
```python
config = {
    "bulk_batch_size": 200,
    "bulk_timeout": 600,
    "entity_extraction_threshold": 0.8,
    "relationship_confidence": 0.7
}
```

## Memory Lifecycle

### Automatic Memory Management

**Memory Recall:**
- Triggered automatically during conversations
- Context-aware relevance scoring
- Intelligent memory selection based on current topic

**Memory Consolidation:**
- Periodic summarization of old memories
- Duplicate detection and merging
- Importance-based retention policies

**Memory Cleanup:**
- Automatic removal of low-importance memories
- Configurable retention periods
- Privacy-compliant data deletion

### Manual Memory Management

**Memory Inspection:**
```python
# View memory statistics
stats = await memory.get_statistics()
print(f"Total memories: {stats['total_count']}")
print(f"Storage size: {stats['storage_size']}")

# Browse memories by area
memories = await memory.list_memories(
    area="main",
    limit=50,
    sort_by="timestamp"
)
```

**Memory Maintenance:**
```python
# Clean up old memories
await memory.cleanup_old_memories(
    older_than="30_days",
    importance_threshold=0.3
)

# Rebuild indices
await memory.rebuild_indices()
```

## Integration with Agent System

### Automatic Integration

**Memory Extensions:**
- Automatic memory saving during conversations
- Background memory recall based on context
- Intelligent memory injection into prompts

**Tool Integration:**
- Memory-aware tool execution
- Tool results automatically saved to memory
- Cross-tool memory sharing

### Custom Integration

**Memory Hooks:**
```python
# Custom memory processing
@memory.on_save
async def process_memory(memory_data):
    # Custom processing logic
    enhanced_data = await enhance_memory(memory_data)
    return enhanced_data

@memory.on_recall
async def filter_memories(memories, context):
    # Custom filtering logic
    return filter_relevant_memories(memories, context)
```

## Best Practices

### Backend Selection
- **Start with Auto**: Let the system choose the best available backend
- **Use FAISS for simplicity**: When you need quick setup and basic functionality
- **Use Graphiti for complexity**: When you need advanced relationships and temporal queries

### Memory Organization
- **Use meaningful areas**: Organize memories by topic or project
- **Set appropriate importance**: Help the system prioritize memories
- **Regular maintenance**: Periodically clean up and organize memories

### Performance Optimization
- **Batch operations**: Use bulk imports for large knowledge bases
- **Tune thresholds**: Adjust similarity and confidence thresholds for your use case
- **Monitor usage**: Keep track of memory usage and performance metrics

## Next Steps

- **[FAISS Backend](faiss-backend.md)**: Detailed FAISS configuration and usage
- **[Graphiti Backend](graphiti-backend.md)**: Advanced Graphiti features and setup
- **[Memory Configuration](configuration.md)**: Complete configuration reference
- **[Knowledge Management](knowledge-management.md)**: Document import and processing
