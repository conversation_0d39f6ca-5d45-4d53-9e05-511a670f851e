from typing import Dict, Any, <PERSON>, Optional, Tuple
from dataclasses import dataclass, field # Added field for default_factory
from datetime import datetime, timezone, timedelta # Added timezone
from collections import defaultdict, Counter
import statistics # For mean calculation
import asyncio # For async method signatures

# Import user preferences directly - no fallbacks or mocks
# This enforces proper dependency management and prevents silent failures
from .user_preferences import UserFeedback, LearningSignal


@dataclass
class BehaviorPattern:
    """Represents a detected behavioral pattern"""
    pattern_type: str
    pattern_data: Dict[str, Any]
    confidence: float
    frequency: int
    first_observed: datetime
    last_observed: datetime
    impact_score: float # Normalized score indicating pattern's influence

@dataclass
class UsageMetrics:
    """Usage metrics for pattern analysis"""
    total_interactions: int = 0
    avg_session_length_minutes: float = 0.0 # Changed from avg_session_length
    preferred_domains: List[str] = field(default_factory=list)
    search_patterns: Dict[str, int] = field(default_factory=dict)
    feedback_ratio: float = 0.0 # positive_feedback / total_feedback or similar
    error_rate: float = 0.0 # errors / total_interactions


class BehavioralPatternAnalyzer:
    """
    Analyzes user behavior patterns for preference learning
    """

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.interaction_history: List[Dict[str, Any]] = []
        self.detected_patterns: List[BehaviorPattern] = []
        self.usage_metrics = UsageMetrics() # Uses dataclass defaults

        # Pattern detection thresholds from spec
        self.min_pattern_frequency = 3
        self.pattern_confidence_threshold = 0.6
        self.analysis_window_days = 30

    async def record_interaction(
        self,
        interaction_type: str,
        context: Dict[str, Any],
        timestamp: Optional[datetime] = None
    ) -> None:
        """Record user interaction for pattern analysis"""
        # Ensure timestamp is timezone-aware (UTC)
        ts = timestamp or datetime.now(timezone.utc)
        if ts.tzinfo is None:
            ts = ts.replace(tzinfo=timezone.utc) # Should not happen if now(timezone.utc) is used

        interaction = {
            'type': interaction_type,
            'context': context, # context should contain all relevant data like 'domain', 'success', 'session_id' etc.
            'timestamp': ts,
            'session_id': context.get('session_id', 'default_session') # Ensure session_id is present
        }
        self.interaction_history.append(interaction)

        # Keep history manageable
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.analysis_window_days)
        self.interaction_history = [
            i for i in self.interaction_history
            if i['timestamp'] > cutoff_date
        ]
        await self._update_usage_metrics() # Update metrics after each interaction

    async def analyze_patterns(self) -> List[BehaviorPattern]:
        """Analyze interaction history to detect behavioral patterns"""
        if len(self.interaction_history) < 10: # Minimum interactions to find meaningful patterns
            return []

        patterns = []
        patterns.extend(await self._detect_domain_patterns())
        patterns.extend(await self._detect_search_patterns())
        patterns.extend(await self._detect_feedback_patterns())
        patterns.extend(await self._detect_temporal_patterns())

        significant_patterns = [
            p for p in patterns
            if p.confidence >= self.pattern_confidence_threshold and                p.frequency >= self.min_pattern_frequency
        ]
        self.detected_patterns = significant_patterns
        return significant_patterns

    async def _detect_domain_patterns(self) -> List[BehaviorPattern]:
        patterns = []

        def _default_domain_data():
            return {'timestamps': [], 'successes': 0, 'confidences': []}

        domain_interactions_data: Dict[str, Dict[str, Any]] = defaultdict(_default_domain_data)

        for interaction in self.interaction_history:
            domain = interaction['context'].get('domain')
            if domain:
                domain_interactions_data[domain]['timestamps'].append(interaction['timestamp'])
                if interaction['context'].get('success', False): # Assuming 'success' boolean in context
                    domain_interactions_data[domain]['successes'] += 1
                domain_interactions_data[domain]['confidences'].append(interaction['context'].get('confidence', 0.5))

        for domain, data in domain_interactions_data.items():
            interaction_count = len(data['timestamps'])
            if interaction_count >= self.min_pattern_frequency:
                success_rate = data['successes'] / interaction_count if interaction_count > 0 else 0
                avg_confidence = statistics.mean(data['confidences']) if data['confidences'] else 0.5
                pattern_confidence = (success_rate + avg_confidence) / 2 # As per spec

                if pattern_confidence >= self.pattern_confidence_threshold: # Check against general threshold
                    patterns.append(BehaviorPattern(
                        pattern_type='domain_preference',
                        pattern_data={
                            'domain': domain, 'success_rate': success_rate,
                            'avg_confidence': avg_confidence, 'interaction_count': interaction_count
                        },
                        confidence=pattern_confidence,
                        frequency=interaction_count,
                        first_observed=min(data['timestamps']),
                        last_observed=max(data['timestamps']),
                        impact_score=interaction_count / len(self.interaction_history) if self.interaction_history else 0
                    ))
        return patterns

    async def _detect_search_patterns(self) -> List[BehaviorPattern]:
        patterns = []
        search_interactions = [i for i in self.interaction_history if i['type'] == 'search']

        if len(search_interactions) < self.min_pattern_frequency:
            return patterns

        # Result usage pattern
        results_used_list = [i['context'].get('results_used', 0) for i in search_interactions if 'results_used' in i['context']]
        result_limits_list = [i['context'].get('result_limit', 10) for i in search_interactions if 'result_limit' in i['context']]

        if results_used_list and result_limits_list and len(results_used_list) == len(result_limits_list): # ensure lists are not empty and aligned
            avg_results_used = statistics.mean(results_used_list)
            avg_limit_requested = statistics.mean(result_limits_list)
            if avg_limit_requested > 0 and (avg_results_used < avg_limit_requested * 0.6): # Spec condition
                patterns.append(BehaviorPattern(
                    pattern_type='search_result_preference',
                    pattern_data={'avg_results_used': avg_results_used, 'avg_limit_requested': avg_limit_requested,
                                  'efficiency_ratio': avg_results_used / avg_limit_requested if avg_limit_requested else 0},
                    confidence=0.8, # Spec fixed confidence
                    frequency=len(search_interactions),
                    first_observed=min(i['timestamp'] for i in search_interactions),
                    last_observed=max(i['timestamp'] for i in search_interactions),
                    impact_score=0.3 # Spec fixed impact
                ))

        # Search timing pattern
        search_hours = [i['timestamp'].hour for i in search_interactions]
        if search_hours:
            hour_counts = Counter(search_hours)
            most_common_hour, count = hour_counts.most_common(1)[0]
            if count >= len(search_interactions) * 0.4: # Spec condition: 40% in most common hour
                 patterns.append(BehaviorPattern(
                    pattern_type='search_timing_preference',
                    pattern_data={'preferred_hour': most_common_hour, 'frequency_at_hour': count,
                                  'total_searches': len(search_interactions)},
                    confidence=0.7, # Spec fixed confidence
                    frequency=count,
                    first_observed=min(i['timestamp'] for i in search_interactions),
                    last_observed=max(i['timestamp'] for i in search_interactions),
                    impact_score=0.2 # Spec fixed impact
                ))
        return patterns

    async def _detect_feedback_patterns(self) -> List[BehaviorPattern]:
        patterns = []
        feedback_interactions = [i for i in self.interaction_history if i['type'] == 'feedback']

        if len(feedback_interactions) < self.min_pattern_frequency:
            return patterns

        feedback_type_counts = Counter(i['context'].get('feedback_type', 'unknown') for i in feedback_interactions)
        total_feedback_interactions = len(feedback_interactions)

        for fb_type, count in feedback_type_counts.items():
            if count >= self.min_pattern_frequency:
                percentage = count / total_feedback_interactions if total_feedback_interactions else 0
                # Timestamps for specific feedback type
                type_timestamps = [i['timestamp'] for i in feedback_interactions if i['context'].get('feedback_type') == fb_type]
                if not type_timestamps: continue # Should not happen if count > 0

                patterns.append(BehaviorPattern(
                    pattern_type='feedback_behavior',
                    pattern_data={'feedback_type': fb_type, 'frequency': count, 'percentage': percentage},
                    confidence=min(0.9, percentage + 0.3), # Spec formula
                    frequency=count,
                    first_observed=min(type_timestamps),
                    last_observed=max(type_timestamps),
                    impact_score=count / len(self.interaction_history) if self.interaction_history else 0
                ))
        return patterns

    async def _detect_temporal_patterns(self) -> List[BehaviorPattern]:
        patterns = []
        if len(self.interaction_history) < 20: # Higher threshold for temporal patterns
            return patterns

        daily_counts = Counter(i['timestamp'].strftime('%A') for i in self.interaction_history)
        total_interactions = len(self.interaction_history)

        for day, count in daily_counts.items():
            if count >= total_interactions * 0.2: # Spec: 20% of interactions on this day
                day_timestamps = [i['timestamp'] for i in self.interaction_history if i['timestamp'].strftime('%A') == day]
                if not day_timestamps: continue

                patterns.append(BehaviorPattern(
                    pattern_type='temporal_usage',
                    pattern_data={'peak_day': day, 'interaction_count': count,
                                  'percentage': count / total_interactions if total_interactions else 0},
                    confidence=0.7, # Spec fixed confidence
                    frequency=count,
                    first_observed=min(day_timestamps), # Min timestamp for that peak day
                    last_observed=max(day_timestamps),   # Max timestamp for that peak day
                    impact_score=0.1 # Spec fixed impact
                ))
        return patterns

    async def _update_usage_metrics(self) -> None:
        """Update overall usage metrics based on the current interaction_history"""
        if not self.interaction_history:
            self.usage_metrics = UsageMetrics() # Reset if history is empty
            return

        self.usage_metrics.total_interactions = len(self.interaction_history)

        # Session length
        sessions = defaultdict(list)
        for interaction in self.interaction_history:
            sessions[interaction['session_id']].append(interaction['timestamp'])

        session_lengths_minutes = []
        for session_id, timestamps in sessions.items():
            if len(timestamps) > 1:
                session_lengths_minutes.append((max(timestamps) - min(timestamps)).total_seconds() / 60.0)
        self.usage_metrics.avg_session_length_minutes = statistics.mean(session_lengths_minutes) if session_lengths_minutes else 0.0

        # Preferred domains
        domain_counts = Counter(i['context']['domain'] for i in self.interaction_history if 'domain' in i['context'])
        self.usage_metrics.preferred_domains = [domain for domain, count in domain_counts.most_common(5)]

        # Search patterns (by query_type if available)
        search_interactions = [i for i in self.interaction_history if i['type'] == 'search']
        self.usage_metrics.search_patterns = dict(Counter(i['context'].get('query_type', 'general_search') for i in search_interactions))

        # Feedback ratio
        feedback_count = sum(1 for i in self.interaction_history if i['type'] == 'feedback')
        self.usage_metrics.feedback_ratio = feedback_count / self.usage_metrics.total_interactions if self.usage_metrics.total_interactions else 0.0

        # Error rate
        error_count = sum(1 for i in self.interaction_history if i['context'].get('error', False))
        self.usage_metrics.error_rate = error_count / self.usage_metrics.total_interactions if self.usage_metrics.total_interactions else 0.0

    async def get_pattern_recommendations(self) -> List[Dict[str, Any]]:
        """Get recommendations based on currently detected patterns"""
        recommendations = []
        # Ensure patterns are analyzed before generating recommendations
        if not self.detected_patterns: # Or if patterns are stale
            await self.analyze_patterns()

        for pattern in self.detected_patterns:
            if pattern.pattern_type == 'domain_preference' and pattern.pattern_data.get('success_rate', 0) > 0.8:
                recommendations.append({
                    'type': 'increase_domain_weight', 'domain': pattern.pattern_data['domain'],
                    'current_success_rate': pattern.pattern_data['success_rate'],
                    'recommended_weight_increase': 0.1, 'confidence': pattern.confidence
                })
            elif pattern.pattern_type == 'search_result_preference': # Assumes 'avg_results_used' exists
                 avg_used = pattern.pattern_data.get('avg_results_used', 0)
                 recommendations.append({
                    'type': 'adjust_search_limit', 'current_avg_used': avg_used,
                    'recommended_limit': max(1, int(avg_used * 1.2)), # Ensure limit is at least 1
                    'confidence': pattern.confidence
                })
            elif pattern.pattern_type == 'feedback_behavior' and                  pattern.pattern_data.get('feedback_type') == LearningSignal.NEGATIVE_FEEDBACK.value and                  pattern.pattern_data.get('percentage', 0) > 0.3:
                recommendations.append({
                    'type': 'review_classification_thresholds',
                    'negative_feedback_rate': pattern.pattern_data['percentage'],
                    'recommended_action': 'lower_confidence_threshold', 'confidence': pattern.confidence
                })
        return recommendations

    async def export_analysis_report(self) -> Dict[str, Any]:
        """Export comprehensive analysis report"""
        # Ensure metrics and patterns are up-to-date
        await self._update_usage_metrics()
        if not self.detected_patterns: # Or if patterns are stale
             await self.analyze_patterns()

        return {
            'user_id': self.user_id,
            'analysis_timestamp': datetime.now(timezone.utc).isoformat(),
            'analysis_period_days': self.analysis_window_days,
            'usage_metrics': self.usage_metrics.__dict__, # Convert dataclass to dict
            'detected_patterns': [p.__dict__ for p in self.detected_patterns], # Convert list of dataclasses
            'recommendations': await self.get_pattern_recommendations()
        }

if __name__ == "__main__":
    async def test_behavioral_analyzer_main():
        print("--- Testing BehavioralPatternAnalyzer ---")
        analyzer = BehavioralPatternAnalyzer("user_main_test")

        # Record sample interactions (diverse enough to trigger some patterns)
        base_ts = datetime.now(timezone.utc)
        interactions = [
            # Domain A: high success
            {'type': 'classification', 'context': {'domain': 'domainA', 'success': True, 'confidence': 0.9, 'session_id': 's1'}, 'timestamp': base_ts - timedelta(days=2, hours=1)},
            {'type': 'classification', 'context': {'domain': 'domainA', 'success': True, 'confidence': 0.8, 'session_id': 's1'}, 'timestamp': base_ts - timedelta(days=2, hours=2)},
            {'type': 'classification', 'context': {'domain': 'domainA', 'success': True, 'confidence': 0.85, 'session_id': 's2'}, 'timestamp': base_ts - timedelta(days=1)},
            # Domain B: mixed
            {'type': 'classification', 'context': {'domain': 'domainB', 'success': False, 'confidence': 0.4, 'error': True, 'session_id': 's1'}, 'timestamp': base_ts - timedelta(days=2)},
            {'type': 'classification', 'context': {'domain': 'domainB', 'success': True, 'confidence': 0.7, 'session_id': 's3'}, 'timestamp': base_ts - timedelta(days=1)},
            # Searches
            {'type': 'search', 'context': {'query_type': 'keyword', 'results_used': 2, 'result_limit': 10, 'session_id': 's1'}, 'timestamp': base_ts - timedelta(hours=5)},
            {'type': 'search', 'context': {'query_type': 'semantic', 'results_used': 8, 'result_limit': 15, 'session_id': 's2'}, 'timestamp': base_ts - timedelta(hours=3)},
            {'type': 'search', 'context': {'query_type': 'keyword', 'results_used': 1, 'result_limit': 5, 'session_id': 's3'}, 'timestamp': base_ts - timedelta(hours=1)},
             # Feedback
            {'type': 'feedback', 'context': {'feedback_type': LearningSignal.POSITIVE_FEEDBACK.value, 'item_id': 'id1', 'session_id': 's1'}, 'timestamp': base_ts - timedelta(days=1, hours=1)},
            {'type': 'feedback', 'context': {'feedback_type': LearningSignal.NEGATIVE_FEEDBACK.value, 'item_id': 'id2', 'session_id': 's2'}, 'timestamp': base_ts - timedelta(days=1, hours=2)},
            {'type': 'feedback', 'context': {'feedback_type': LearningSignal.NEGATIVE_FEEDBACK.value, 'item_id': 'id3', 'session_id': 's3'}, 'timestamp': base_ts - timedelta(days=1, hours=3)},
            {'type': 'feedback', 'context': {'feedback_type': LearningSignal.NEGATIVE_FEEDBACK.value, 'item_id': 'id4', 'session_id': 's3'}, 'timestamp': base_ts - timedelta(days=1, hours=4)}, # 3 negative
        ]
        # Add more interactions to meet the minimum of 10 for analysis
        for i in range(10 - len(interactions)):
             interactions.append({'type': 'generic', 'context': {'domain': 'domainC', 'success': True, 'confidence':0.6, 'session_id': f's{i+4}'}, 'timestamp': base_ts - timedelta(days=3, hours=i)})


        for inter in interactions:
            await analyzer.record_interaction(inter['type'], inter['context'], inter['timestamp'])

        print(f"Recorded {len(analyzer.interaction_history)} interactions.")

        # Analyze patterns
        patterns = await analyzer.analyze_patterns()
        print(f"\nDetected {len(patterns)} significant behavioral patterns:")
        for p in patterns:
            print(f"  - Type: {p.pattern_type}, Data: {p.pattern_data}, Confidence: {p.confidence:.2f}, Freq: {p.frequency}, Impact: {p.impact_score:.2f}")

        # Get recommendations
        recommendations = await analyzer.get_pattern_recommendations()
        print(f"\nGenerated {len(recommendations)} recommendations:")
        for r in recommendations:
            print(f"  - Type: {r['type']}, Details: { {k:v for k,v in r.items() if k!='type'} }")

        # Export report
        report = await analyzer.export_analysis_report()
        print(f"\n--- Analysis Report for {report['user_id']} ---")
        print(f"Timestamp: {report['analysis_timestamp']}")
        print(f"Total Interactions in window: {report['usage_metrics']['total_interactions']}")
        print(f"Avg Session Length (min): {report['usage_metrics']['avg_session_length_minutes']:.2f}")
        print(f"Preferred Domains: {report['usage_metrics']['preferred_domains']}")
        print(f"Error Rate: {report['usage_metrics']['error_rate']:.2%}")
        print(f"Number of Detected Patterns: {len(report['detected_patterns'])}")
        print(f"Number of Recommendations: {len(report['recommendations'])}")

        # Example assertions (can be more specific)
        assert len(patterns) > 0, "Expected some patterns to be detected with diverse data."
        # One specific check based on data: domainA preference
        domain_a_pattern_found = any(p.pattern_type == 'domain_preference' and p.pattern_data.get('domain') == 'domainA' for p in patterns)
        assert domain_a_pattern_found, "Expected to detect a preference for domainA."

        # Check for negative feedback pattern if applicable
        negative_feedback_pattern = any(p.pattern_type == 'feedback_behavior' and p.pattern_data.get('feedback_type') == LearningSignal.NEGATIVE_FEEDBACK.value for p in patterns)
        if len([i for i in interactions if i['type'] == 'feedback' and i['context'].get('feedback_type') == LearningSignal.NEGATIVE_FEEDBACK.value]) >= analyzer.min_pattern_frequency:
            assert negative_feedback_pattern, "Expected negative feedback pattern."


        print("\n--- BehavioralPatternAnalyzer Test Completed ---")

    asyncio.run(test_behavioral_analyzer_main())
