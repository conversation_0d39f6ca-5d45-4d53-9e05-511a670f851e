# Phase 2: Classification Engine - Intelligent Engine
## Core Intelligent Classification Engine Implementation

This document provides implementation for the core intelligent classification engine that makes data routing decisions using quantitative metrics and decision matrices.

## 🧠 Engine Architecture

### Decision-Making Process
1. **Entity Extraction**: Identify entities and their types
2. **Metric Calculation**: Compute quantitative classification metrics
3. **Decision Matrix**: Apply rules-based decision logic
4. **Confidence Assessment**: Calculate confidence scores
5. **Result Validation**: Validate and finalize classification

## 🔧 Implementation

### Step 2.1: Core Intelligent Classification Engine

**File:** `python/helpers/intelligent_classification_engine.py`
**Action:** Create new file

```python
"""
Intelligent Classification Engine - Core implementation
"""

import asyncio
import time
import re
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timedelta
from collections import defaultdict
import hashlib

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

from .classification_engine import (
    ClassificationEngine, ClassificationResult, ClassificationMetrics,
    ClassificationRequest, ClassificationStatus
)
from .classification_config import ClassificationConfig, ClassificationStrategy
from .entity_relationship_analyzer import EntityRelationshipAnalyzer

class IntelligentClassificationEngine(ClassificationEngine):
    """
    Intelligent classification engine with quantitative decision making
    """
    
    def __init__(self, config: ClassificationConfig):
        super().__init__(config)
        
        # Core components
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.relationship_analyzer = EntityRelationshipAnalyzer()
        
        # Caching
        self.entity_cache: Dict[str, List[Dict[str, Any]]] = {}
        self.schema_cache: Dict[str, List[Dict[str, Any]]] = {}
        self.classification_cache: Dict[str, ClassificationResult] = {}
        
        # Domain knowledge
        self.domain_keywords = self._initialize_domain_keywords()
        
        # Performance optimization
        self.batch_entity_extraction = True
        self.parallel_processing = True
    
    async def _initialize_engine(self):
        """Initialize the intelligent classification engine"""
        
        # Initialize TF-IDF with sample documents
        sample_docs = [
            "programming code function class variable",
            "medical patient diagnosis treatment",
            "legal contract agreement law",
            "financial investment portfolio market",
            "academic research paper study"
        ]
        
        try:
            self.tfidf_vectorizer.fit(sample_docs)
        except Exception as e:
            print(f"Warning: TF-IDF initialization failed: {e}")
        
        # Pre-load existing schemas
        await self._load_existing_schemas()
        
        print("Intelligent Classification Engine initialized")
    
    def _initialize_domain_keywords(self) -> Dict[str, List[str]]:
        """Initialize domain-specific keywords for classification"""
        
        return {
            'programming': [
                'function', 'class', 'variable', 'method', 'api', 'code', 'script',
                'algorithm', 'data structure', 'framework', 'library', 'module',
                'python', 'javascript', 'java', 'c++', 'sql', 'html', 'css',
                'debug', 'compile', 'execute', 'runtime', 'syntax', 'semantic'
            ],
            'medical': [
                'patient', 'diagnosis', 'treatment', 'medication', 'symptom',
                'disease', 'therapy', 'clinical', 'medical', 'health', 'doctor',
                'nurse', 'hospital', 'clinic', 'surgery', 'prescription',
                'anatomy', 'physiology', 'pathology', 'radiology'
            ],
            'legal': [
                'contract', 'agreement', 'law', 'legal', 'court', 'judge',
                'attorney', 'lawyer', 'litigation', 'settlement', 'clause',
                'statute', 'regulation', 'compliance', 'liability', 'damages',
                'jurisdiction', 'precedent', 'testimony', 'evidence'
            ],
            'finance': [
                'investment', 'portfolio', 'market', 'stock', 'bond', 'asset',
                'liability', 'revenue', 'profit', 'loss', 'budget', 'financial',
                'accounting', 'audit', 'tax', 'banking', 'credit', 'debt',
                'equity', 'dividend', 'interest', 'capital', 'valuation'
            ],
            'academic': [
                'research', 'study', 'paper', 'journal', 'publication', 'thesis',
                'dissertation', 'academic', 'university', 'college', 'professor',
                'student', 'education', 'learning', 'knowledge', 'theory',
                'methodology', 'analysis', 'experiment', 'hypothesis'
            ],
            'business': [
                'company', 'corporation', 'business', 'enterprise', 'organization',
                'management', 'strategy', 'marketing', 'sales', 'customer',
                'client', 'product', 'service', 'project', 'team', 'meeting',
                'presentation', 'proposal', 'contract', 'partnership'
            ]
        }
    
    async def classify_content(
        self, 
        content: str, 
        metadata: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
        """
        Main classification method using intelligent decision making
        """
        
        start_time = time.time()
        operation_id = self.performance_tracker.start_timing()
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(content, metadata)
            if self.config.cache.enabled and cache_key in self.classification_cache:
                cached_result = self.classification_cache[cache_key]
                cached_result.cache_hit = True
                self.performance_tracker.record_classification(cached_result)
                return cached_result
            
            # Step 1: Extract entities
            entities = await self.extract_entities(content)
            
            # Step 2: Calculate classification metrics
            metrics = await self._calculate_classification_metrics(
                content, entities, metadata, context
            )
            
            # Step 3: Apply decision matrix
            strategy, confidence, reasoning = self._apply_decision_matrix(metrics)
            
            # Step 4: Determine storage location
            namespace, domain = self._determine_storage_location(strategy, metrics, metadata)
            
            # Step 5: Create result
            processing_time = self.performance_tracker.end_timing(operation_id)
            
            result = ClassificationResult(
                strategy=strategy,
                confidence=confidence,
                metrics=metrics,
                reasoning=reasoning,
                processing_time_ms=processing_time,
                namespace=namespace,
                domain=domain,
                cache_hit=False
            )
            
            # Cache result
            if self.config.cache.enabled:
                self.classification_cache[cache_key] = result
                
                # Limit cache size
                if len(self.classification_cache) > self.config.cache.max_size_mb * 100:
                    # Remove oldest entries (simple FIFO)
                    oldest_keys = list(self.classification_cache.keys())[:100]
                    for key in oldest_keys:
                        del self.classification_cache[key]
            
            # Record performance
            self.performance_tracker.record_classification(result)
            
            return result
            
        except Exception as e:
            # Handle errors gracefully
            processing_time = self.performance_tracker.end_timing(operation_id)
            
            error_result = ClassificationResult(
                strategy=ClassificationStrategy.ISOLATED_NAMESPACE,
                confidence=0.5,
                metrics=ClassificationMetrics(privacy_flags=True),
                reasoning=f"Classification failed: {str(e)}. Using safe fallback.",
                processing_time_ms=processing_time,
                status=ClassificationStatus.FAILED,
                validation_passed=False,
                validation_warnings=[f"Error: {str(e)}"],
                fallback_used=True
            )
            
            self.performance_tracker.record_classification(error_result)
            return error_result
    
    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        Extract entities from content using multiple approaches
        """
        
        if not content or not content.strip():
            return []
        
        # Check cache
        content_hash = hashlib.md5(content.encode()).hexdigest()
        if content_hash in self.entity_cache:
            return self.entity_cache[content_hash]
        
        entities = []
        
        # 1. Extract basic entities using regex patterns
        entities.extend(await self._extract_basic_entities(content))
        
        # 2. Extract domain-specific entities
        entities.extend(await self._extract_domain_entities(content))
        
        # 3. Extract privacy-sensitive entities
        entities.extend(await self._extract_privacy_entities(content))
        
        # 4. Calculate confidence scores
        for entity in entities:
            entity['confidence_score'] = self._calculate_entity_confidence(entity)
        
        # Cache results
        self.entity_cache[content_hash] = entities
        
        # Limit cache size
        if len(self.entity_cache) > 1000:
            # Remove oldest entries
            oldest_keys = list(self.entity_cache.keys())[:100]
            for key in oldest_keys:
                del self.entity_cache[key]
        
        return entities
    
    async def _extract_basic_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extract basic entities using regex patterns"""
        
        entities = []
        
        # Email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        for match in re.finditer(email_pattern, content):
            entities.append({
                'text': match.group(),
                'label': 'EMAIL',
                'start': match.start(),
                'end': match.end(),
                'pattern_type': 'regex'
            })
        
        # URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        for match in re.finditer(url_pattern, content):
            entities.append({
                'text': match.group(),
                'label': 'URL',
                'start': match.start(),
                'end': match.end(),
                'pattern_type': 'regex'
            })
        
        # Phone numbers (simple pattern)
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        for match in re.finditer(phone_pattern, content):
            entities.append({
                'text': match.group(),
                'label': 'PHONE',
                'start': match.start(),
                'end': match.end(),
                'pattern_type': 'regex'
            })
        
        # Dates (simple patterns)
        date_patterns = [
            r'\b\d{1,2}/\d{1,2}/\d{4}\b',  # MM/DD/YYYY
            r'\b\d{4}-\d{2}-\d{2}\b',      # YYYY-MM-DD
            r'\b\d{1,2}-\d{1,2}-\d{4}\b'   # MM-DD-YYYY
        ]
        
        for pattern in date_patterns:
            for match in re.finditer(pattern, content):
                entities.append({
                    'text': match.group(),
                    'label': 'DATE',
                    'start': match.start(),
                    'end': match.end(),
                    'pattern_type': 'regex'
                })
        
        # Simple person name detection (capitalized words)
        name_pattern = r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'
        for match in re.finditer(name_pattern, content):
            # Simple heuristic: avoid common false positives
            text = match.group()
            if not any(word.lower() in ['the', 'and', 'for', 'with'] for word in text.split()):
                entities.append({
                    'text': text,
                    'label': 'PERSON',
                    'start': match.start(),
                    'end': match.end(),
                    'pattern_type': 'heuristic'
                })
        
        return entities
    
    async def _extract_domain_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extract domain-specific entities"""
        
        entities = []
        content_lower = content.lower()
        
        for domain, keywords in self.domain_keywords.items():
            for keyword in keywords:
                if keyword.lower() in content_lower:
                    # Find all occurrences
                    start = 0
                    while True:
                        pos = content_lower.find(keyword.lower(), start)
                        if pos == -1:
                            break
                        
                        entities.append({
                            'text': keyword,
                            'label': f'DOMAIN_{domain.upper()}',
                            'start': pos,
                            'end': pos + len(keyword),
                            'pattern_type': 'domain_keyword',
                            'domain': domain
                        })
                        
                        start = pos + 1
        
        return entities
    
    async def _extract_privacy_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extract privacy-sensitive entities"""
        
        entities = []
        
        # SSN pattern
        ssn_pattern = r'\b\d{3}-\d{2}-\d{4}\b'
        for match in re.finditer(ssn_pattern, content):
            entities.append({
                'text': match.group(),
                'label': 'SSN',
                'start': match.start(),
                'end': match.end(),
                'pattern_type': 'privacy',
                'privacy_level': 'high'
            })
        
        # Credit card pattern (simple)
        cc_pattern = r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'
        for match in re.finditer(cc_pattern, content):
            entities.append({
                'text': match.group(),
                'label': 'CREDIT_CARD',
                'start': match.start(),
                'end': match.end(),
                'pattern_type': 'privacy',
                'privacy_level': 'high'
            })
        
        # Password-like patterns
        password_indicators = ['password', 'passwd', 'pwd', 'secret', 'key']
        for indicator in password_indicators:
            pattern = rf'{indicator}[:\s=]+\S+'
            for match in re.finditer(pattern, content, re.IGNORECASE):
                entities.append({
                    'text': match.group(),
                    'label': 'PASSWORD',
                    'start': match.start(),
                    'end': match.end(),
                    'pattern_type': 'privacy',
                    'privacy_level': 'high'
                })
        
        return entities
    
    def _calculate_entity_confidence(self, entity: Dict[str, Any]) -> float:
        """Calculate confidence score for an entity"""
        
        base_confidence = 0.5
        
        # Boost confidence based on pattern type
        pattern_type = entity.get('pattern_type', 'unknown')
        if pattern_type == 'regex':
            base_confidence = 0.9
        elif pattern_type == 'domain_keyword':
            base_confidence = 0.7
        elif pattern_type == 'privacy':
            base_confidence = 0.95
        elif pattern_type == 'heuristic':
            base_confidence = 0.6
        
        # Adjust based on entity length
        text_length = len(entity.get('text', ''))
        if text_length > 20:
            base_confidence *= 0.9
        elif text_length < 3:
            base_confidence *= 0.8
        
        # Adjust based on context
        if entity.get('privacy_level') == 'high':
            base_confidence = min(1.0, base_confidence + 0.1)
        
        return min(1.0, base_confidence)
    
    async def _calculate_classification_metrics(
        self,
        content: str,
        entities: List[Dict[str, Any]],
        metadata: Dict[str, Any],
        context: Optional[Dict[str, Any]]
    ) -> ClassificationMetrics:
        """Calculate comprehensive classification metrics"""
        
        # Initialize metrics
        metrics = ClassificationMetrics()
        
        # Basic content metrics
        metrics.content_length = len(content)
        metrics.entity_types = list(set(entity.get('label', '') for entity in entities))
        
        # Calculate entity confidence
        metrics.entity_confidence = self._calculate_entity_confidence_aggregate(entities)
        
        # Calculate semantic overlap with existing schemas
        metrics.semantic_overlap = await self.calculate_semantic_overlap(entities, await self._get_existing_schemas())
        
        # Calculate domain specificity
        metrics.domain_specificity, domain = self._calculate_domain_specificity(content, entities)
        
        # Calculate privacy metrics
        metrics.privacy_score, metrics.privacy_flags = self._detect_privacy_flags(content, entities)
        
        # Calculate relationship density
        metrics.relationship_density = await self._calculate_relationship_density(entities, content)
        
        # Calculate interconnectedness
        metrics.interconnectedness = self._calculate_interconnectedness(entities)
        
        # Calculate temporal relevance
        timestamp = metadata.get('timestamp') or context.get('timestamp') if context else None
        metrics.temporal_relevance = self._calculate_temporal_relevance(timestamp)
        
        # Count related entities
        metrics.related_entities_count = len(entities)
        
        # Check user preferences
        metrics.user_specified_isolation = metadata.get('force_isolation', False)
        metrics.user_domain_preference = metadata.get('preferred_domain')
        
        return metrics
    
    def _calculate_entity_confidence_aggregate(self, entities: List[Dict[str, Any]]) -> float:
        """Calculate aggregate entity confidence"""
        if not entities:
            return 0.0
        
        confidences = [entity.get('confidence_score', 0.5) for entity in entities]
        return sum(confidences) / len(confidences)
    
    async def calculate_semantic_overlap(
        self, 
        entities: List[Dict[str, Any]], 
        existing_schemas: List[Dict[str, Any]]
    ) -> float:
        """Calculate semantic overlap with existing schemas"""
        
        if not entities or not existing_schemas:
            return 0.0
        
        # Extract entity texts
        entity_texts = [entity.get('text', '').lower() for entity in entities]
        
        max_overlap = 0.0
        
        for schema in existing_schemas:
            schema_entities = schema.get('entities', [])
            if not schema_entities:
                continue
            
            # Calculate Jaccard similarity
            entity_set = set(entity_texts)
            schema_set = set(ent.lower() for ent in schema_entities)
            
            intersection = entity_set.intersection(schema_set)
            union = entity_set.union(schema_set)
            
            if union:
                overlap = len(intersection) / len(union)
                max_overlap = max(max_overlap, overlap)
        
        return max_overlap
    
    def _calculate_domain_specificity(
        self, 
        content: str, 
        entities: List[Dict[str, Any]]
    ) -> Tuple[float, str]:
        """Calculate domain specificity and identify primary domain"""
        
        domain_scores = defaultdict(float)
        content_lower = content.lower()
        
        # Score based on domain keywords in content
        for domain, keywords in self.domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword.lower() in content_lower)
            if score > 0:
                domain_scores[domain] = score / len(keywords)
        
        # Score based on domain entities
        for entity in entities:
            if entity.get('label', '').startswith('DOMAIN_'):
                domain = entity.get('domain', '').lower()
                if domain:
                    domain_scores[domain] += 0.1
        
        if not domain_scores:
            return 0.0, 'general'
        
        # Find primary domain
        primary_domain = max(domain_scores, key=domain_scores.get)
        max_score = domain_scores[primary_domain]
        
        # Normalize score
        specificity = min(1.0, max_score)
        
        return specificity, primary_domain
    
    def _detect_privacy_flags(
        self, 
        content: str, 
        entities: List[Dict[str, Any]]
    ) -> Tuple[int, bool]:
        """Detect privacy-sensitive content"""
        
        privacy_score = 0
        
        # Check for privacy entities
        privacy_entities = [e for e in entities if e.get('privacy_level') == 'high']
        privacy_score += len(privacy_entities) * 2
        
        # Check for privacy keywords
        privacy_keywords = [
            'confidential', 'private', 'secret', 'classified', 'restricted',
            'personal', 'sensitive', 'proprietary', 'internal', 'password'
        ]
        
        content_lower = content.lower()
        for keyword in privacy_keywords:
            if keyword in content_lower:
                privacy_score += 1
        
        # Check for PII patterns
        pii_patterns = [
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
            r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card
        ]
        
        for pattern in pii_patterns:
            if re.search(pattern, content):
                privacy_score += 3
        
        privacy_flags = privacy_score >= self.config.thresholds.privacy_score_threshold
        
        return privacy_score, privacy_flags
    
    async def _calculate_relationship_density(
        self, 
        entities: List[Dict[str, Any]], 
        content: str
    ) -> float:
        """Calculate relationship density between entities"""
        
        if len(entities) < 2:
            return 0.0
        
        # Analyze relationships using the relationship analyzer
        relationships = await self.relationship_analyzer.analyze_entity_relationships(entities, content)
        
        # Calculate density as ratio of actual relationships to possible relationships
        max_relationships = len(entities) * (len(entities) - 1) / 2
        actual_relationships = len(relationships)
        
        if max_relationships == 0:
            return 0.0
        
        density = actual_relationships / max_relationships
        return min(1.0, density)
    
    def _calculate_interconnectedness(self, entities: List[Dict[str, Any]]) -> float:
        """Calculate interconnectedness score"""
        
        if not entities:
            return 0.0
        
        # Simple interconnectedness based on entity diversity and co-occurrence
        entity_types = set(entity.get('label', '') for entity in entities)
        type_diversity = len(entity_types) / max(1, len(entities))
        
        # Check for entities that commonly appear together
        domain_entities = [e for e in entities if e.get('label', '').startswith('DOMAIN_')]
        if len(domain_entities) > 1:
            # Multiple domain entities suggest interconnectedness
            type_diversity += 0.2
        
        return min(1.0, type_diversity)
    
    def _calculate_temporal_relevance(self, timestamp: Optional[str]) -> float:
        """Calculate temporal relevance score"""
        
        if not timestamp:
            return 1.0  # No timestamp means current relevance
        
        try:
            if isinstance(timestamp, str):
                ts = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                ts = timestamp
            
            # Calculate age in days
            age_days = (datetime.now() - ts.replace(tzinfo=None)).days
            
            # Relevance decreases over time
            if age_days <= 1:
                return 1.0
            elif age_days <= 7:
                return 0.9
            elif age_days <= 30:
                return 0.7
            elif age_days <= 90:
                return 0.5
            else:
                return 0.3
                
        except Exception:
            return 1.0  # Default to current relevance on error
```

**Validation:**
```python
# Test intelligent classification engine
from python.helpers.intelligent_classification_engine import IntelligentClassificationEngine
from python.helpers.classification_config import classification_config

engine = IntelligentClassificationEngine(classification_config)
await engine.initialize()

# Test entity extraction
test_content = "John Smith works at Microsoft Corporation. His <NAME_EMAIL>"
entities = await engine.extract_entities(test_content)
print(f"Extracted {len(entities)} entities")

# Test classification
result = await engine.classify_content(test_content, {'content_type': 'text'})
print(f"Classification: {result.strategy.value} with confidence {result.confidence}")
print(f"Reasoning: {result.reasoning}")
```

---

**Next Step**: [Entity Analysis](entity-analysis.md)
