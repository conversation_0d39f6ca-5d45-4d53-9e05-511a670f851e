import asyncio
import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime, timezone, timedelta

# Adjust imports based on actual file structure
try:
    from python.helpers.behavioral_analyzer import (
        BehavioralPatternAnalyzer,
        BehaviorPattern,
        UsageMetrics
    )
    # If UserFeedback, LearningSignal are used by analyzer's public interface or for test data setup:
    from python.helpers.user_preferences import LearningSignal
except ImportError:
    print("WARN: test_behavioral_analyzer.py: Could not import. Using placeholders.")
    from enum import Enum
    from dataclasses import dataclass, field
    class LearningSignal(Enum): POSITIVE_FEEDBACK="positive"; NEGATIVE_FEEDBACK="negative"; IMPLICIT_USAGE="implicit" # Match analyzer's usage
    @dataclass
    class BehaviorPattern: pattern_type: str; pattern_data: dict; confidence: float; frequency: int; first_observed: datetime; last_observed: datetime; impact_score: float
    @dataclass
    class UsageMetrics: total_interactions: int = 0; avg_session_length_minutes: float = 0.0; preferred_domains: list = field(default_factory=list); search_patterns: dict = field(default_factory=dict); feedback_ratio: float = 0.0; error_rate: float = 0.0
    class BehavioralPatternAnalyzer:
        def __init__(self, user_id): self.user_id=user_id; self.interaction_history=[]; self.detected_patterns=[]; self.usage_metrics=UsageMetrics(); self.min_pattern_frequency=3; self.pattern_confidence_threshold=0.6; self.analysis_window_days=30
        async def record_interaction(self, type, context, timestamp=None): self.interaction_history.append({'type':type, 'context':context, 'timestamp':timestamp or datetime.now(timezone.utc)})
        async def analyze_patterns(self): return [] # Simplified for placeholder
        async def _update_usage_metrics(self): pass # Simplified
        async def get_pattern_recommendations(self): return [] # Simplified
        async def export_analysis_report(self): return {'user_id': self.user_id, 'usage_metrics': {}, 'detected_patterns': [], 'recommendations': []} # Simplified


class TestBehavioralPatternAnalyzer(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.analyzer = BehavioralPatternAnalyzer("test_user_analyzer")
        self.base_time = datetime.now(timezone.utc)

    async def test_initialization(self):
        self.assertEqual(self.analyzer.user_id, "test_user_analyzer")
        self.assertEqual(len(self.analyzer.interaction_history), 0)
        self.assertEqual(len(self.analyzer.detected_patterns), 0)
        self.assertIsInstance(self.analyzer.usage_metrics, UsageMetrics)

    async def test_record_interaction(self):
        await self.analyzer.record_interaction("search", {"query": "test"}, self.base_time)
        self.assertEqual(len(self.analyzer.interaction_history), 1)
        interaction = self.analyzer.interaction_history[0]
        self.assertEqual(interaction['type'], "search")
        self.assertEqual(interaction['context']['query'], "test")
        self.assertEqual(interaction['timestamp'], self.base_time)

    async def test_interaction_history_management_window(self):
        self.analyzer.analysis_window_days = 1 # Short window for test
        await self.analyzer.record_interaction("type1", {}, self.base_time - timedelta(days=2)) # Outside window
        await self.analyzer.record_interaction("type2", {}, self.base_time - timedelta(hours=1))  # Inside window
        # record_interaction itself now prunes history
        self.assertEqual(len(self.analyzer.interaction_history), 1)
        self.assertEqual(self.analyzer.interaction_history[0]['type'], "type2")

    async def test_analyze_patterns_empty_history_or_insufficient_data(self):
        patterns = await self.analyzer.analyze_patterns()
        self.assertEqual(len(patterns), 0) # Needs min 10 interactions

        for i in range(5): # Add 5, still less than 10
            await self.analyzer.record_interaction("generic", {"session_id":f"s{i}"}, self.base_time - timedelta(minutes=i))
        patterns = await self.analyzer.analyze_patterns()
        self.assertEqual(len(patterns), 0)


    async def test_detect_domain_patterns_strong_preference(self):
        # Helper to quickly add domain interactions
        async def add_domain_interaction(domain, success, confidence, days_ago=0, hours_ago=0):
            await self.analyzer.record_interaction(
                "classification",
                {"domain": domain, "success": success, "confidence": confidence, "session_id":f"s_{domain}_{days_ago}_{hours_ago}"},
                self.base_time - timedelta(days=days_ago, hours=hours_ago)
            )

        # domainA: 3 successful interactions, high confidence
        await add_domain_interaction("domainA", True, 0.9, 1)
        await add_domain_interaction("domainA", True, 0.85, 1, 1)
        await add_domain_interaction("domainA", True, 0.95, 0, 1)
        # domainB: 2 interactions (below min_pattern_frequency)
        await add_domain_interaction("domainB", True, 0.7, 0)
        await add_domain_interaction("domainB", False, 0.3, 0)
        # Add some noise to meet >10 interaction for analyze_patterns to run fully
        for i in range(5): await add_domain_interaction(f"noise{i}", True,0.5)


        patterns = await self.analyzer.analyze_patterns()
        domain_a_pattern = next((p for p in patterns if p.pattern_type == 'domain_preference' and p.pattern_data['domain'] == 'domainA'), None)

        self.assertIsNotNone(domain_a_pattern, "domainA preference pattern not detected.")
        if domain_a_pattern:
            self.assertEqual(domain_a_pattern.frequency, 3)
            self.assertAlmostEqual(domain_a_pattern.pattern_data['success_rate'], 1.0)
            self.assertAlmostEqual(domain_a_pattern.pattern_data['avg_confidence'], 0.9) # (0.9+0.85+0.95)/3
            self.assertGreaterEqual(domain_a_pattern.confidence, self.analyzer.pattern_confidence_threshold)

        domain_b_pattern = next((p for p in patterns if p.pattern_type == 'domain_preference' and p.pattern_data['domain'] == 'domainB'), None)
        self.assertIsNone(domain_b_pattern, "domainB should not form a pattern due to low frequency.")


    async def test_detect_search_patterns_low_result_usage(self):
        # Helper for search interactions
        async def add_search(results_used, result_limit, days_ago=0):
             await self.analyzer.record_interaction(
                "search",
                {"results_used": results_used, "result_limit": result_limit, "session_id":f"s_search_{days_ago}_{results_used}"},
                self.base_time - timedelta(days=days_ago)
            )

        await add_search(2, 10, 2) # 20% usage
        await add_search(3, 10, 1) # 30% usage
        await add_search(1, 5,  0) # 20% usage
        # Add noise for >10 interactions
        for i in range(7): await self.analyzer.record_interaction("noise", {"session_id":f"s_noise_{i}"})


        patterns = await self.analyzer.analyze_patterns()
        search_pref_pattern = next((p for p in patterns if p.pattern_type == 'search_result_preference'), None)

        self.assertIsNotNone(search_pref_pattern, "Search result preference pattern not detected.")
        if search_pref_pattern:
            self.assertEqual(search_pref_pattern.frequency, 3) # 3 search interactions
            self.assertLess(search_pref_pattern.pattern_data['efficiency_ratio'], 0.6) # avg_used < avg_limit * 0.6

    async def test_detect_feedback_patterns_frequent_negative(self):
        async def add_feedback(fb_type, days_ago=0):
            await self.analyzer.record_interaction(
                "feedback",
                {"feedback_type": fb_type.value, "item_id": f"item_{days_ago}", "session_id":f"s_fb_{days_ago}"},
                self.base_time - timedelta(days=days_ago)
            )

        await add_feedback(LearningSignal.NEGATIVE_FEEDBACK, 2)
        await add_feedback(LearningSignal.NEGATIVE_FEEDBACK, 1)
        await add_feedback(LearningSignal.NEGATIVE_FEEDBACK, 0)
        await add_feedback(LearningSignal.POSITIVE_FEEDBACK, 0) # One positive
        for i in range(6): await self.analyzer.record_interaction("noise", {"session_id":f"s_noise_fb_{i}"})


        patterns = await self.analyzer.analyze_patterns()
        neg_fb_pattern = next((p for p in patterns if p.pattern_type == 'feedback_behavior' and p.pattern_data['feedback_type'] == LearningSignal.NEGATIVE_FEEDBACK.value), None)

        self.assertIsNotNone(neg_fb_pattern, "Negative feedback pattern not detected.")
        if neg_fb_pattern:
            self.assertEqual(neg_fb_pattern.frequency, 3)
            self.assertAlmostEqual(neg_fb_pattern.pattern_data['percentage'], 0.75) # 3 out of 4 feedback items

    async def test_update_usage_metrics(self):
        # Record some interactions
        await self.analyzer.record_interaction("search", {"domain": "tech", "session_id":"s1"}, self.base_time - timedelta(minutes=30))
        await self.analyzer.record_interaction("classification", {"domain": "news", "success":True, "session_id":"s1"}, self.base_time - timedelta(minutes=20))
        await self.analyzer.record_interaction("feedback", {"feedback_type": LearningSignal.POSITIVE_FEEDBACK.value, "session_id":"s1"}, self.base_time - timedelta(minutes=10)) # End s1
        await self.analyzer.record_interaction("search", {"domain": "tech", "error":True, "session_id":"s2"}, self.base_time - timedelta(minutes=5)) # Start s2

        # _update_usage_metrics is called by record_interaction, so check results
        metrics = self.analyzer.usage_metrics
        self.assertEqual(metrics.total_interactions, 4)
        self.assertIn("tech", metrics.preferred_domains)
        self.assertIn("news", metrics.preferred_domains)
        self.assertAlmostEqual(metrics.feedback_ratio, 1/4)
        self.assertAlmostEqual(metrics.error_rate, 1/4)
        self.assertGreater(metrics.avg_session_length_minutes, 0) # s1 had duration

    async def test_get_pattern_recommendations(self):
        # Manually create and set a pattern that should trigger a recommendation
        strong_domain_pref_pattern = BehaviorPattern(
            pattern_type='domain_preference',
            pattern_data={'domain': 'science', 'success_rate': 0.9, 'interaction_count': 10},
            confidence=0.85, frequency=10,
            first_observed=self.base_time - timedelta(days=5),
            last_observed=self.base_time,
            impact_score=0.5
        )
        self.analyzer.detected_patterns = [strong_domain_pref_pattern] # Override detected patterns

        recommendations = await self.analyzer.get_pattern_recommendations()
        self.assertGreaterEqual(len(recommendations), 1)
        rec = recommendations[0]
        self.assertEqual(rec['type'], 'increase_domain_weight')
        self.assertEqual(rec['domain'], 'science')

    async def test_export_analysis_report(self):
        await self.analyzer.record_interaction("test_export", {"data":"content"}, self.base_time)
        # Manually add a pattern for the report
        test_pattern = BehaviorPattern("test_pattern", {}, 0.7, 5, self.base_time, self.base_time, 0.1)
        self.analyzer.detected_patterns = [test_pattern]

        report = await self.analyzer.export_analysis_report()

        self.assertEqual(report['user_id'], self.analyzer.user_id)
        self.assertIn('analysis_timestamp', report)
        self.assertEqual(report['usage_metrics']['total_interactions'], 1)
        self.assertEqual(len(report['detected_patterns']), 1)
        self.assertEqual(report['detected_patterns'][0]['type'], "test_pattern")
        self.assertIsInstance(report['recommendations'], list)


if __name__ == '__main__':
    unittest.main()
