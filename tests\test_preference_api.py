import asyncio
import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch, call
from datetime import datetime
from typing import Dict, Any, List

# Attempt to import the actual PreferenceAPI and its dependencies
# If they are not available (e.g., path issues or still using placeholders in preference_api.py),
# this test file might need adjustments or might not run correctly until paths are resolved.
try:
    from python.helpers.preference_api import PreferenceAPI, preference_api
    # Import Enums and data classes that might be used for type checking or instance creation in tests
    from python.helpers.user_preferences import PreferenceType, LearningSignal, UserFeedback, PreferenceItem
    from python.helpers.behavioral_analyzer import BehaviorPattern, UsageMetrics
    from python.helpers.preference_adaptation import PreferenceAdaptationEngine
except ImportError as e:
    print(f"test_preference_api.py: Error importing modules: {e}. Tests may fail or not run.")
    # Define minimal placeholders if imports fail, allowing syntax to be valid
    # This is a fallback and assumes preference_api.py itself has robust placeholders.
    class PreferenceAPI: pass
    preference_api = PreferenceAPI() # Dummy instance
    class PreferenceType(MagicMock): pass # Mocking enums
    class LearningSignal(MagicMock): pass
    class UserFeedback(MagicMock): pass
    class PreferenceItem(MagicMock): pass
    class BehaviorPattern(MagicMock): pass
    class UsageMetrics(MagicMock): pass
    class PreferenceAdaptationEngine(MagicMock): pass


# Helper to run async tests
def async_test(coro):
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

class TestPreferenceAPI(unittest.TestCase):

    def setUp(self):
        # It's better to test a fresh instance of PreferenceAPI in each test
        # rather than the global `preference_api` instance, to avoid state leakage.
        self.api = PreferenceAPI()

        # Resetting the global instance's internal state if it's used by mistake or for specific tests.
        # This is generally not ideal, prefer using self.api.
        if hasattr(preference_api, 'preference_managers'):
            preference_api.preference_managers = {}
        if hasattr(preference_api, 'adaptation_engines'):
            preference_api.adaptation_engines = {}

        # Common test data
        self.test_user_id = "test_user_unittest"
        self.mock_manager = AsyncMock()
        self.mock_analyzer = AsyncMock()
        self.mock_engine = AsyncMock()

        # Patch the helper methods that create/get managers and engines
        # These patches apply to the self.api instance for the duration of a test method
        self.patch_get_manager = patch.object(self.api, '_get_preference_manager', return_value=self.mock_manager)
        self.patch_get_engine = patch.object(self.api, '_get_adaptation_engine', return_value=self.mock_engine)

        # Start the patchers
        self.mock_get_manager = self.patch_get_manager.start()
        self.mock_get_engine = self.patch_get_engine.start()

        # BehavioralPatternAnalyzer is often instantiated directly in API methods,
        # so we might need to patch its instantiation if _get_behavioral_analyzer isn't consistently used.
        # For now, assuming _get_adaptation_engine's behavioral_analyzer is the one to mock or
        # that direct instantiation will be patched where needed.
        if hasattr(self.mock_engine, 'behavioral_analyzer'):
            self.mock_engine.behavioral_analyzer = self.mock_analyzer


    def tearDown(self):
        # Stop the patchers
        self.patch_get_manager.stop()
        self.patch_get_engine.stop()
        patch.stopall() # Stops any other patches started with patch class methods

    @async_test
    async def test_get_user_preferences(self):
        now = datetime.now()
        self.mock_manager.preferences = {
            "theme": PreferenceItem(preference_type=PreferenceType.GENERAL, key="theme", value="dark", confidence=1.0, last_updated=now, learning_history=[]),
            "results_per_page": PreferenceItem(preference_type=PreferenceType.SEARCH_PREFERENCE, key="results_per_page", value=10, confidence=0.8, last_updated=now, user_explicit=True, learning_history=[1,2])
        }
        # Ensure enum values are correctly accessed if they are actual enums
        expected_theme_type_val = PreferenceType.GENERAL.value if hasattr(PreferenceType.GENERAL, 'value') else str(PreferenceType.GENERAL)
        expected_search_type_val = PreferenceType.SEARCH_PREFERENCE.value if hasattr(PreferenceType.SEARCH_PREFERENCE, 'value') else str(PreferenceType.SEARCH_PREFERENCE)


        prefs = await self.api.get_user_preferences(self.test_user_id)

        self.mock_get_manager.assert_called_once_with(self.test_user_id)
        self.assertIn("theme", prefs)
        self.assertEqual(prefs["theme"]["value"], "dark")
        self.assertEqual(prefs["theme"]["type"], expected_theme_type_val)
        self.assertEqual(prefs["theme"]["learning_history_count"], 0)
        self.assertIn("results_per_page", prefs)
        self.assertEqual(prefs["results_per_page"]["value"], 10)
        self.assertEqual(prefs["results_per_page"]["type"], expected_search_type_val)
        self.assertEqual(prefs["results_per_page"]["confidence"], 0.8)
        self.assertTrue(prefs["results_per_page"]["user_explicit"])
        self.assertEqual(prefs["results_per_page"]["learning_history_count"], 2)


    @async_test
    async def test_set_user_preference_success(self):
        self.mock_manager.set_preference = AsyncMock(return_value=True)
        pref_key = "notifications_enabled"
        pref_value = True
        pref_type_str = "GENERAL" # Test with string value for type

        # Mock PreferenceType.GENERAL.value behavior for the assertion
        if hasattr(PreferenceType, pref_type_str):
            enum_member = getattr(PreferenceType, pref_type_str)
            pref_type_enum = enum_member
        else: # Fallback for placeholder
            pref_type_enum = PreferenceType(pref_type_str)


        result = await self.api.set_user_preference(self.test_user_id, pref_key, pref_value, pref_type_str)

        self.mock_get_manager.assert_called_once_with(self.test_user_id)
        self.mock_manager.set_preference.assert_called_once_with(
            pref_key, pref_value, pref_type_enum, user_explicit=True
        )
        self.assertTrue(result["success"])
        self.assertEqual(result["preference_key"], pref_key)
        self.assertEqual(result["new_value"], pref_value)

    @async_test
    async def test_set_user_preference_invalid_type_string(self):
        result = await self.api.set_user_preference(self.test_user_id, "some_key", "some_value", "INVALID_TYPE_STRING")
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        self.assertIn("Invalid preference type string: INVALID_TYPE_STRING", result["error"])

    @patch('python.helpers.preference_api.BehavioralPatternAnalyzer') # Patch direct instantiation
    @async_test
    async def test_get_user_behavioral_insights(self, MockBehavioralAnalyzer):
        mocked_analyzer_instance = AsyncMock()
        mocked_analyzer_instance.export_analysis_report = AsyncMock(return_value={
            "user_id": self.test_user_id, "usage_metrics": {"total_interactions": 5}, "detected_patterns": []
        })
        MockBehavioralAnalyzer.return_value = mocked_analyzer_instance

        self.mock_manager.preferences = {"pref1": MagicMock()}
        self.mock_manager.feedback_history = [MagicMock(), MagicMock()]

        insights = await self.api.get_user_behavioral_insights(self.test_user_id)

        self.mock_get_manager.assert_called_once_with(self.test_user_id)
        MockBehavioralAnalyzer.assert_called_once_with(self.test_user_id)
        mocked_analyzer_instance.export_analysis_report.assert_called_once()

        self.assertEqual(insights["user_id"], self.test_user_id)
        self.assertIn("insights", insights)
        self.assertEqual(insights["insights"]["usage_metrics"]["total_interactions"], 5)
        self.assertEqual(insights["preference_count"], 1)
        self.assertEqual(insights["feedback_history_count"], 2)


    @async_test
    async def test_trigger_preference_adaptation(self):
        expected_session_data = {"patterns_analyzed": 5, "adaptations_applied": 2}
        self.mock_engine.analyze_and_adapt = AsyncMock(return_value=expected_session_data)

        result = await self.api.trigger_preference_adaptation(self.test_user_id)

        self.mock_get_engine.assert_called_once_with(self.test_user_id)
        self.mock_engine.analyze_and_adapt.assert_called_once()
        self.assertEqual(result["user_id"], self.test_user_id)
        self.assertEqual(result["adaptation_session"], expected_session_data)

    @async_test
    async def test_record_user_feedback_success(self):
        self.mock_manager.record_feedback = AsyncMock()
        action = "item_click"
        context = {"item_id": "doc123"}
        feedback_type_str = "implicit" # Changed to lowercase to match enum value

        # Correctly get the enum member for assertion
        signal_enum = LearningSignal(feedback_type_str)


        result = await self.api.record_user_feedback(self.test_user_id, action, context, feedback_type_str.upper(), value=None) # API receives "IMPLICIT"

        self.mock_get_manager.assert_called_once_with(self.test_user_id)
        # The API will convert "IMPLICIT" to LearningSignal.IMPLICIT_USAGE before calling manager's method
        self.mock_manager.record_feedback.assert_called_once_with(action, context, LearningSignal.IMPLICIT_USAGE, None)
        self.assertTrue(result["success"])
        self.assertEqual(result["action"], action)

    @async_test
    async def test_record_user_feedback_invalid_type_string(self):
        result = await self.api.record_user_feedback(self.test_user_id, "action", {}, "INVALID_SIGNAL_STRING")
        self.assertFalse(result["success"])
        self.assertIn("error", result)
        self.assertIn("Invalid feedback type string: INVALID_SIGNAL_STRING", result["error"])


    @async_test
    async def test_get_preference_recommendations(self):
        expected_recs = [{"type": "rec1", "confidence": 0.9}]
        # Ensure the mock_analyzer is correctly associated with the mock_engine
        # If mock_engine.behavioral_analyzer isn't set up in setUp, this needs careful handling.
        # Assuming setUp correctly mocks mock_engine.behavioral_analyzer.
        if not hasattr(self.mock_engine, 'behavioral_analyzer') or not isinstance(self.mock_engine.behavioral_analyzer, AsyncMock):
             # If the analyzer is not part of the engine mock, we might need to patch it differently,
             # or ensure the PreferenceAPI._get_adaptation_engine correctly returns an engine with a mocked analyzer.
             # This setup is tricky if the structure isn't as expected.
             # For this test, let's assume self.mock_analyzer is what's used.
             self.mock_engine.behavioral_analyzer = self.mock_analyzer


        self.mock_engine.behavioral_analyzer.get_pattern_recommendations = AsyncMock(return_value=expected_recs)


        result = await self.api.get_preference_recommendations(self.test_user_id)

        self.mock_get_engine.assert_called_once_with(self.test_user_id)
        self.mock_engine.behavioral_analyzer.get_pattern_recommendations.assert_called_once()
        self.assertEqual(result["user_id"], self.test_user_id)
        self.assertEqual(result["recommendations"], expected_recs)
        self.assertEqual(result["recommendation_count"], len(expected_recs))


    @patch('python.helpers.preference_api.BehavioralPatternAnalyzer') # Patch direct instantiation
    @async_test
    async def test_export_user_data(self, MockBehavioralAnalyzer):
        now = datetime.now()
        # Mock for get_user_preferences part
        self.mock_manager.preferences = {
            "export_pref": PreferenceItem(preference_type=PreferenceType.GENERAL, key="export_pref", value="export_val", confidence=1.0, last_updated=now, learning_history=[])
        }
        # Mock for get_user_behavioral_insights part
        mocked_analyzer_instance = AsyncMock()
        mocked_analyzer_instance.export_analysis_report = AsyncMock(return_value={
            "user_id": self.test_user_id, "usage_metrics": {"total_interactions": 20}, "detected_patterns": ["pattern_export"]
        })
        MockBehavioralAnalyzer.return_value = mocked_analyzer_instance

        # Mock for feedback_history part
        # For UserFeedback, ensure its constructor matches its definition if it's a real class
        # Assuming UserFeedback(action, context, feedback_type_enum, timestamp, value)
        self.mock_manager.feedback_history = [
            UserFeedback(action="action_export", context={"ctx": "export"}, feedback_type=LearningSignal.POSITIVE_FEEDBACK, timestamp=now, value=None)
        ]

        # Ensure enum values are correctly accessed
        expected_pref_type_val = PreferenceType.GENERAL.value if hasattr(PreferenceType.GENERAL, 'value') else str(PreferenceType.GENERAL)
        expected_signal_val = LearningSignal.POSITIVE_FEEDBACK.value if hasattr(LearningSignal.POSITIVE_FEEDBACK, 'value') else str(LearningSignal.POSITIVE_FEEDBACK)


        export_data = await self.api.export_user_data(self.test_user_id)

        # get_user_preferences is called twice by export_user_data (once directly, once via get_behavioral_insights indirectly if manager is involved)
        # self.mock_get_manager.assert_any_call(self.test_user_id) - this is complex due to multiple calls
        self.assertEqual(export_data["user_id"], self.test_user_id)
        self.assertIn("preferences", export_data)
        self.assertIn("export_pref", export_data["preferences"])
        self.assertEqual(export_data["preferences"]["export_pref"]["value"], "export_val")

        self.assertIn("behavioral_insights", export_data)
        self.assertEqual(export_data["behavioral_insights"]["insights"]["usage_metrics"]["total_interactions"], 20)

        self.assertIn("feedback_history", export_data)
        self.assertEqual(len(export_data["feedback_history"]), 1)
        self.assertEqual(export_data["feedback_history"][0]["action"], "action_export")
        self.assertEqual(export_data["feedback_history"][0]["feedback_type"], expected_signal_val)


    @async_test
    async def test_reset_user_preferences_keep_explicit(self):
        now = datetime.now()
        self.mock_manager.preferences = {
            "explicit_pref": PreferenceItem(preference_type=PreferenceType.GENERAL, key="explicit_pref", value="explicit_val", confidence=1.0, user_explicit=True, last_updated=now, learning_history=[]),
            "implicit_pref": PreferenceItem(preference_type=PreferenceType.DOMAIN_WEIGHT, key="implicit_pref", value="implicit_val", confidence=0.5, user_explicit=False, last_updated=now, learning_history=[])
        }
        # _initialize_default_preferences will be called on mock_manager
        # We need to ensure it results in a known state for manager.preferences for the test
        def mock_init_defaults():
            # Simulate clearing and setting new defaults
            self.mock_manager.preferences = {"default_after_reset": PreferenceItem(preference_type=PreferenceType.GENERAL, key="default_after_reset", value="default_val", confidence=1.0, last_updated=datetime.now(), learning_history=[])}
        self.mock_manager._initialize_default_preferences = MagicMock(side_effect=mock_init_defaults)


        result = await self.api.reset_user_preferences(self.test_user_id, keep_explicit=True)

        self.mock_get_manager.assert_called_once_with(self.test_user_id)
        self.mock_manager._initialize_default_preferences.assert_called_once()
        self.assertTrue(result["success"])
        self.assertEqual(result["explicit_preferences_kept"], 1)
        # After reset and restore, "explicit_pref" and "default_after_reset" should be there
        self.assertIn("explicit_pref", self.mock_manager.preferences)
        self.assertEqual(self.mock_manager.preferences["explicit_pref"].value, "explicit_val")
        self.assertIn("default_after_reset", self.mock_manager.preferences) # Check default is also there
        self.assertEqual(len(self.mock_manager.preferences), 2)


    @async_test
    async def test_reset_user_preferences_discard_explicit(self):
        now = datetime.now()
        self.mock_manager.preferences = {
            "explicit_pref": PreferenceItem(preference_type=PreferenceType.GENERAL, key="explicit_pref", value="explicit_val", confidence=1.0, user_explicit=True, last_updated=now, learning_history=[]),
        }
        def mock_init_defaults_discard():
            self.mock_manager.preferences = {"default_for_discard": PreferenceItem(preference_type=PreferenceType.GENERAL, key="default_for_discard", value="default_val", confidence=1.0, last_updated=datetime.now(), learning_history=[])}
        self.mock_manager._initialize_default_preferences = MagicMock(side_effect=mock_init_defaults_discard)

        result = await self.api.reset_user_preferences(self.test_user_id, keep_explicit=False)

        self.mock_get_manager.assert_called_once_with(self.test_user_id)
        self.mock_manager._initialize_default_preferences.assert_called_once()
        self.assertTrue(result["success"])
        self.assertEqual(result["explicit_preferences_kept"], 0)
        self.assertNotIn("explicit_pref", self.mock_manager.preferences)
        self.assertIn("default_for_discard", self.mock_manager.preferences) # Check only default is there
        self.assertEqual(len(self.mock_manager.preferences), 1)


    @patch('python.helpers.preference_api.UserPreferenceManager') # Patch UserPreferenceManager instantiation
    @async_test
    async def test_get_preference_manager_creation_and_caching(self, MockUserPreferenceManager):
        # Stop the setUp patch for _get_preference_manager for this specific test
        self.patch_get_manager.stop()

        mock_manager_instance = AsyncMock()
        MockUserPreferenceManager.return_value = mock_manager_instance

        # First call, should create and cache
        manager1 = await self.api._get_preference_manager("new_user_1")
        self.assertEqual(manager1, mock_manager_instance)
        MockUserPreferenceManager.assert_called_once_with("new_user_1")
        self.assertIn("new_user_1", self.api.preference_managers)
        self.assertEqual(self.api.preference_managers["new_user_1"], mock_manager_instance)

        # Second call for same user, should return cached
        MockUserPreferenceManager.reset_mock() # Reset call count for the class mock
        manager2 = await self.api._get_preference_manager("new_user_1")
        self.assertEqual(manager2, mock_manager_instance)
        MockUserPreferenceManager.assert_not_called() # Should not be called again

        # Call for a different user, should create new one
        mock_manager_instance_2 = AsyncMock()
        MockUserPreferenceManager.return_value = mock_manager_instance_2
        manager3 = await self.api._get_preference_manager("new_user_2")
        self.assertEqual(manager3, mock_manager_instance_2)
        MockUserPreferenceManager.assert_called_once_with("new_user_2")
        self.assertIn("new_user_2", self.api.preference_managers)

        # Restore the patch for other tests
        self.patch_get_manager.start()


    @patch('python.helpers.preference_api.PreferenceAdaptationEngine') # Patch Engine instantiation
    @patch('python.helpers.preference_api.UserPreferenceManager') # Patch Manager instantiation
    @async_test
    async def test_get_adaptation_engine_creation_and_caching(self, MockUserPreferenceManager, MockPreferenceAdaptationEngine):
        # Stop the setUp patch for _get_adaptation_engine and _get_preference_manager for this test
        self.patch_get_engine.stop()
        self.patch_get_manager.stop()


        mock_manager_instance = AsyncMock(user_id="engine_user_1") # Give user_id to mock manager
        MockUserPreferenceManager.return_value = mock_manager_instance

        mock_engine_instance = AsyncMock()
        MockPreferenceAdaptationEngine.return_value = mock_engine_instance

        # First call, should create manager (if not cached) and engine
        engine1 = await self.api._get_adaptation_engine("engine_user_1")
        self.assertEqual(engine1, mock_engine_instance)
        MockUserPreferenceManager.assert_called_once_with("engine_user_1") # Manager created first
        MockPreferenceAdaptationEngine.assert_called_once_with(mock_manager_instance) # Engine created with manager
        self.assertIn("engine_user_1", self.api.adaptation_engines)
        self.assertEqual(self.api.adaptation_engines["engine_user_1"], mock_engine_instance)

        # Second call for same user, should return cached engine (and manager)
        MockUserPreferenceManager.reset_mock()
        MockPreferenceAdaptationEngine.reset_mock()
        engine2 = await self.api._get_adaptation_engine("engine_user_1")
        self.assertEqual(engine2, mock_engine_instance)
        MockUserPreferenceManager.assert_not_called() # Manager should be cached now by self.api.preference_managers
        MockPreferenceAdaptationEngine.assert_not_called() # Engine should be cached

        # Restore patches
        self.patch_get_manager.start()
        self.patch_get_engine.start()


if __name__ == '__main__':
    unittest.main()
