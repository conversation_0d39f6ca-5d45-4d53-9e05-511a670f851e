from typing import Dict, Any, List
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
import asyncio

# Import classification config directly - no fallbacks or mocks
# This enforces proper dependency management and prevents silent failures
from .classification_config import classification_config


class PreferenceType(Enum):
    GENERAL = "general"
    DOMAIN_WEIGHT = "domain_weight"
    PRIVACY_SENSITIVITY = "privacy_sensitivity"
    CLASSIFICATION_THRESHOLD = "classification_threshold"
    MEMORY_RETENTION = "memory_retention"
    SEARCH_PREFERENCE = "search_preference"

class LearningSignal(Enum):
    POSITIVE_FEEDBACK = "positive"
    NEGATIVE_FEEDBACK = "negative"
    IMPLICIT_USAGE = "implicit"
    EXPLICIT_SETTING = "explicit" # Added from spec

@dataclass
class PreferenceItem:
    """Individual preference item with learning history"""
    preference_type: PreferenceType
    key: str
    value: Any
    confidence: float
    last_updated: datetime
    learning_history: List[Dict[str, Any]] = field(default_factory=list)
    user_explicit: bool = False

@dataclass
class UserFeedback:
    """User feedback for preference learning"""
    action: str
    context: Dict[str, Any]
    feedback_type: LearningSignal
    timestamp: datetime
    value: Any # Value associated with the feedback, if any


class UserPreferenceManager:
    """
    Manages user preferences with adaptive learning capabilities
    """

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.preferences: Dict[str, PreferenceItem] = {}
        self.feedback_history: List[UserFeedback] = []
        # Ensure classification_config has these attributes, even if it's a placeholder
        self.learning_rate = getattr(classification_config, 'preference_learning_rate', 0.1)
        self.adaptation_enabled = getattr(classification_config, 'enable_adaptive_learning', True)
        self._initialize_default_preferences()

    def _initialize_default_preferences(self):
        """Initialize default user preferences"""
        # Using timezone.utc for all datetime objects
        now_utc = datetime.now(timezone.utc)
        default_prefs = {
            # Domain weights
            'domain_weight_programming': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'programming', 1.0, 0.5, now_utc
            ),
            'domain_weight_documentation': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'documentation', 0.8, 0.5, now_utc
            ),
            'domain_weight_conversations': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'conversations', 0.6, 0.5, now_utc
            ),
            'domain_weight_general': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'general', 0.4, 0.5, now_utc
            ),

            # Privacy sensitivity
            'privacy_sensitivity_level': PreferenceItem(
                PreferenceType.PRIVACY_SENSITIVITY, 'level', 'medium', 0.7, now_utc
            ),
            'privacy_auto_isolation': PreferenceItem(
                PreferenceType.PRIVACY_SENSITIVITY, 'auto_isolation', True, 0.8, now_utc
            ),

            # Classification thresholds
            'classification_confidence_threshold': PreferenceItem(
                PreferenceType.CLASSIFICATION_THRESHOLD, 'confidence', 0.7, 0.6, now_utc
            ),
            'classification_manual_review_threshold': PreferenceItem(
                PreferenceType.CLASSIFICATION_THRESHOLD, 'manual_review', 0.6, 0.6, now_utc
            ),

            # Memory retention preferences
            'memory_working_retention_days': PreferenceItem(
                PreferenceType.MEMORY_RETENTION, 'working_days', 7, 0.8, now_utc
            ),
            'memory_importance_threshold': PreferenceItem(
                PreferenceType.MEMORY_RETENTION, 'importance_threshold', 0.5, 0.7, now_utc
            ),

            # Search preferences
            'search_strategy_preference': PreferenceItem(
                PreferenceType.SEARCH_PREFERENCE, 'strategy', 'hierarchical', 0.6, now_utc
            ),
            'search_result_limit': PreferenceItem(
                PreferenceType.SEARCH_PREFERENCE, 'result_limit', 10, 0.8, now_utc
            )
        }
        self.preferences.update(default_prefs)

    async def get_preference(self, preference_key: str, default_value: Any = None) -> Any:
        """Get user preference value"""
        if preference_key in self.preferences:
            return self.preferences[preference_key].value
        return default_value

    async def set_preference(
        self,
        preference_key: str,
        value: Any,
        preference_type: PreferenceType,
        user_explicit: bool = True
    ) -> bool:
        """Set user preference explicitly"""
        now_utc = datetime.now(timezone.utc)
        try:
            if preference_key in self.preferences:
                pref = self.preferences[preference_key]
                pref.value = value
                pref.last_updated = now_utc
                pref.user_explicit = user_explicit
                if user_explicit:
                    pref.confidence = 1.0
            else:
                self.preferences[preference_key] = PreferenceItem(
                    preference_type=preference_type,
                    key=preference_key,
                    value=value,
                    confidence=1.0 if user_explicit else 0.5,
                    last_updated=now_utc,
                    user_explicit=user_explicit
                )
            await self._record_preference_change(preference_key, value, user_explicit)
            return True
        except Exception as e:
            print(f"Error setting preference {preference_key}: {e}")
            return False

    async def record_feedback(
        self,
        action: str,
        context: Dict[str, Any],
        feedback_type: LearningSignal,
        value: Any = None # Added default None as per spec step 4.1.3
    ) -> None:
        """Record user feedback for learning"""
        feedback = UserFeedback(
            action=action,
            context=context,
            feedback_type=feedback_type,
            timestamp=datetime.now(timezone.utc),
            value=value
        )
        self.feedback_history.append(feedback)

        if self.adaptation_enabled:
            await self._process_feedback(feedback)

        if len(self.feedback_history) > 1000: # Keep history manageable
            self.feedback_history = self.feedback_history[-1000:]

    async def _process_feedback(self, feedback: UserFeedback) -> None:
        """Process feedback to update preferences"""
        if feedback.feedback_type == LearningSignal.POSITIVE_FEEDBACK:
            await self._reinforce_preferences(feedback)
        elif feedback.feedback_type == LearningSignal.NEGATIVE_FEEDBACK:
            await self._adjust_preferences(feedback)
        elif feedback.feedback_type == LearningSignal.IMPLICIT_USAGE:
            await self._learn_from_usage(feedback)
        # EXPLICIT_SETTING is handled by set_preference directly, not via _process_feedback loop

    async def _reinforce_preferences(self, feedback: UserFeedback) -> None:
        """Reinforce preferences based on positive feedback"""
        context = feedback.context
        if 'classification_result' in context:
            result = context['classification_result']
            # Ensure result is a dict-like structure or an object with attributes
            # The spec implies result['domain'] and result['confidence']
            domain_from_result = result.get('domain') if isinstance(result, dict) else getattr(result, 'domain', None)
            confidence_from_result = result.get('confidence') if isinstance(result, dict) else getattr(result, 'confidence', None)

            if domain_from_result:
                domain_key = f"domain_weight_{domain_from_result}"
                if domain_key in self.preferences:
                    pref = self.preferences[domain_key]
                    new_value = min(1.0, pref.value + self.learning_rate * 0.1)
                    await self._update_preference_value(domain_key, new_value, 'positive_feedback_domain')

            if confidence_from_result is not None:
                threshold_key = 'classification_confidence_threshold'
                if threshold_key in self.preferences:
                    pref = self.preferences[threshold_key]
                    adjustment = (confidence_from_result - pref.value) * self.learning_rate * 0.05
                    new_value = max(0.1, min(0.9, pref.value + adjustment))
                    await self._update_preference_value(threshold_key, new_value, 'positive_feedback_threshold')

    async def _adjust_preferences(self, feedback: UserFeedback) -> None:
        """Adjust preferences based on negative feedback"""
        context = feedback.context
        if 'classification_result' in context:
            result = context['classification_result']
            domain_from_result = result.get('domain') if isinstance(result, dict) else getattr(result, 'domain', None)

            if domain_from_result:
                domain_key = f"domain_weight_{domain_from_result}"
                if domain_key in self.preferences:
                    pref = self.preferences[domain_key]
                    new_value = max(0.1, pref.value - self.learning_rate * 0.1)
                    await self._update_preference_value(domain_key, new_value, 'negative_feedback_domain')

    async def _learn_from_usage(self, feedback: UserFeedback) -> None:
        """Learn from implicit usage patterns"""
        context = feedback.context
        action = feedback.action

        if action == 'search_query':
            result_count = context.get('result_count')
            if result_count is not None:
                limit_key = 'search_result_limit'
                if limit_key in self.preferences:
                    pref = self.preferences[limit_key]
                    if result_count < pref.value * 0.5:
                        new_value = max(5, int(pref.value * 0.9))
                        await self._update_preference_value(limit_key, new_value, 'usage_search_limit')

        elif action == 'memory_access':
            tier = context.get('tier')
            frequency = context.get('access_frequency')
            if tier == 'working' and frequency is not None and frequency > 5:
                retention_key = 'memory_working_retention_days'
                if retention_key in self.preferences:
                    pref = self.preferences[retention_key]
                    new_value = min(14, pref.value + 1)
                    await self._update_preference_value(retention_key, new_value, 'usage_working_retention')

    async def _update_preference_value(
        self,
        preference_key: str,
        new_value: Any,
        reason: str
    ) -> None:
        """Update preference value with learning history"""
        if preference_key in self.preferences:
            pref = self.preferences[preference_key]
            if pref.user_explicit: return # Don't override user-set preferences with learning

            old_value = pref.value
            pref.value = new_value
            pref.last_updated = datetime.now(timezone.utc)
            pref.confidence = min(0.95, pref.confidence + 0.05) # Cap confidence slightly below explicit

            pref.learning_history.append({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'old_value': old_value,
                'new_value': new_value,
                'reason': reason,
                'confidence': pref.confidence
            })
            if len(pref.learning_history) > 50: # Keep history manageable
                pref.learning_history = pref.learning_history[-50:]

    async def _record_preference_change(
        self,
        preference_key: str,
        value: Any,
        user_explicit: bool
    ) -> None:
        """Record preference change in history (called by set_preference)"""
        if preference_key in self.preferences:
            pref = self.preferences[preference_key]
            # Determine old_value safely
            old_value = None
            if pref.learning_history:
                # Check if the last entry has 'new_value'
                last_event = pref.learning_history[-1]
                if isinstance(last_event, dict) and 'new_value' in last_event:
                    old_value = last_event['new_value']
                else: # Fallback if structure is unexpected or it's the first "explicit" set after init
                    old_value = "default" if not pref.learning_history else "unknown"
            else: # if no history, it means it was a default value
                old_value = "default"


            pref.learning_history.append({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'old_value': old_value,
                'new_value': value,
                'reason': 'explicit_setting' if user_explicit else 'system_update_via_set_preference',
                'confidence': pref.confidence
            })
            if len(pref.learning_history) > 50: # Keep history manageable
                pref.learning_history = pref.learning_history[-50:]


if __name__ == "__main__":
    async def test_user_preferences_main():
        print("--- Testing UserPreferenceManager ---")
        manager = UserPreferenceManager("test_user_main")

        # Test getting default preference
        programming_weight = await manager.get_preference('domain_weight_programming')
        print(f"Initial Programming weight: {programming_weight}")
        assert programming_weight == 1.0

        # Test setting preference explicitly
        success = await manager.set_preference(
            'domain_weight_programming',
            0.95,
            PreferenceType.DOMAIN_WEIGHT,
            user_explicit=True
        )
        print(f"Setting 'domain_weight_programming' to 0.95 explicitly: Success = {success}")
        updated_weight = await manager.get_preference('domain_weight_programming')
        print(f"Updated Programming weight: {updated_weight}")
        assert updated_weight == 0.95
        assert manager.preferences['domain_weight_programming'].user_explicit is True
        assert manager.preferences['domain_weight_programming'].confidence == 1.0

        # Test feedback recording and positive reinforcement
        print("--- Testing Positive Feedback ---")
        # Since it's user_explicit, learning should not change it. Let's test another one.
        docs_weight_before_feedback = await manager.get_preference('domain_weight_documentation')
        print(f"Docs weight before feedback: {docs_weight_before_feedback}")


        await manager.record_feedback(
            action="classification_success", # Example action
            context={'classification_result': {'domain': 'documentation', 'confidence': 0.9}},
            feedback_type=LearningSignal.POSITIVE_FEEDBACK
        )
        docs_weight_after_feedback = await manager.get_preference('domain_weight_documentation')
        print(f"Docs weight after positive feedback: {docs_weight_after_feedback}")
        assert docs_weight_after_feedback > docs_weight_before_feedback # Expecting increase

        # Test that explicit setting is not overridden by learning
        explicit_weight_before_learning = await manager.get_preference('domain_weight_programming')
        await manager.record_feedback(
            action="classification_success",
            context={'classification_result': {'domain': 'programming', 'confidence': 0.99}}, # High confidence
            feedback_type=LearningSignal.POSITIVE_FEEDBACK
        )
        explicit_weight_after_learning = await manager.get_preference('domain_weight_programming')
        print(f"Explicitly set programming weight after trying to learn on it: {explicit_weight_after_learning}")
        assert explicit_weight_after_learning == explicit_weight_before_learning # Should not change

        # Test implicit usage learning for search result limit
        print("--- Testing Implicit Usage Feedback (Search Limit) ---")
        search_limit_before = await manager.get_preference('search_result_limit')
        print(f"Search result limit before implicit usage: {search_limit_before}")
        # Simulate a search where user only views few results, implying current limit is too high
        await manager.record_feedback(
            action="search_query",
            context={'result_count': 2}, # User implicitly indicated they needed few results
            feedback_type=LearningSignal.IMPLICIT_USAGE
        )
        search_limit_after = await manager.get_preference('search_result_limit')
        print(f"Search result limit after implicit usage (expected lower): {search_limit_after}")
        assert search_limit_after < search_limit_before
        assert search_limit_after == max(5, int(search_limit_before * 0.9)) # Check logic

        print("--- UserPreferenceManager Test Completed ---")

    asyncio.run(test_user_preferences_main())
