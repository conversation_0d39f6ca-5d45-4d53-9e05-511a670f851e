import asyncio
import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, AsyncMock, patch
from typing import Dict, Any, <PERSON><PERSON>

# Attempt to import EMAL and its dependencies
try:
    from python.helpers.memory_abstraction import EnhancedMemoryAbstractionLayer, MemoryItem # Assuming MemoryItem is used or relevant
    from python.helpers.intelligent_classification_engine import IntelligentClassificationEngine, ClassificationResult, ClassificationStrategy, ClassificationMetrics, ClassificationStatus
    from python.helpers.user_preferences import UserPreferenceManager, LearningSignal, PreferenceType # Enums for feedback
    from python.helpers.hierarchical_memory_manager import HierarchicalMemoryManager # HMM for storage
    # Other components EMAL uses, if they need to be specifically mocked or referenced
    from python.helpers.classification_config import ClassificationConfig # For engine init
    from python.helpers.data_separation import DataSeparationFramework # Corrected import name
    from python.helpers.boundary_enforcement import BoundaryEnforcementSystem # Corrected import name
    from python.helpers.performance_utils import PerformanceTimer # Corrected import name
except ImportError as e:
    print(f"test_memory_abstraction.py: Error importing modules: {e}. Tests may fail or not run.")
    # Minimal placeholders
    class EnhancedMemoryAbstractionLayer: pass
    class MemoryItem(MagicMock): pass
    class IntelligentClassificationEngine(AsyncMock): pass # AsyncMock for methods
    class ClassificationResult(MagicMock): pass
    class ClassificationStrategy(MagicMock): pass # Assuming it's an Enum or has .value
    class ClassificationMetrics(MagicMock): pass
    class ClassificationStatus(MagicMock): pass # Assuming it's an Enum or has .value
    class UserPreferenceManager(AsyncMock): pass
    class LearningSignal(MagicMock): pass # Assuming it's an Enum or has .value
    class PreferenceType(MagicMock): pass
    class HierarchicalMemoryManager(AsyncMock): pass
    class ClassificationConfig(MagicMock): pass
    class DataSeparationFramework(AsyncMock): pass # Placeholder for DataSeparationFramework
    class BoundaryEnforcementSystem(AsyncMock): pass # Placeholder for BoundaryEnforcementSystem
    class PerformanceTimer(MagicMock): # Placeholder for PerformanceTimer
        def __init__(self, name: str): self.name = name
        async def __aenter__(self): pass
        async def __aexit__(self, exc_type, exc, tb): pass


# Helper to run async tests
def async_test(coro):
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

class TestEnhancedMemoryAbstractionLayerWithPreferences(unittest.TestCase):

    def setUp(self):
        self.test_user_id = "emal_test_user"

        # Patch the global classification_config if it's used directly in EMAL's __init__ for engine
        # For this test, we assume ClassificationConfig() can be passed directly.
        self.test_classification_config = ClassificationConfig()


        # Instantiate EMAL - this will also instantiate its components.
        # We will then patch the *instances* of these components on self.emal.
        self.emal = EnhancedMemoryAbstractionLayer(user_id=self.test_user_id)

        # Mock the internal components of the EMAL instance
        self.mock_classification_engine = AsyncMock(spec=IntelligentClassificationEngine)
        self.emal.classification_engine = self.mock_classification_engine

        self.mock_memory_manager = AsyncMock(spec=HierarchicalMemoryManager)
        self.emal.memory_manager = self.mock_memory_manager

        self.mock_user_preferences = AsyncMock(spec=UserPreferenceManager)
        self.mock_user_preferences.user_id = self.test_user_id
        self.emal.user_preferences = self.mock_user_preferences

        self.mock_data_separation = AsyncMock(spec=DataSeparationFramework)
        self.emal.data_separation = self.mock_data_separation
        self.mock_data_separation.separate_mixed_data.return_value = MagicMock(
            history_items=[], knowledge_items=[], mixed_items=[]
        )

        self.mock_boundary_enforcement = AsyncMock(spec=BoundaryEnforcementSystem)
        self.emal.boundary_enforcement = self.mock_boundary_enforcement
        self.mock_boundary_enforcement.check_data_boundaries.return_value = MagicMock(is_valid=True, violations=[])

        self.test_content = "Test content for EMAL with preferences."
        self.test_content_type = "test_document"
        self.test_metadata = {"source": "emal_preference_test"}

        self.emal.is_initialized = True

    def tearDown(self):
        patch.stopall()

    @async_test
    async def test_store_with_user_preferences_calls_engine_with_prefs(self):
        # Prepare a mock ClassificationResult
        mock_cls_result_instance = MagicMock(spec=ClassificationResult)
        mock_cls_result_instance.classification_id="cls_pref_123"
        # Mock strategy as an object with a 'value' attribute if ClassificationStrategy is an Enum
        mock_cls_result_instance.strategy = MagicMock()
        mock_cls_result_instance.strategy.value = "CUSTOM_DOMAIN" # Example, ensure consistent with actual enum if used
        mock_cls_result_instance.confidence=0.95
        mock_cls_result_instance.metrics=MagicMock(spec=ClassificationMetrics)
        mock_cls_result_instance.reasoning="Classified with user preferences."
        mock_cls_result_instance.processing_time_ms=10.0
        mock_cls_result_instance.namespace="user_ns"
        mock_cls_result_instance.domain="user_domain"
        mock_cls_result_instance.timestamp=MagicMock()
        mock_cls_result_instance.status = MagicMock() # Mock status object
        mock_cls_result_instance.status.value = "COMPLETED" # Example value
        mock_cls_result_instance.cache_hit=False

        self.mock_classification_engine.classify_content_with_preferences.return_value = mock_cls_result_instance
        self.mock_memory_manager.store_memory.return_value = "item_id_pref_123"

        with patch.object(self.emal, '_create_feedback_opportunity', new_callable=AsyncMock) as mock_create_fb_op:
            item_id, result = await self.emal.store_with_user_preferences(
                content=self.test_content,
                content_type=self.test_content_type,
                metadata=self.test_metadata,
                user_id=self.test_user_id
            )

            self.mock_classification_engine.classify_content_with_preferences.assert_called_once()
            # Correctly access call_args: .call_args[0] for positional, .call_args[1] for kwargs
            call_args_dict = self.mock_classification_engine.classify_content_with_preferences.call_args.kwargs
            self.assertEqual(call_args_dict['user_id'], self.test_user_id)
            self.assertEqual(call_args_dict['content'], self.test_content)
            self.assertIn('user_id', call_args_dict['metadata'])
            self.assertEqual(call_args_dict['metadata']['user_id'], self.test_user_id)

            self.mock_memory_manager.store_memory.assert_called_once_with(
                self.test_content, call_args_dict['metadata'], mock_cls_result_instance
            )
            self.assertEqual(item_id, "item_id_pref_123")
            self.assertEqual(result, mock_cls_result_instance)
            mock_create_fb_op.assert_called_once_with(self.test_user_id, item_id, result)


    @async_test
    async def test_store_with_user_preferences_shared_ontology_path(self):
        mock_cls_result_shared = MagicMock(spec=ClassificationResult)
        mock_cls_result_shared.classification_id="cls_shared_pref_456"
        mock_cls_result_shared.strategy = MagicMock()
        # Use the actual enum member if available, otherwise mock its .value behavior
        mock_cls_result_shared.strategy.value = ClassificationStrategy.SHARED_ONTOLOGY.value if hasattr(ClassificationStrategy.SHARED_ONTOLOGY, 'value') else "shared_ontology"

        mock_cls_result_shared.confidence=0.90
        mock_cls_result_shared.metrics=MagicMock(spec=ClassificationMetrics)
        mock_cls_result_shared.reasoning="Routed to shared ontology by preference."
        mock_cls_result_shared.processing_time_ms=12.0
        mock_cls_result_shared.namespace="shared_ns"; mock_cls_result_shared.domain="shared_domain"
        mock_cls_result_shared.timestamp=MagicMock(); mock_cls_result_shared.status = MagicMock(); mock_cls_result_shared.status.value = "COMPLETED"; mock_cls_result_shared.cache_hit=False

        self.mock_classification_engine.classify_content_with_preferences.return_value = mock_cls_result_shared

        with patch.object(self.emal, '_store_in_shared_ontology_with_preferences', new_callable=AsyncMock, return_value="item_shared_456") as mock_store_shared, \
             patch.object(self.emal, '_create_feedback_opportunity', new_callable=AsyncMock) as mock_create_fb_op:

            item_id, result = await self.emal.store_with_user_preferences(
                content="shared content", content_type="knowledge_snippet", user_id=self.test_user_id
            )

            self.mock_classification_engine.classify_content_with_preferences.assert_called_once()
            mock_store_shared.assert_called_once()
            call_args_store_shared = mock_store_shared.call_args[0]
            self.assertEqual(call_args_store_shared[0], "shared content")
            self.assertEqual(call_args_store_shared[1]['user_id'], self.test_user_id)
            self.assertEqual(call_args_store_shared[2], mock_cls_result_shared)
            self.assertEqual(call_args_store_shared[3], self.test_user_id)

            self.assertEqual(item_id, "item_shared_456")
            self.assertEqual(result, mock_cls_result_shared)
            mock_create_fb_op.assert_called_once()


    @async_test
    async def test_store_in_shared_ontology_with_preferences_calls_memory_manager(self):
        classification_res = MagicMock(spec=ClassificationResult)
        classification_res.strategy = MagicMock()
        classification_res.strategy.value = "SHARED_ONTOLOGY"
        metadata_arg = {"user_id": self.test_user_id, "content_type": "ontology_data"}

        self.mock_memory_manager.store_memory.return_value = "stored_in_hmm_id_789"

        with patch('builtins.print') as mock_print:
            item_id = await self.emal._store_in_shared_ontology_with_preferences(
                "ontology content", metadata_arg, classification_res, self.test_user_id
            )
            self.mock_memory_manager.store_memory.assert_called_once_with(
                "ontology content", metadata_arg, classification_res
            )
            self.assertEqual(item_id, "stored_in_hmm_id_789")
            mock_print.assert_any_call(f"INFO ({self.test_user_id}): Storing content in shared ontology. Strategy: {classification_res.strategy.value}, Item: ontology_data")


    @async_test
    async def test_create_feedback_opportunity_logging(self):
        item_id = "fb_item_001"
        mock_cls_result = MagicMock(spec=ClassificationResult)
        mock_cls_result.strategy = MagicMock()
        mock_cls_result.strategy.value = "TEST_STRATEGY_FB"
        mock_cls_result.confidence = 0.789
        mock_cls_result.reasoning = "This is a test reasoning for feedback."

        with patch('builtins.print') as mock_print:
            await self.emal._create_feedback_opportunity(self.test_user_id, item_id, mock_cls_result)

            mock_print.assert_any_call(f"INFO ({self.test_user_id}): Feedback opportunity created for item {item_id}.")
            mock_print.assert_any_call(f"  Classification Strategy: {mock_cls_result.strategy.value}")
            mock_print.assert_any_call(f"  Confidence: {mock_cls_result.confidence:.2f}")
            mock_print.assert_any_call(f"  Reasoning: {mock_cls_result.reasoning[:100]}...")


    @async_test
    async def test_collect_user_feedback_success(self):
        item_id_for_feedback = "item_xyz_feedback"
        feedback_type_str = "classification_correctness"
        feedback_value_bool = True
        learning_signal_to_use = LearningSignal.POSITIVE if hasattr(LearningSignal.POSITIVE, 'value') else MagicMock(value="POSITIVE") # Mock enum member

        context_for_feedback = {"comment": "User confirmed classification was good."}

        self.mock_user_preferences.record_feedback = AsyncMock()

        result = await self.emal.collect_user_feedback(
            user_id=self.test_user_id,
            item_id=item_id_for_feedback,
            feedback_type=feedback_type_str,
            feedback_value=feedback_value_bool,
            signal=learning_signal_to_use,
            context=context_for_feedback
        )

        self.assertTrue(result.get('success'))
        self.mock_user_preferences.record_feedback.assert_called_once()
        call_args_dict_feedback = self.mock_user_preferences.record_feedback.call_args.kwargs

        self.assertEqual(call_args_dict_feedback['action'], f"explicit_feedback_{feedback_type_str}")
        self.assertEqual(call_args_dict_feedback['value'], feedback_value_bool)
        self.assertEqual(call_args_dict_feedback['signal_type'], learning_signal_to_use)
        self.assertIn("item_id", call_args_dict_feedback['context'])
        self.assertEqual(call_args_dict_feedback['context']['item_id'], item_id_for_feedback)
        self.assertIn("comment", call_args_dict_feedback['context'])


    @async_test
    async def test_collect_user_feedback_user_id_mismatch(self):
        different_user_id = "different_user_999"

        # This test assumes that if a different user_id is passed, and EMAL does not have a
        # _get_user_preference_manager (which it doesn't per current plan for EMAL itself),
        # it should result in an error or specific handling.
        # The current implementation prints a WARN and uses self.emal.user_preferences if _get_user_preference_manager is missing.
        # For a strict test of mismatch error, we'd need to ensure _get_user_preference_manager is not there.
        # The provided code for collect_user_feedback has a path that prints a WARN if _get_user_preference_manager is not found.
        # Let's test that path.
        if hasattr(self.emal, '_get_user_preference_manager'):
            delattr(self.emal, '_get_user_preference_manager') # Ensure it's not there for this test

        with patch('builtins.print') as mock_print:
            result = await self.emal.collect_user_feedback(
                user_id=different_user_id,
                item_id="item_abc",
                feedback_type="any_type",
                feedback_value="any_value",
                signal=LearningSignal.POSITIVE if hasattr(LearningSignal.POSITIVE, 'value') else MagicMock(value="POSITIVE")
            )
            # Given the current code, it will print a WARN then an ERROR because _get_user_preference_manager is not on EMAL.
            # And then it will try to use self.user_preferences.record_feedback.
            # The "User ID mismatch" error is only returned if _get_user_preference_manager does not exist AND the WARN path is taken.
            # The logic in the provided solution for collect_user_feedback is a bit complex here.
            # It seems the intent was: if user_id differs AND we can't get the right UPM, then error.
            # The current code in the prompt will print a WARN and then an ERROR if _get_user_preference_manager is missing.
            # Let's check for the print WARN and then that record_feedback on the original UPM was still called.
            # This indicates the WARN path was taken.
            mock_print.assert_any_call(f"WARN: Feedback collected for user '{different_user_id}' which differs from EMAL instance user '{self.test_user_id}'. This may require fetching a different UPM if EMAL is multi-user.")
            mock_print.assert_any_call(f"ERROR: EMAL is not equipped to handle feedback for arbitrary user_ids without a _get_user_preference_manager method.")
            self.assertFalse(result.get('success')) # Should fail because _get_user_preference_manager is missing.
            self.assertIn("User ID mismatch", result.get('error', ''))


    @async_test
    async def test_store_with_user_preferences_engine_method_missing_fallback(self):
        # Simulate classification_engine not having classify_content_with_preferences
        # but having classify_content (standard method)
        mock_standard_classification_result = MagicMock(spec=ClassificationResult)
        mock_standard_classification_result.strategy = MagicMock(); mock_standard_classification_result.strategy.value = "STANDARD_FALLBACK"

        original_engine_method = self.mock_classification_engine.classify_content_with_preferences
        del self.mock_classification_engine.classify_content_with_preferences
        self.mock_classification_engine.classify_content = AsyncMock(return_value=mock_standard_classification_result)

        with patch('builtins.print') as mock_print, \
             patch.object(self.emal, '_create_feedback_opportunity', new_callable=AsyncMock): # Mock feedback op

            await self.emal.store_with_user_preferences(
                content=self.test_content,
                content_type=self.test_content_type,
                user_id=self.test_user_id
            )

            mock_print.assert_any_call(f"WARN: Classification engine {type(self.mock_classification_engine).__name__} does not support preference-aware classification. Falling back to standard classification.")
            self.mock_classification_engine.classify_content.assert_called_once()

        # Restore the method for other tests
        self.mock_classification_engine.classify_content_with_preferences = original_engine_method


if __name__ == '__main__':
    unittest.main()
