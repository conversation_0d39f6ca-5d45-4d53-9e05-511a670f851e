# Phase 5: Data Separation Enforcement - Boundary Enforcement
## Strict Boundary Implementation

This document provides implementation for strict boundary enforcement mechanisms that prevent data leakage between the history and knowledge systems.

## 🛡️ Boundary Enforcement Architecture

### Core Principles
1. **Zero Data Duplication**: No data exists in both systems simultaneously
2. **Strict Access Control**: Each system can only access its designated data
3. **Validated Transfers**: All data transfers are validated and logged
4. **Integrity Monitoring**: Continuous monitoring for boundary violations

## 🔧 Implementation

### Step 5.2: Create Boundary Enforcement System

**File:** `python/helpers/boundary_enforcement.py`
**Action:** Create new file

```python
"""
Boundary Enforcement System - Prevents data leakage between systems
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import asyncio
import hashlib
import json

from .data_separation_framework import DataDomain, DataItem, DataType

class BoundaryViolationType(Enum):
    """Types of boundary violations"""
    DATA_DUPLICATION = "data_duplication"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    CROSS_DOMAIN_LEAK = "cross_domain_leak"
    INTEGRITY_VIOLATION = "integrity_violation"
    VALIDATION_FAILURE = "validation_failure"

class AccessLevel(Enum):
    """Access levels for different operations"""
    READ_ONLY = "read_only"
    WRITE_ONLY = "write_only"
    READ_WRITE = "read_write"
    NO_ACCESS = "no_access"
    ADMIN = "admin"

@dataclass
class BoundaryViolation:
    """Represents a boundary violation"""
    violation_type: BoundaryViolationType
    source_system: str
    target_system: str
    data_id: str
    description: str
    severity: str  # "low", "medium", "high", "critical"
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class AccessRequest:
    """Represents an access request"""
    requesting_system: str
    target_domain: DataDomain
    operation: str  # "read", "write", "delete", "update"
    data_id: Optional[str]
    access_level: AccessLevel
    justification: str
    timestamp: datetime

@dataclass
class BoundaryCheckResult:
    """Result of boundary validation check"""
    is_valid: bool
    violations: List[BoundaryViolation]
    warnings: List[str]
    recommendations: List[str]
    check_duration_ms: float

class BoundaryEnforcementInterface(ABC):
    """Abstract interface for boundary enforcement"""
    
    @abstractmethod
    async def validate_access_request(self, request: AccessRequest) -> bool:
        """Validate if access request should be allowed"""
        pass
    
    @abstractmethod
    async def check_data_boundaries(self, data_items: List[DataItem]) -> BoundaryCheckResult:
        """Check if data items violate boundaries"""
        pass
    
    @abstractmethod
    async def enforce_separation(self, source_domain: DataDomain, target_domain: DataDomain) -> bool:
        """Enforce separation between domains"""
        pass

class BoundaryEnforcementSystem(BoundaryEnforcementInterface):
    """
    Core boundary enforcement system implementation
    """
    
    def __init__(self):
        self.access_rules = self._initialize_access_rules()
        self.violation_history: List[BoundaryViolation] = []
        self.data_registry: Dict[str, DataItem] = {}
        self.access_log: List[AccessRequest] = []
        self.monitoring_enabled = True
        
        # Boundary enforcement statistics
        self.stats = {
            'total_access_requests': 0,
            'denied_requests': 0,
            'violations_detected': 0,
            'data_items_tracked': 0,
            'boundary_checks_performed': 0
        }
    
    def _initialize_access_rules(self) -> Dict[str, Dict[str, AccessLevel]]:
        """Initialize access control rules"""
        return {
            'history_system': {
                DataDomain.HISTORY.value: AccessLevel.READ_WRITE,
                DataDomain.KNOWLEDGE.value: AccessLevel.NO_ACCESS,
                DataDomain.MIXED.value: AccessLevel.READ_ONLY,
                DataDomain.UNKNOWN.value: AccessLevel.NO_ACCESS
            },
            'knowledge_system': {
                DataDomain.HISTORY.value: AccessLevel.NO_ACCESS,
                DataDomain.KNOWLEDGE.value: AccessLevel.READ_WRITE,
                DataDomain.MIXED.value: AccessLevel.READ_ONLY,
                DataDomain.UNKNOWN.value: AccessLevel.READ_ONLY
            },
            'integration_layer': {
                DataDomain.HISTORY.value: AccessLevel.READ_ONLY,
                DataDomain.KNOWLEDGE.value: AccessLevel.READ_ONLY,
                DataDomain.MIXED.value: AccessLevel.READ_WRITE,
                DataDomain.UNKNOWN.value: AccessLevel.READ_ONLY
            },
            'admin_system': {
                DataDomain.HISTORY.value: AccessLevel.ADMIN,
                DataDomain.KNOWLEDGE.value: AccessLevel.ADMIN,
                DataDomain.MIXED.value: AccessLevel.ADMIN,
                DataDomain.UNKNOWN.value: AccessLevel.ADMIN
            }
        }
    
    async def validate_access_request(self, request: AccessRequest) -> bool:
        """
        Validate if access request should be allowed based on rules
        """
        self.stats['total_access_requests'] += 1
        
        # Log the access request
        self.access_log.append(request)
        
        # Check if requesting system has appropriate access level
        system_rules = self.access_rules.get(request.requesting_system, {})
        allowed_access = system_rules.get(request.target_domain.value, AccessLevel.NO_ACCESS)
        
        # Validate operation against access level
        operation_allowed = self._validate_operation(request.operation, allowed_access)
        
        if not operation_allowed:
            self.stats['denied_requests'] += 1
            
            # Log violation
            violation = BoundaryViolation(
                violation_type=BoundaryViolationType.UNAUTHORIZED_ACCESS,
                source_system=request.requesting_system,
                target_system=request.target_domain.value,
                data_id=request.data_id or "unknown",
                description=f"Unauthorized {request.operation} access attempt",
                severity="high",
                timestamp=datetime.now(),
                metadata={
                    'requested_access': allowed_access.value,
                    'operation': request.operation,
                    'justification': request.justification
                }
            )
            
            await self._record_violation(violation)
            return False
        
        return True
    
    def _validate_operation(self, operation: str, access_level: AccessLevel) -> bool:
        """Validate if operation is allowed for given access level"""
        
        if access_level == AccessLevel.NO_ACCESS:
            return False
        elif access_level == AccessLevel.READ_ONLY:
            return operation in ['read', 'query', 'search']
        elif access_level == AccessLevel.WRITE_ONLY:
            return operation in ['write', 'create', 'insert']
        elif access_level == AccessLevel.READ_WRITE:
            return operation in ['read', 'write', 'create', 'update', 'query', 'search', 'insert']
        elif access_level == AccessLevel.ADMIN:
            return True  # Admin can do anything
        
        return False
    
    async def check_data_boundaries(self, data_items: List[DataItem]) -> BoundaryCheckResult:
        """
        Comprehensive boundary validation for data items
        """
        import time
        start_time = time.time()
        
        self.stats['boundary_checks_performed'] += 1
        
        violations = []
        warnings = []
        recommendations = []
        
        # Check for data duplication
        duplication_violations = await self._check_data_duplication(data_items)
        violations.extend(duplication_violations)
        
        # Check for cross-domain leaks
        leak_violations = await self._check_cross_domain_leaks(data_items)
        violations.extend(leak_violations)
        
        # Check data integrity
        integrity_violations = await self._check_data_integrity(data_items)
        violations.extend(integrity_violations)
        
        # Check for domain consistency
        consistency_warnings = await self._check_domain_consistency(data_items)
        warnings.extend(consistency_warnings)
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(violations, warnings)
        
        # Update data registry
        for item in data_items:
            self.data_registry[item.id] = item
            self.stats['data_items_tracked'] += 1
        
        check_duration = (time.time() - start_time) * 1000
        
        return BoundaryCheckResult(
            is_valid=len(violations) == 0,
            violations=violations,
            warnings=warnings,
            recommendations=recommendations,
            check_duration_ms=check_duration
        )
    
    async def _check_data_duplication(self, data_items: List[DataItem]) -> List[BoundaryViolation]:
        """Check for data duplication across domains"""
        violations = []
        
        # Group items by domain
        domain_items = {}
        for item in data_items:
            domain = item.data_domain
            if domain not in domain_items:
                domain_items[domain] = []
            domain_items[domain].append(item)
        
        # Check for content duplication across domains
        for domain1, items1 in domain_items.items():
            for domain2, items2 in domain_items.items():
                if domain1 != domain2 and domain1 != DataDomain.MIXED and domain2 != DataDomain.MIXED:
                    duplicates = self._find_content_duplicates(items1, items2)
                    
                    for item1, item2 in duplicates:
                        violation = BoundaryViolation(
                            violation_type=BoundaryViolationType.DATA_DUPLICATION,
                            source_system=domain1.value,
                            target_system=domain2.value,
                            data_id=f"{item1.id},{item2.id}",
                            description=f"Duplicate content found across {domain1.value} and {domain2.value}",
                            severity="critical",
                            timestamp=datetime.now(),
                            metadata={
                                'item1_id': item1.id,
                                'item2_id': item2.id,
                                'content_hash': self._calculate_content_hash(item1.content)
                            }
                        )
                        violations.append(violation)
        
        return violations
    
    def _find_content_duplicates(self, items1: List[DataItem], items2: List[DataItem]) -> List[Tuple[DataItem, DataItem]]:
        """Find content duplicates between two lists of items"""
        duplicates = []
        
        # Create content hash map for items1
        hash_map = {}
        for item in items1:
            content_hash = self._calculate_content_hash(item.content)
            hash_map[content_hash] = item
        
        # Check items2 against hash map
        for item in items2:
            content_hash = self._calculate_content_hash(item.content)
            if content_hash in hash_map:
                duplicates.append((hash_map[content_hash], item))
        
        return duplicates
    
    def _calculate_content_hash(self, content: Any) -> str:
        """Calculate hash of content for duplication detection"""
        content_str = json.dumps(content, sort_keys=True, default=str)
        return hashlib.sha256(content_str.encode()).hexdigest()
    
    async def _check_cross_domain_leaks(self, data_items: List[DataItem]) -> List[BoundaryViolation]:
        """Check for cross-domain data leaks"""
        violations = []
        
        for item in data_items:
            # Check if item contains data that should be in a different domain
            leak_indicators = await self._detect_domain_leak_indicators(item)
            
            for indicator in leak_indicators:
                violation = BoundaryViolation(
                    violation_type=BoundaryViolationType.CROSS_DOMAIN_LEAK,
                    source_system=item.data_domain.value,
                    target_system=indicator['expected_domain'],
                    data_id=item.id,
                    description=f"Data contains {indicator['expected_domain']} indicators but is in {item.data_domain.value}",
                    severity="medium",
                    timestamp=datetime.now(),
                    metadata={
                        'leak_type': indicator['type'],
                        'confidence': indicator['confidence']
                    }
                )
                violations.append(violation)
        
        return violations
    
    async def _detect_domain_leak_indicators(self, item: DataItem) -> List[Dict[str, Any]]:
        """Detect indicators that data might belong to a different domain"""
        indicators = []
        content_str = str(item.content).lower()
        
        # History indicators in knowledge domain
        if item.data_domain == DataDomain.KNOWLEDGE:
            history_indicators = ['user:', 'assistant:', 'conversation', 'chat', 'message']
            for indicator in history_indicators:
                if indicator in content_str:
                    indicators.append({
                        'type': 'history_in_knowledge',
                        'expected_domain': 'history',
                        'confidence': 0.8,
                        'indicator': indicator
                    })
        
        # Knowledge indicators in history domain
        if item.data_domain == DataDomain.HISTORY:
            knowledge_indicators = ['entity:', 'relationship:', 'fact:', 'concept:']
            for indicator in knowledge_indicators:
                if indicator in content_str:
                    indicators.append({
                        'type': 'knowledge_in_history',
                        'expected_domain': 'knowledge',
                        'confidence': 0.7,
                        'indicator': indicator
                    })
        
        return indicators
    
    async def _check_data_integrity(self, data_items: List[DataItem]) -> List[BoundaryViolation]:
        """Check data integrity constraints"""
        violations = []
        
        for item in data_items:
            # Check required fields
            if not item.id or not item.content or not item.data_domain:
                violation = BoundaryViolation(
                    violation_type=BoundaryViolationType.INTEGRITY_VIOLATION,
                    source_system=item.source_system,
                    target_system="validation",
                    data_id=item.id or "unknown",
                    description="Missing required fields in data item",
                    severity="high",
                    timestamp=datetime.now(),
                    metadata={
                        'missing_fields': [
                            field for field in ['id', 'content', 'data_domain']
                            if not getattr(item, field, None)
                        ]
                    }
                )
                violations.append(violation)
            
            # Check domain consistency
            if item.data_domain == DataDomain.UNKNOWN:
                violation = BoundaryViolation(
                    violation_type=BoundaryViolationType.INTEGRITY_VIOLATION,
                    source_system=item.source_system,
                    target_system="validation",
                    data_id=item.id,
                    description="Data item has unknown domain classification",
                    severity="medium",
                    timestamp=datetime.now(),
                    metadata={'data_type': item.data_type.value if item.data_type else 'unknown'}
                )
                violations.append(violation)
        
        return violations
    
    async def _check_domain_consistency(self, data_items: List[DataItem]) -> List[str]:
        """Check for domain consistency issues (warnings)"""
        warnings = []
        
        # Check for mixed domain items
        mixed_items = [item for item in data_items if item.data_domain == DataDomain.MIXED]
        if mixed_items:
            warnings.append(f"Found {len(mixed_items)} items with mixed domain classification")
        
        # Check for items with processing flags
        flagged_items = [item for item in data_items if item.processing_flags.get('requires_manual_review')]
        if flagged_items:
            warnings.append(f"Found {len(flagged_items)} items requiring manual review")
        
        return warnings
    
    async def _generate_recommendations(self, violations: List[BoundaryViolation], warnings: List[str]) -> List[str]:
        """Generate recommendations based on violations and warnings"""
        recommendations = []
        
        # Recommendations based on violation types
        violation_types = set(v.violation_type for v in violations)
        
        if BoundaryViolationType.DATA_DUPLICATION in violation_types:
            recommendations.append("Implement deduplication process before data storage")
            recommendations.append("Review data separation logic for accuracy")
        
        if BoundaryViolationType.CROSS_DOMAIN_LEAK in violation_types:
            recommendations.append("Enhance domain classification algorithms")
            recommendations.append("Add additional validation rules for domain detection")
        
        if BoundaryViolationType.INTEGRITY_VIOLATION in violation_types:
            recommendations.append("Implement stricter data validation before processing")
            recommendations.append("Add required field validation at data entry points")
        
        # Recommendations based on warnings
        if any("mixed domain" in warning for warning in warnings):
            recommendations.append("Review mixed domain items for proper classification")
        
        if any("manual review" in warning for warning in warnings):
            recommendations.append("Establish manual review process for flagged items")
        
        return recommendations
    
    async def enforce_separation(self, source_domain: DataDomain, target_domain: DataDomain) -> bool:
        """
        Enforce separation between domains
        """
        
        # Check if separation is allowed
        if source_domain == target_domain:
            return True  # Same domain, no separation needed
        
        # History and Knowledge domains should be strictly separated
        if ((source_domain == DataDomain.HISTORY and target_domain == DataDomain.KNOWLEDGE) or
            (source_domain == DataDomain.KNOWLEDGE and target_domain == DataDomain.HISTORY)):
            
            # Log separation enforcement
            violation = BoundaryViolation(
                violation_type=BoundaryViolationType.UNAUTHORIZED_ACCESS,
                source_system=source_domain.value,
                target_system=target_domain.value,
                data_id="separation_enforcement",
                description=f"Enforced separation between {source_domain.value} and {target_domain.value}",
                severity="info",
                timestamp=datetime.now(),
                metadata={'action': 'separation_enforced'}
            )
            
            await self._record_violation(violation)
            return False
        
        # Mixed domain can interact with others through integration layer
        if source_domain == DataDomain.MIXED or target_domain == DataDomain.MIXED:
            return True
        
        return True
    
    async def _record_violation(self, violation: BoundaryViolation):
        """Record a boundary violation"""
        self.violation_history.append(violation)
        self.stats['violations_detected'] += 1
        
        # Keep violation history manageable
        if len(self.violation_history) > 1000:
            self.violation_history = self.violation_history[-1000:]
        
        # Log critical violations
        if violation.severity == "critical":
            print(f"CRITICAL BOUNDARY VIOLATION: {violation.description}")
    
    def get_violation_summary(self) -> Dict[str, Any]:
        """Get summary of boundary violations"""
        
        if not self.violation_history:
            return {
                'total_violations': 0,
                'by_type': {},
                'by_severity': {},
                'recent_violations': []
            }
        
        # Count by type
        by_type = {}
        for violation in self.violation_history:
            vtype = violation.violation_type.value
            by_type[vtype] = by_type.get(vtype, 0) + 1
        
        # Count by severity
        by_severity = {}
        for violation in self.violation_history:
            severity = violation.severity
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        # Recent violations (last 10)
        recent_violations = [
            {
                'type': v.violation_type.value,
                'severity': v.severity,
                'description': v.description,
                'timestamp': v.timestamp.isoformat()
            }
            for v in self.violation_history[-10:]
        ]
        
        return {
            'total_violations': len(self.violation_history),
            'by_type': by_type,
            'by_severity': by_severity,
            'recent_violations': recent_violations,
            'stats': self.stats
        }
    
    def get_access_log_summary(self) -> Dict[str, Any]:
        """Get summary of access requests"""
        
        if not self.access_log:
            return {
                'total_requests': 0,
                'by_system': {},
                'by_operation': {},
                'recent_requests': []
            }
        
        # Count by requesting system
        by_system = {}
        for request in self.access_log:
            system = request.requesting_system
            by_system[system] = by_system.get(system, 0) + 1
        
        # Count by operation
        by_operation = {}
        for request in self.access_log:
            operation = request.operation
            by_operation[operation] = by_operation.get(operation, 0) + 1
        
        # Recent requests (last 10)
        recent_requests = [
            {
                'system': r.requesting_system,
                'operation': r.operation,
                'domain': r.target_domain.value,
                'timestamp': r.timestamp.isoformat()
            }
            for r in self.access_log[-10:]
        ]
        
        return {
            'total_requests': len(self.access_log),
            'by_system': by_system,
            'by_operation': by_operation,
            'recent_requests': recent_requests
        }
```

**Validation:**
```python
# Test boundary enforcement system
from python.helpers.boundary_enforcement import BoundaryEnforcementSystem, AccessRequest, DataDomain, AccessLevel
from python.helpers.data_separation_framework import DataItem, DataType
from datetime import datetime

enforcement = BoundaryEnforcementSystem()

# Test access validation
request = AccessRequest(
    requesting_system="history_system",
    target_domain=DataDomain.KNOWLEDGE,
    operation="read",
    data_id="test_001",
    access_level=AccessLevel.READ_ONLY,
    justification="Testing access control",
    timestamp=datetime.now()
)

access_allowed = await enforcement.validate_access_request(request)
print(f"Access allowed: {access_allowed}")

# Test boundary checking
test_items = [
    DataItem("1", "user: hello", DataType.CONVERSATION_MESSAGE, DataDomain.HISTORY, {}, datetime.now(), "test", {}),
    DataItem("2", "entity: greeting", DataType.ENTITY_RELATIONSHIP, DataDomain.KNOWLEDGE, {}, datetime.now(), "test", {})
]

result = await enforcement.check_data_boundaries(test_items)
print(f"Boundary check valid: {result.is_valid}, violations: {len(result.violations)}")
```

---

**Next Step**: [Integration APIs](integration-apis.md)
