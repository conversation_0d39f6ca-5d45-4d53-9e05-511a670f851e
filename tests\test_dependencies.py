"""
Test suite for dependency verification and functionality
"""

import pytest
import numpy as np
import scipy
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import networkx as nx
from dateutil import parser # type: ignore
import psutil
from datetime import datetime

# Attempt to import env_loader and classification_config for testing
# These imports assume that the `python` directory is in PYTHONPATH
# or that pytest is run from the project root.
try:
    from python.helpers.env_loader import env_loader
    ENV_LOADER_AVAILABLE = True
except ImportError:
    ENV_LOADER_AVAILABLE = False
    env_loader = None # type: ignore

class TestDependencies:
    """Test all required dependencies"""

    def test_numpy_functionality(self):
        """Test NumPy basic functionality"""
        arr1 = np.array([1, 2, 3, 4, 5])
        arr2 = np.array([2, 4, 6, 8, 10])
        result = arr1 + arr2
        expected = np.array([3, 6, 9, 12, 15])
        assert np.array_equal(result, expected)
        # Common dtypes are np.int32 or np.int64 depending on system architecture
        assert arr1.dtype == np.int64 or arr1.dtype == np.int32

    def test_scipy_functionality(self):
        """Test SciPy basic functionality"""
        from scipy import stats # type: ignore
        data = [1.0, 2.0, 3.0, 4.0, 5.0] # Use floats for tmean
        mean = stats.tmean(data)
        assert mean == 3.0

    def test_sklearn_functionality(self):
        """Test scikit-learn functionality"""
        documents = ["hello world", "world of python", "python programming"]
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform(documents)
        assert tfidf_matrix.shape[0] == 3
        assert tfidf_matrix.shape[1] > 0

        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])
        assert 0 <= similarity[0][0] <= 1

    def test_networkx_functionality(self):
        """Test NetworkX functionality"""
        G = nx.Graph()
        G.add_edge(1, 2)
        G.add_edge(2, 3)
        G.add_edge(3, 4)
        assert G.number_of_nodes() == 4
        assert G.number_of_edges() == 3
        path = nx.shortest_path(G, 1, 4)
        assert path == [1, 2, 3, 4] # Corrected expected path

    def test_dateutil_functionality(self):
        """Test python-dateutil functionality"""
        date_str = "2024-06-18T10:30:00Z"
        parsed_date = parser.parse(date_str)
        assert parsed_date.year == 2024
        assert parsed_date.month == 6
        assert parsed_date.day == 18

    def test_psutil_functionality(self):
        """Test psutil functionality"""
        cpu_percent = psutil.cpu_percent(interval=0.01) # Reduced interval for faster test
        memory_info = psutil.virtual_memory()
        assert isinstance(cpu_percent, float)
        assert 0 <= cpu_percent <= 100
        assert memory_info.total > 0

    @pytest.mark.asyncio
    async def test_async_functionality(self):
        """Test async/await functionality"""
        import asyncio

        async def async_function():
            await asyncio.sleep(0.01)
            return "async_result"

        result = await async_function()
        assert result == "async_result"

@pytest.mark.skipif(not ENV_LOADER_AVAILABLE, reason="env_loader module not found, skipping environment config tests")
class TestEnvironmentConfiguration:
    """Test environment configuration using env_loader"""

    def test_environment_loader_basic_retrieval(self):
        """Test basic retrieval from env_loader"""
        assert env_loader is not None, "env_loader was not imported successfully"
        # Assuming .env file has OPENAI_API_KEY="your_openai_api_key_here"
        # and LOG_LEVEL="INFO" (or other defaults set in Phase 0)

        # Test get_str
        openai_key = env_loader.get_str("OPENAI_API_KEY")
        assert openai_key is not None
        # A more specific check might be too brittle if the default .env changes
        # assert openai_key == "your_openai_api_key_here"

        log_level = env_loader.get_str("LOG_LEVEL", "DEBUG") # Test with default
        assert log_level in ["INFO", "DEBUG", "WARNING", "ERROR", "CRITICAL"]

        # Test get_int
        # CLASSIFICATION_PRIVACY_THRESHOLD="3"
        privacy_thresh = env_loader.get_int("CLASSIFICATION_PRIVACY_THRESHOLD", 0)
        assert isinstance(privacy_thresh, int)
        assert privacy_thresh == 3

        # Test get_float
        # CLASSIFICATION_SEMANTIC_OVERLAP_SHARED="0.70"
        overlap_shared = env_loader.get_float("CLASSIFICATION_SEMANTIC_OVERLAP_SHARED", 0.0)
        assert isinstance(overlap_shared, float)
        assert overlap_shared == 0.70

        # Test get_bool
        # ADAPTIVE_LEARNING_ENABLED="true"
        adaptive_learning = env_loader.get_bool("ADAPTIVE_LEARNING_ENABLED", False)
        assert isinstance(adaptive_learning, bool)
        assert adaptive_learning is True

        debug_mode_defined_in_phase0 = env_loader.get_bool("DEBUG_MODE", True) # Default true for test
        assert isinstance(debug_mode_defined_in_phase0, bool)
        # assert debug_mode_defined_in_phase0 is False # As per Phase 0 .env

    def test_env_loader_config_methods(self):
        """Test specialized config dictionary retrieval methods"""
        assert env_loader is not None, "env_loader was not imported successfully"

        classification_cfg = env_loader.get_classification_config()
        assert isinstance(classification_cfg, dict)
        assert 'backend' in classification_cfg
        assert classification_cfg['backend'] == "faiss" # Default from .env
        assert classification_cfg['privacy_threshold'] == 3

        performance_cfg = env_loader.get_performance_config()
        assert isinstance(performance_cfg, dict)
        assert 'working_memory_target_ms' in performance_cfg
        assert performance_cfg['working_memory_target_ms'] == 100 # Default from .env

        memory_cfg = env_loader.get_memory_config()
        assert isinstance(memory_cfg, dict)
        assert 'working_size' in memory_cfg
        assert memory_cfg['working_size'] == 50000 # Default from .env

    def test_env_loader_validation(self):
        """Test validation of required variables"""
        assert env_loader is not None, "env_loader was not imported successfully"

        # OPENAI_API_KEY is set in the .env by default
        assert env_loader.validate_required_vars(["OPENAI_API_KEY"]) is True
        # This one is also set in .env
        assert env_loader.validate_required_vars(["GRAPHITI_API_KEY"]) is True

        # Test with a variable known to be missing
        assert env_loader.validate_required_vars(["A_MISSING_VARIABLE_FOR_TEST"]) is False

        # Test with a mix
        assert env_loader.validate_required_vars(["OPENAI_API_KEY", "ANOTHER_MISSING_ONE"]) is False


# This part of the test from the markdown relies on classification_config.py
# which itself relies on env_loader. So if env_loader is not available, these might also fail.
# For now, let's assume if env_loader is available, classification_config should be too.
@pytest.mark.skipif(not ENV_LOADER_AVAILABLE, reason="env_loader/classification_config modules not found, skipping config value tests")
class TestConfigurationValues: # Renamed from markdown to avoid conflict
    """Test that configuration values loaded are reasonable"""

    def test_performance_config_values(self):
        """Test that performance configuration values are within reasonable bounds"""
        # This test assumes that python.helpers.classification_config.classification_config
        # is initialized using ClassificationConfig.from_env() which uses the global env_loader
        from python.helpers.classification_config import classification_config as global_classification_config

        perf_config = global_classification_config.performance_targets

        # Using values from the .env file created in Phase 1
        # CLASSIFICATION_WORKING_MEMORY_TARGET_MS="100"
        # CLASSIFICATION_LONG_TERM_MEMORY_TARGET_MS="500"
        # CLASSIFICATION_EPISODIC_MEMORY_TARGET_MS="1000"
        assert 10 <= perf_config.working_memory_response_ms <= 1000
        assert 100 <= perf_config.long_term_memory_response_ms <= 5000
        assert 500 <= perf_config.episodic_memory_response_ms <= 10000

        # Check a specific value
        assert perf_config.working_memory_response_ms == 100


if __name__ == "__main__":
    # This allows running the test file directly for debugging.
    # Add -s to see print statements, -v for verbose.
    pytest.main([__file__, "-v"])
