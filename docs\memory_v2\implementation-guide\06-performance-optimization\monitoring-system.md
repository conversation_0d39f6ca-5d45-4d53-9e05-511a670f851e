# Phase 6: Performance Optimization - Monitoring System
## Real-time Performance Monitoring

This document provides implementation for a comprehensive real-time performance monitoring system that tracks all aspects of the intelligent data classification system.

## 📊 Monitoring Architecture

### Key Performance Indicators (KPIs)

1. **Response Time Metrics**
   - Working Memory: <100ms (95th percentile)
   - Long-term Memory: <500ms (95th percentile)
   - Episodic Memory: <1s (90th percentile)
   - Classification: <50ms (99th percentile)

2. **Throughput Metrics**
   - Queries per second (QPS)
   - Classifications per minute
   - Memory operations per second

3. **Resource Utilization**
   - CPU usage
   - Memory consumption
   - Storage I/O
   - Network bandwidth

4. **System Health**
   - Cache hit rates
   - Error rates
   - Queue depths
   - Connection pools

## 🔧 Implementation

### Step 6.3: Create Performance Monitoring System

**File:** `python/helpers/performance_monitoring.py`
**Action:** Create new file

```python
"""
Real-time Performance Monitoring System
"""

from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import time
import psutil
import threading
from collections import deque, defaultdict
import statistics

class MetricType(Enum):
    """Types of metrics to track"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"

class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class MetricValue:
    """Individual metric value with timestamp"""
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)

@dataclass
class PerformanceAlert:
    """Performance alert definition"""
    metric_name: str
    threshold: float
    comparison: str  # "gt", "lt", "eq"
    level: AlertLevel
    message: str
    timestamp: datetime
    resolved: bool = False

@dataclass
class SystemSnapshot:
    """System performance snapshot"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    network_io_mb: float
    active_connections: int
    queue_depths: Dict[str, int]

class PerformanceMonitor:
    """
    Comprehensive performance monitoring system
    """
    
    def __init__(self, collection_interval: float = 1.0, retention_hours: int = 24):
        self.collection_interval = collection_interval
        self.retention_hours = retention_hours
        self.max_samples = int((retention_hours * 3600) / collection_interval)
        
        # Metric storage
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=self.max_samples))
        self.metric_types: Dict[str, MetricType] = {}
        
        # Alert system
        self.alerts: List[PerformanceAlert] = []
        self.alert_rules: List[Dict[str, Any]] = []
        self.alert_callbacks: List[Callable] = []
        
        # System monitoring
        self.system_snapshots: deque = deque(maxlen=self.max_samples)
        self.monitoring_active = False
        self.monitoring_task = None
        
        # Performance targets
        self.performance_targets = {
            'working_memory_response_ms': 100,
            'long_term_memory_response_ms': 500,
            'episodic_memory_response_ms': 1000,
            'classification_decision_ms': 50,
            'cache_hit_rate': 0.85,
            'cpu_usage_percent': 80,
            'memory_usage_percent': 85,
            'error_rate_percent': 1.0
        }
        
        # Initialize default alert rules
        self._initialize_default_alerts()
    
    def _initialize_default_alerts(self):
        """Initialize default performance alert rules"""
        
        default_rules = [
            {
                'metric': 'working_memory_response_ms',
                'threshold': self.performance_targets['working_memory_response_ms'],
                'comparison': 'gt',
                'level': AlertLevel.WARNING,
                'message': 'Working memory response time exceeded target'
            },
            {
                'metric': 'long_term_memory_response_ms',
                'threshold': self.performance_targets['long_term_memory_response_ms'],
                'comparison': 'gt',
                'level': AlertLevel.WARNING,
                'message': 'Long-term memory response time exceeded target'
            },
            {
                'metric': 'classification_decision_ms',
                'threshold': self.performance_targets['classification_decision_ms'],
                'comparison': 'gt',
                'level': AlertLevel.ERROR,
                'message': 'Classification decision time exceeded target'
            },
            {
                'metric': 'cache_hit_rate',
                'threshold': self.performance_targets['cache_hit_rate'],
                'comparison': 'lt',
                'level': AlertLevel.WARNING,
                'message': 'Cache hit rate below target'
            },
            {
                'metric': 'cpu_usage_percent',
                'threshold': self.performance_targets['cpu_usage_percent'],
                'comparison': 'gt',
                'level': AlertLevel.WARNING,
                'message': 'High CPU usage detected'
            },
            {
                'metric': 'memory_usage_percent',
                'threshold': self.performance_targets['memory_usage_percent'],
                'comparison': 'gt',
                'level': AlertLevel.ERROR,
                'message': 'High memory usage detected'
            },
            {
                'metric': 'error_rate_percent',
                'threshold': self.performance_targets['error_rate_percent'],
                'comparison': 'gt',
                'level': AlertLevel.CRITICAL,
                'message': 'Error rate exceeded acceptable threshold'
            }
        ]
        
        self.alert_rules.extend(default_rules)
    
    async def start_monitoring(self):
        """Start the performance monitoring system"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        print("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Stop the performance monitoring system"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        print("Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                await self._collect_system_metrics()
                
                # Check alert conditions
                await self._check_alerts()
                
                # Clean up old data
                await self._cleanup_old_data()
                
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            await self.record_metric('cpu_usage_percent', cpu_percent, MetricType.GAUGE)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            await self.record_metric('memory_usage_percent', memory_percent, MetricType.GAUGE)
            await self.record_metric('memory_used_mb', memory_used_mb, MetricType.GAUGE)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            await self.record_metric('disk_usage_percent', disk_percent, MetricType.GAUGE)
            
            # Network metrics
            network = psutil.net_io_counters()
            network_mb = (network.bytes_sent + network.bytes_recv) / (1024 * 1024)
            await self.record_metric('network_io_mb', network_mb, MetricType.COUNTER)
            
            # Process metrics
            process_count = len(psutil.pids())
            await self.record_metric('process_count', process_count, MetricType.GAUGE)
            
            # Create system snapshot
            snapshot = SystemSnapshot(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                disk_usage_percent=disk_percent,
                network_io_mb=network_mb,
                active_connections=0,  # Would be populated by application
                queue_depths={}  # Would be populated by application
            )
            
            self.system_snapshots.append(snapshot)
            
        except Exception as e:
            print(f"Error collecting system metrics: {e}")
    
    async def record_metric(self, name: str, value: float, metric_type: MetricType, labels: Optional[Dict[str, str]] = None):
        """Record a performance metric"""
        
        metric_value = MetricValue(
            value=value,
            timestamp=datetime.now(),
            labels=labels or {}
        )
        
        self.metrics[name].append(metric_value)
        self.metric_types[name] = metric_type
        
        # Check if this metric triggers any alerts
        await self._check_metric_alerts(name, value)
    
    async def _check_metric_alerts(self, metric_name: str, value: float):
        """Check if a metric value triggers any alerts"""
        
        for rule in self.alert_rules:
            if rule['metric'] == metric_name:
                threshold = rule['threshold']
                comparison = rule['comparison']
                
                triggered = False
                if comparison == 'gt' and value > threshold:
                    triggered = True
                elif comparison == 'lt' and value < threshold:
                    triggered = True
                elif comparison == 'eq' and abs(value - threshold) < 0.001:
                    triggered = True
                
                if triggered:
                    alert = PerformanceAlert(
                        metric_name=metric_name,
                        threshold=threshold,
                        comparison=comparison,
                        level=rule['level'],
                        message=f"{rule['message']}: {value:.2f} (threshold: {threshold})",
                        timestamp=datetime.now()
                    )
                    
                    await self._trigger_alert(alert)
    
    async def _trigger_alert(self, alert: PerformanceAlert):
        """Trigger a performance alert"""
        
        # Check if we already have a recent similar alert
        recent_cutoff = datetime.now() - timedelta(minutes=5)
        similar_alerts = [
            a for a in self.alerts
            if (a.metric_name == alert.metric_name and 
                a.level == alert.level and 
                a.timestamp > recent_cutoff and 
                not a.resolved)
        ]
        
        if similar_alerts:
            return  # Don't spam similar alerts
        
        self.alerts.append(alert)
        
        # Call alert callbacks
        for callback in self.alert_callbacks:
            try:
                await callback(alert)
            except Exception as e:
                print(f"Error in alert callback: {e}")
        
        # Log the alert
        level_str = alert.level.value.upper()
        print(f"[{level_str}] PERFORMANCE ALERT: {alert.message}")
    
    async def _check_alerts(self):
        """Check all alert conditions"""
        
        # Check for stale metrics (no updates in last 5 minutes)
        stale_cutoff = datetime.now() - timedelta(minutes=5)
        
        for metric_name, values in self.metrics.items():
            if values and values[-1].timestamp < stale_cutoff:
                alert = PerformanceAlert(
                    metric_name=metric_name,
                    threshold=0,
                    comparison='eq',
                    level=AlertLevel.WARNING,
                    message=f"Metric {metric_name} has not been updated recently",
                    timestamp=datetime.now()
                )
                await self._trigger_alert(alert)
    
    async def _cleanup_old_data(self):
        """Clean up old metric data and alerts"""
        
        # Clean up old alerts (keep last 1000)
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-1000:]
        
        # Metrics are automatically cleaned up by deque maxlen
    
    def get_metric_statistics(self, metric_name: str, time_window_minutes: int = 60) -> Dict[str, float]:
        """Get statistics for a metric over a time window"""
        
        if metric_name not in self.metrics:
            return {}
        
        cutoff_time = datetime.now() - timedelta(minutes=time_window_minutes)
        recent_values = [
            mv.value for mv in self.metrics[metric_name]
            if mv.timestamp > cutoff_time
        ]
        
        if not recent_values:
            return {}
        
        return {
            'count': len(recent_values),
            'min': min(recent_values),
            'max': max(recent_values),
            'mean': statistics.mean(recent_values),
            'median': statistics.median(recent_values),
            'p95': self._percentile(recent_values, 95),
            'p99': self._percentile(recent_values, 99),
            'stddev': statistics.stdev(recent_values) if len(recent_values) > 1 else 0
        }
    
    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile of values"""
        if not values:
            return 0.0
        
        sorted_values = sorted(values)
        index = (percentile / 100.0) * (len(sorted_values) - 1)
        
        if index.is_integer():
            return sorted_values[int(index)]
        else:
            lower = sorted_values[int(index)]
            upper = sorted_values[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'monitoring_active': self.monitoring_active,
            'metrics_tracked': len(self.metrics),
            'total_alerts': len(self.alerts),
            'active_alerts': len([a for a in self.alerts if not a.resolved]),
            'performance_targets': self.performance_targets,
            'metric_statistics': {},
            'system_health': {},
            'recent_alerts': []
        }
        
        # Get statistics for key metrics
        key_metrics = [
            'working_memory_response_ms',
            'long_term_memory_response_ms',
            'episodic_memory_response_ms',
            'classification_decision_ms',
            'cache_hit_rate',
            'cpu_usage_percent',
            'memory_usage_percent'
        ]
        
        for metric in key_metrics:
            if metric in self.metrics:
                summary['metric_statistics'][metric] = self.get_metric_statistics(metric)
        
        # System health indicators
        if self.system_snapshots:
            latest_snapshot = self.system_snapshots[-1]
            summary['system_health'] = {
                'cpu_percent': latest_snapshot.cpu_percent,
                'memory_percent': latest_snapshot.memory_percent,
                'memory_used_mb': latest_snapshot.memory_used_mb,
                'disk_usage_percent': latest_snapshot.disk_usage_percent,
                'timestamp': latest_snapshot.timestamp.isoformat()
            }
        
        # Recent alerts (last 10)
        recent_alerts = sorted(self.alerts, key=lambda x: x.timestamp, reverse=True)[:10]
        summary['recent_alerts'] = [
            {
                'metric': alert.metric_name,
                'level': alert.level.value,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved
            }
            for alert in recent_alerts
        ]
        
        return summary
    
    def add_alert_callback(self, callback: Callable):
        """Add callback function for alerts"""
        self.alert_callbacks.append(callback)
    
    def add_alert_rule(self, metric: str, threshold: float, comparison: str, level: AlertLevel, message: str):
        """Add custom alert rule"""
        rule = {
            'metric': metric,
            'threshold': threshold,
            'comparison': comparison,
            'level': level,
            'message': message
        }
        self.alert_rules.append(rule)
    
    async def resolve_alert(self, alert_id: int):
        """Mark an alert as resolved"""
        if 0 <= alert_id < len(self.alerts):
            self.alerts[alert_id].resolved = True
    
    def get_metric_history(self, metric_name: str, time_window_minutes: int = 60) -> List[Dict[str, Any]]:
        """Get metric history for visualization"""
        
        if metric_name not in self.metrics:
            return []
        
        cutoff_time = datetime.now() - timedelta(minutes=time_window_minutes)
        
        history = []
        for mv in self.metrics[metric_name]:
            if mv.timestamp > cutoff_time:
                history.append({
                    'timestamp': mv.timestamp.isoformat(),
                    'value': mv.value,
                    'labels': mv.labels
                })
        
        return history

# Global performance monitor instance
performance_monitor = PerformanceMonitor()

class PerformanceTimer:
    """Context manager for timing operations"""
    
    def __init__(self, metric_name: str, monitor: PerformanceMonitor = None):
        self.metric_name = metric_name
        self.monitor = monitor or performance_monitor
        self.start_time = None
    
    async def __aenter__(self):
        self.start_time = time.time()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration_ms = (time.time() - self.start_time) * 1000
            await self.monitor.record_metric(self.metric_name, duration_ms, MetricType.TIMER)

def performance_timer(metric_name: str):
    """Decorator for timing function execution"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            async with PerformanceTimer(metric_name):
                return await func(*args, **kwargs)
        return wrapper
    return decorator
```

**Validation:**
```python
# Test performance monitoring system
from python.helpers.performance_monitoring import performance_monitor, PerformanceTimer, performance_timer

# Start monitoring
await performance_monitor.start_monitoring()

# Record some test metrics
await performance_monitor.record_metric('test_response_time', 45.5, MetricType.TIMER)
await performance_monitor.record_metric('test_cache_hit_rate', 0.92, MetricType.GAUGE)

# Use performance timer
async with PerformanceTimer('database_query_ms'):
    await asyncio.sleep(0.1)  # Simulate database query

# Use decorator
@performance_timer('api_request_ms')
async def test_api_call():
    await asyncio.sleep(0.05)  # Simulate API call

await test_api_call()

# Get performance summary
summary = performance_monitor.get_performance_summary()
print(f"Monitoring {summary['metrics_tracked']} metrics")
print(f"Active alerts: {summary['active_alerts']}")

# Get metric statistics
stats = performance_monitor.get_metric_statistics('test_response_time')
print(f"Response time stats: {stats}")
```

---

**Next Step**: [Auto-optimization](auto-optimization.md)
