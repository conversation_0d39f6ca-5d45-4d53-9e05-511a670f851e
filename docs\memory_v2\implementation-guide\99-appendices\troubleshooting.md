# Troubleshooting Guide
## Common Issues and Solutions

This document provides solutions to common issues encountered during implementation and operation of the intelligent data classification system.

## 🚨 Installation and Setup Issues

### Python Environment Problems

**Issue**: `ImportError: No module named 'sklearn'`
```bash
# Solution: Install missing dependencies
pip install scikit-learn>=1.3.0

# If using conda
conda install scikit-learn
```

**Issue**: `ModuleNotFoundError: No module named 'python.helpers'`
```bash
# Solution: Ensure you're running from project root and PYTHONPATH is set
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Or add to your .bashrc/.zshrc
echo 'export PYTHONPATH="${PYTHONPATH}:/path/to/agent-zero-v2"' >> ~/.bashrc
```

**Issue**: Virtual environment not activating
```bash
# Windows
agent-zero-env\Scripts\activate.bat

# If that fails, try
python -m venv agent-zero-env --clear
agent-zero-env\Scripts\activate

# Linux/Mac
source agent-zero-env/bin/activate

# If that fails
python3 -m venv agent-zero-env
source agent-zero-env/bin/activate
```

### Dependency Conflicts

**Issue**: `pip check` shows dependency conflicts
```bash
# Solution 1: Clean install
pip uninstall -y numpy scipy scikit-learn
pip install --no-cache-dir numpy>=1.24.0 scipy>=1.10.0 scikit-learn>=1.3.0

# Solution 2: Use specific versions
pip install numpy==1.24.3 scipy==1.10.1 scikit-learn==1.3.0

# Solution 3: Create fresh environment
deactivate
rm -rf agent-zero-env
python -m venv agent-zero-env
source agent-zero-env/bin/activate
pip install -r requirements.txt
```

## 🔧 Configuration Issues

### Environment Variables Not Loading

**Issue**: Configuration values not being read from `.env` file
```python
# Debug: Check if .env file exists and is readable
import os
from pathlib import Path

env_path = Path('.env')
print(f"Env file exists: {env_path.exists()}")
print(f"Env file readable: {env_path.is_file()}")

# Check file contents
if env_path.exists():
    with open(env_path) as f:
        print("Env file contents:")
        print(f.read())

# Check if python-dotenv is installed
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("python-dotenv loaded successfully")
except ImportError:
    print("python-dotenv not installed")
    # Install it: pip install python-dotenv
```

**Solution**: Ensure `.env` file format is correct
```bash
# Correct format (no spaces around =)
OPENAI_API_KEY=your-key-here
DEBUG=true
LOG_LEVEL=INFO

# Incorrect format (spaces around =)
OPENAI_API_KEY = your-key-here  # Wrong!
DEBUG = true                    # Wrong!
```

### API Key Issues

**Issue**: `openai.AuthenticationError: Invalid API key`
```python
# Debug: Check API key format
import os
api_key = os.getenv('OPENAI_API_KEY')
print(f"API key length: {len(api_key) if api_key else 'None'}")
print(f"API key starts with 'sk-': {api_key.startswith('sk-') if api_key else False}")

# Test API key
import openai
try:
    client = openai.OpenAI(api_key=api_key)
    # Test with a simple request
    response = client.models.list()
    print("API key is valid")
except Exception as e:
    print(f"API key error: {e}")
```

## 🧠 Classification Engine Issues

### Poor Classification Accuracy

**Issue**: Classification results are inconsistent or inaccurate

**Diagnosis**:
```python
# Check classification metrics
from python.helpers.intelligent_classification_engine import IntelligentClassificationEngine
from python.helpers.classification_config import classification_config

engine = IntelligentClassificationEngine(classification_config)
await engine.initialize()

# Test with known content
test_cases = [
    ("def hello(): print('world')", "Should be programming"),
    ("user: hello\nassistant: hi", "Should be conversation"),
    ("Patient has fever and cough", "Should be medical")
]

for content, expected in test_cases:
    result = await engine.classify_content(content, {})
    print(f"Content: {content[:30]}...")
    print(f"Expected: {expected}")
    print(f"Got: {result.strategy.value} (confidence: {result.confidence:.2f})")
    print(f"Reasoning: {result.reasoning}")
    print("---")
```

**Solutions**:
1. **Adjust thresholds**:
```python
# Lower confidence thresholds if classifications are too conservative
config.thresholds.entity_confidence_min = 0.6  # Default: 0.7
config.thresholds.semantic_overlap_shared = 0.6  # Default: 0.7

# Increase domain specificity threshold for better domain detection
config.thresholds.domain_specificity_threshold = 0.5  # Default: 0.4
```

2. **Add domain keywords**:
```python
# Extend domain keywords in intelligent_classification_engine.py
domain_keywords['programming'].extend([
    'import', 'export', 'const', 'let', 'var', 'def', 'class',
    'interface', 'struct', 'enum', 'namespace'
])
```

3. **Improve entity extraction**:
```python
# Add custom entity patterns
custom_patterns = {
    'PROGRAMMING_CONCEPT': r'\b(function|method|class|variable|array|object)\b',
    'MEDICAL_TERM': r'\b(patient|diagnosis|treatment|symptom|medication)\b'
}
```

### Slow Classification Performance

**Issue**: Classification taking longer than target times

**Diagnosis**:
```python
# Check performance metrics
engine_status = await engine.get_engine_status()
perf_summary = engine_status['performance_summary']

print(f"Average response time: {perf_summary['average_response_time_ms']:.2f}ms")
print(f"95th percentile: {perf_summary['response_time_p95_ms']:.2f}ms")
print(f"Cache hit rate: {perf_summary['cache_hit_rate']:.2%}")

# Check for bottlenecks
import time
start = time.time()
entities = await engine.extract_entities("test content")
entity_time = (time.time() - start) * 1000
print(f"Entity extraction time: {entity_time:.2f}ms")
```

**Solutions**:
1. **Enable caching**:
```python
# Ensure caching is enabled
config.cache.enabled = True
config.cache.ttl_seconds = 3600  # 1 hour cache
```

2. **Optimize entity extraction**:
```python
# Reduce entity extraction complexity
engine.batch_entity_extraction = True
engine.parallel_processing = True

# Limit content length for processing
MAX_CONTENT_LENGTH = 10000
if len(content) > MAX_CONTENT_LENGTH:
    content = content[:MAX_CONTENT_LENGTH] + "..."
```

3. **Use batch processing**:
```python
# Process multiple items together
requests = [ClassificationRequest(content=c, metadata={}) for c in content_list]
results = await engine.classify_batch(requests)
```

## 💾 Memory System Issues

### Memory Tier Performance Problems

**Issue**: Memory queries exceeding target response times

**Diagnosis**:
```python
# Check memory tier statistics
from python.helpers.memory_tiers import WorkingMemoryTier

working_memory = WorkingMemoryTier()
stats = await working_memory.get_tier_stats()

print(f"Average response time: {stats['avg_response_time_ms']:.2f}ms")
print(f"Hit rate: {stats['hit_rate']:.2%}")
print(f"Capacity utilization: {stats['capacity_utilization']:.2%}")
```

**Solutions**:
1. **Optimize working memory size**:
```python
# Reduce working memory size if performance is poor
working_memory = WorkingMemoryTier(max_items=25000)  # Default: 50000

# Or increase if you have more RAM
working_memory = WorkingMemoryTier(max_items=100000)
```

2. **Improve indexing**:
```python
# Add more specific indexing for common queries
# In working memory, create specialized indices
self.tag_index = defaultdict(set)  # tag -> item_ids
self.content_type_index = defaultdict(set)  # type -> item_ids
```

3. **Optimize query patterns**:
```python
# Use more specific queries
query = MemoryQuery(
    query_text="specific search terms",
    importance_threshold=0.5,  # Filter low-importance items
    limit=5  # Reduce result set size
)
```

### Memory Leaks

**Issue**: Memory usage continuously increasing

**Diagnosis**:
```python
import psutil
import gc

# Monitor memory usage
process = psutil.Process()
print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")

# Check for large objects
import sys
large_objects = []
for obj in gc.get_objects():
    if sys.getsizeof(obj) > 1024 * 1024:  # > 1MB
        large_objects.append((type(obj), sys.getsizeof(obj)))

print("Large objects:")
for obj_type, size in sorted(large_objects, key=lambda x: x[1], reverse=True)[:10]:
    print(f"  {obj_type}: {size / 1024 / 1024:.2f} MB")
```

**Solutions**:
1. **Enable cleanup**:
```python
# Ensure cleanup is running
await working_memory.cleanup_expired()
await long_term_memory.cleanup_expired()

# Set up periodic cleanup
import asyncio

async def periodic_cleanup():
    while True:
        await asyncio.sleep(3600)  # Every hour
        await working_memory.cleanup_expired()
        await long_term_memory.cleanup_expired()

asyncio.create_task(periodic_cleanup())
```

2. **Limit cache sizes**:
```python
# Reduce cache sizes
engine.entity_cache = {}  # Clear cache
engine.classification_cache = {}

# Set smaller cache limits
MAX_CACHE_SIZE = 1000
if len(engine.entity_cache) > MAX_CACHE_SIZE:
    # Remove oldest entries
    oldest_keys = list(engine.entity_cache.keys())[:100]
    for key in oldest_keys:
        del engine.entity_cache[key]
```

## 🔒 Security and Privacy Issues

### Privacy Detection Problems

**Issue**: Privacy-sensitive content not being properly isolated

**Diagnosis**:
```python
# Test privacy detection
test_content = "My SSN is *********** and password is secret123"
result = await engine.classify_content(test_content, {})

print(f"Privacy score: {result.metrics.privacy_score}")
print(f"Privacy flags: {result.metrics.privacy_flags}")
print(f"Strategy: {result.strategy.value}")

# Should be ISOLATED_NAMESPACE for high privacy content
```

**Solutions**:
1. **Adjust privacy thresholds**:
```python
# Lower privacy threshold for more sensitive detection
config.thresholds.privacy_score_threshold = 2  # Default: 3
```

2. **Add privacy patterns**:
```python
# Add more privacy detection patterns
privacy_patterns = [
    r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
    r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b',  # Credit card
    r'password[:\s=]+\S+',  # Password
    r'api[_\s]?key[:\s=]+\S+',  # API key
]
```

### Data Boundary Violations

**Issue**: Data appearing in wrong systems

**Diagnosis**:
```python
# Check boundary enforcement
from python.helpers.boundary_enforcement import BoundaryEnforcementSystem

enforcement = BoundaryEnforcementSystem()
violation_summary = enforcement.get_violation_summary()

print(f"Total violations: {violation_summary['total_violations']}")
print(f"By type: {violation_summary['by_type']}")
print(f"By severity: {violation_summary['by_severity']}")
```

**Solutions**:
1. **Enable strict validation**:
```python
# Enable all validation rules
enforcement.validation_rules['no_duplication'] = True
enforcement.validation_rules['complete_coverage'] = True
enforcement.validation_rules['domain_consistency'] = True
```

2. **Add custom validation**:
```python
# Add custom boundary checks
async def custom_boundary_check(data_items):
    for item in data_items:
        if item.data_domain == DataDomain.HISTORY:
            # Ensure no knowledge content in history
            if 'entity:' in str(item.content):
                return False
    return True
```

## 📊 Performance Monitoring Issues

### Monitoring Not Working

**Issue**: Performance metrics not being collected

**Diagnosis**:
```python
# Check monitoring status
from python.helpers.performance_monitoring import performance_monitor

summary = performance_monitor.get_performance_summary()
print(f"Monitoring active: {summary['monitoring_active']}")
print(f"Metrics tracked: {summary['metrics_tracked']}")
```

**Solutions**:
1. **Start monitoring**:
```python
# Ensure monitoring is started
await performance_monitor.start_monitoring()

# Check if it's running
print(f"Monitoring active: {performance_monitor.monitoring_active}")
```

2. **Fix metric recording**:
```python
# Manually record metrics to test
await performance_monitor.record_metric('test_metric', 42.0, MetricType.GAUGE)

# Check if it was recorded
stats = performance_monitor.get_metric_statistics('test_metric')
print(f"Test metric stats: {stats}")
```

## 🧪 Testing Issues

### Tests Failing

**Issue**: Unit tests or integration tests failing

**Common Solutions**:
1. **Environment setup**:
```bash
# Ensure test environment is clean
export ENVIRONMENT=test
export DATABASE_URL=sqlite:///:memory:
export CACHE_ENABLED=false
```

2. **Mock external dependencies**:
```python
# Mock OpenAI API calls
@pytest.fixture
def mock_openai():
    with patch('openai.OpenAI') as mock:
        mock.return_value.embeddings.create.return_value = Mock(
            data=[Mock(embedding=[0.1] * 1536)]
        )
        yield mock
```

3. **Async test issues**:
```python
# Ensure proper async test setup
@pytest.mark.asyncio
async def test_async_function():
    result = await async_function()
    assert result is not None

# If tests hang, check event loop
@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
```

## 📞 Getting Additional Help

### Debug Information Collection

When reporting issues, collect this information:

```python
# System information
import sys
import platform
print(f"Python version: {sys.version}")
print(f"Platform: {platform.platform()}")

# Package versions
import pkg_resources
packages = ['numpy', 'scipy', 'scikit-learn', 'networkx']
for package in packages:
    try:
        version = pkg_resources.get_distribution(package).version
        print(f"{package}: {version}")
    except:
        print(f"{package}: not installed")

# Configuration summary
from python.helpers.classification_config import classification_config
print(f"Backend: {classification_config.backend_type}")
print(f"Cache enabled: {classification_config.cache.enabled}")
print(f"Debug mode: {classification_config.debug_mode}")

# Performance summary
if 'engine' in locals():
    status = await engine.get_engine_status()
    print(f"Engine initialized: {status['is_initialized']}")
    print(f"Error count: {status['error_count']}")
```

### Log Analysis

Enable detailed logging for debugging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable specific loggers
logging.getLogger('agent_zero.classification').setLevel(logging.DEBUG)
logging.getLogger('agent_zero.memory').setLevel(logging.DEBUG)
```

### Performance Profiling

For performance issues:

```python
import cProfile
import pstats

# Profile classification
profiler = cProfile.Profile()
profiler.enable()

# Run your code here
result = await engine.classify_content(content, metadata)

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(20)  # Top 20 functions
```

---

**Need more help?** Check the [API Reference](api-reference.md) or [Performance Tuning](performance-tuning.md) guides.
