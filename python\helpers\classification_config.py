"""
Enhanced configuration management for intelligent data classification system
"""

from dataclasses import dataclass, field, asdict
from typing import Dict, Any, Optional, List
from enum import Enum
import os
import json
import yaml # type: ignore
from pathlib import Path
from datetime import datetime

# Import env_loader directly - no fallbacks or mocks
# This enforces proper dependency management and prevents silent failures
from .env_loader import env_loader

def validate_dependencies() -> None:
    """
    Validate that all required dependencies are available.

    Raises:
        ImportError: If required dependencies are not available
        RuntimeError: If env_loader is not properly initialized
    """
    if env_loader is None:
        raise RuntimeError("env_loader is not properly initialized")

    # Validate that env_loader has the required methods
    required_methods = ['get_str', 'get_int', 'get_float', 'get_bool']
    for method in required_methods:
        if not hasattr(env_loader, method):
            raise RuntimeError(f"env_loader is missing required method: {method}")

# Validate dependencies on module import
validate_dependencies()


class ConfigurationSource(Enum):
    """Configuration source types"""
    DEFAULT = "default"
    ENVIRONMENT = "environment"
    FILE = "file"
    USER_PREFERENCE = "user_preference"
    RUNTIME = "runtime"

class ClassificationStrategy(Enum):
    SHARED_ONTOLOGY = "shared_ontology"
    CUSTOM_DOMAIN = "custom_domain"
    ISOLATED_NAMESPACE = "isolated_namespace"

@dataclass
class ClassificationThresholds:
    """Thresholds for classification decisions"""
    semantic_overlap_shared: float = 0.70
    semantic_overlap_custom: float = 0.30
    entity_confidence_min: float = 0.70
    domain_specificity_threshold: float = 0.40
    privacy_score_threshold: int = 3
    interconnectedness_threshold: float = 0.30
    min_related_entities: int = 5

    # Advanced thresholds
    temporal_relevance_threshold: float = 0.60
    relationship_density_threshold: float = 0.50
    user_isolation_override: bool = True
    confidence_decay_rate: float = 0.1

@dataclass
class PerformanceTargets:
    """Performance targets for the system"""
    working_memory_response_ms: int = 100
    long_term_memory_response_ms: int = 500
    episodic_memory_response_ms: int = 1000
    classification_decision_ms: int = 50
    cache_hit_rate_target: float = 0.85
    classification_accuracy_target: float = 0.90

    # Advanced performance targets
    batch_processing_timeout_ms: int = 5000
    concurrent_classification_limit: int = 10
    memory_usage_limit_mb: int = 1000
    disk_usage_limit_gb: int = 10

@dataclass
class CacheConfiguration:
    """Caching system configuration"""
    enabled: bool = True
    ttl_seconds: int = 3600
    max_size_mb: int = 500 # Matches .env example, not max_size from .md (100000)

    # Multi-level cache settings
    l1_memory_size: int = 10000
    l1_eviction_policy: str = "lru"
    l2_redis_enabled: bool = True
    l2_redis_url: str = "redis://localhost:6379/0" # Default, can be overridden by env
    l3_persistent_enabled: bool = True
    l3_persistent_dir: str = "cache"
    l3_max_size_mb: int = 1000

@dataclass
class MonitoringConfiguration:
    """Performance monitoring configuration"""
    enabled: bool = True
    collection_interval_seconds: float = 1.0
    retention_hours: int = 24
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'cpu_usage_percent': 80.0,
        'memory_usage_percent': 85.0,
        'error_rate_percent': 1.0,
        'response_time_p95_ms': 1000.0
    })

    # Advanced monitoring
    metrics_export_enabled: bool = False
    metrics_export_url: str = ""
    log_level: str = "INFO" # Default, can be overridden by env
    performance_profiling: bool = False

@dataclass
class SecurityConfiguration:
    """Security and privacy configuration"""
    encryption_enabled: bool = True
    encryption_key_rotation_days: int = 30
    audit_logging_enabled: bool = True

    # Privacy settings
    pii_detection_enabled: bool = True
    pii_auto_redaction: bool = False
    data_retention_days: int = 365
    secure_deletion_enabled: bool = True

    # Access control
    api_rate_limiting: bool = True
    api_rate_limit_per_minute: int = 1000
    authentication_required: bool = False

@dataclass
class ClassificationConfig:
    """Main configuration for classification system"""
    thresholds: ClassificationThresholds = field(default_factory=ClassificationThresholds)
    performance_targets: PerformanceTargets = field(default_factory=PerformanceTargets)
    cache: CacheConfiguration = field(default_factory=CacheConfiguration)
    monitoring: MonitoringConfiguration = field(default_factory=MonitoringConfiguration)
    security: SecurityConfiguration = field(default_factory=SecurityConfiguration)

    # Core system settings from .env
    backend_type: str = "faiss" # CLASSIFICATION_BACKEND
    working_memory_size: int = 50000 # MEMORY_WORKING_SIZE
    working_memory_days: int = 7 # MEMORY_WORKING_RETENTION_DAYS
    batch_size: int = 100 # MEMORY_BATCH_SIZE
    batch_timeout: int = 300 # MEMORY_BATCH_TIMEOUT

    # User preference settings from .env
    enable_adaptive_learning: bool = True # ADAPTIVE_LEARNING_ENABLED
    preference_learning_rate: float = 0.1 # PREFERENCE_LEARNING_RATE
    user_feedback_weight: float = 0.3 # Default, not in .env example

    # Advanced features
    experimental_features_enabled: bool = False # Default, not in .env
    debug_mode: bool = False # DEBUG_MODE (already in .env, will be loaded)
    verbose_logging: bool = False # Default, not in .env

    # Configuration metadata
    config_version: str = "1.0.0"
    last_updated: Optional[datetime] = None
    source_priority: List[ConfigurationSource] = field(default_factory=lambda: [
        ConfigurationSource.RUNTIME,
        ConfigurationSource.USER_PREFERENCE,
        ConfigurationSource.ENVIRONMENT,
        ConfigurationSource.FILE,
        ConfigurationSource.DEFAULT
    ])

    @classmethod
    def from_env(cls) -> 'ClassificationConfig':
        """Load configuration from environment variables using env_loader."""
        config = cls() # Initialize with defaults

        # Thresholds
        config.thresholds.semantic_overlap_shared = env_loader.get_float('CLASSIFICATION_SEMANTIC_OVERLAP_SHARED', config.thresholds.semantic_overlap_shared)
        config.thresholds.semantic_overlap_custom = env_loader.get_float('CLASSIFICATION_SEMANTIC_OVERLAP_CUSTOM', config.thresholds.semantic_overlap_custom)
        config.thresholds.entity_confidence_min = env_loader.get_float('CLASSIFICATION_ENTITY_CONFIDENCE_MIN', config.thresholds.entity_confidence_min)
        config.thresholds.privacy_score_threshold = env_loader.get_int('CLASSIFICATION_PRIVACY_THRESHOLD', config.thresholds.privacy_score_threshold)


        # Performance Targets
        config.performance_targets.working_memory_response_ms = env_loader.get_int('CLASSIFICATION_WORKING_MEMORY_TARGET_MS', config.performance_targets.working_memory_response_ms)
        config.performance_targets.long_term_memory_response_ms = env_loader.get_int('CLASSIFICATION_LONG_TERM_MEMORY_TARGET_MS', config.performance_targets.long_term_memory_response_ms)
        config.performance_targets.episodic_memory_response_ms = env_loader.get_int('CLASSIFICATION_EPISODIC_MEMORY_TARGET_MS', config.performance_targets.episodic_memory_response_ms)
        config.performance_targets.classification_decision_ms = env_loader.get_int('CLASSIFICATION_DECISION_TARGET_MS', config.performance_targets.classification_decision_ms)

        # Cache Configuration
        config.cache.enabled = env_loader.get_bool('CACHE_ENABLED', config.cache.enabled)
        config.cache.ttl_seconds = env_loader.get_int('CACHE_TTL_SECONDS', config.cache.ttl_seconds)
        config.cache.max_size_mb = env_loader.get_int('CACHE_MAX_SIZE', config.cache.max_size_mb) # .env uses CACHE_MAX_SIZE
        redis_url = env_loader.get_str('REDIS_URL', config.cache.l2_redis_url)
        if redis_url is not None:
            config.cache.l2_redis_url = redis_url

        # Monitoring Configuration
        config.monitoring.enabled = env_loader.get_bool('PERFORMANCE_MONITORING', config.monitoring.enabled)
        log_level = env_loader.get_str('LOG_LEVEL', config.monitoring.log_level)
        if log_level is not None:
            config.monitoring.log_level = log_level

        # Core System Settings
        backend_type = env_loader.get_str('CLASSIFICATION_BACKEND', config.backend_type)
        if backend_type is not None:
            config.backend_type = backend_type
        config.working_memory_size = env_loader.get_int('MEMORY_WORKING_SIZE', config.working_memory_size)
        config.working_memory_days = env_loader.get_int('MEMORY_WORKING_RETENTION_DAYS', config.working_memory_days)
        config.batch_size = env_loader.get_int('MEMORY_BATCH_SIZE', config.batch_size)
        config.batch_timeout = env_loader.get_int('MEMORY_BATCH_TIMEOUT', config.batch_timeout)

        # User Preference Settings
        config.enable_adaptive_learning = env_loader.get_bool('ADAPTIVE_LEARNING_ENABLED', config.enable_adaptive_learning)
        config.preference_learning_rate = env_loader.get_float('PREFERENCE_LEARNING_RATE', config.preference_learning_rate)

        # Development Settings
        config.debug_mode = env_loader.get_bool('DEBUG_MODE', config.debug_mode)
        # verbose_logging is not in the .env example, so it will retain its default unless set by other means.

        config.last_updated = datetime.now()
        return config

    @classmethod
    def from_file(cls, file_path: str) -> 'ClassificationConfig':
        """Load configuration from file (JSON or YAML)"""
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")

        with open(path, 'r', encoding='utf-8') as f:
            if path.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            elif path.suffix.lower() == '.json':
                data = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {path.suffix}")

        config = cls() # Start with defaults
        if data: # Ensure data is not None
            config._update_from_dict(data)
        config.last_updated = datetime.now()
        return config

    def _update_from_dict(self, data: Dict[str, Any]):
        """Recursively update configuration from dictionary."""
        for key, value in data.items():
            if hasattr(self, key):
                attr = getattr(self, key)
                if isinstance(attr, dict) and isinstance(value, dict): # For alert_thresholds
                    attr.update(value)
                elif isinstance(getattr(self, key), object) and not isinstance(getattr(self, key), (list, dict, str, int, float, bool, type(None))) and isinstance(value, dict):
                    # This handles nested dataclasses. Iterate through the dict 'value'
                    # and set attributes on the existing dataclass instance 'attr'.
                    nested_obj = getattr(self, key)
                    for v_key, v_value in value.items():
                        if hasattr(nested_obj, v_key):
                            setattr(nested_obj, v_key, v_value)
                elif isinstance(attr, list) and isinstance(value, list): # For source_priority
                    # Handle enum conversion if necessary
                    if key == "source_priority":
                        try:
                            setattr(self, key, [ConfigurationSource(item) for item in value])
                        except ValueError:
                             # Fallback or log error if invalid enum value
                            pass # Keep default or handle error
                    else:
                        setattr(self, key, value)
                else: # Direct attribute
                    setattr(self, key, value)

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return asdict(self)

    def to_file(self, file_path: str, format: str = 'json'):
        """Save configuration to file"""
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        data = self.to_dict()

        # Convert datetime to string if present
        if data.get('last_updated') and isinstance(data['last_updated'], datetime):
            data['last_updated'] = data['last_updated'].isoformat()

        # Convert enums to strings
        if 'source_priority' in data and data['source_priority']:
             data['source_priority'] = [source.value if isinstance(source, Enum) else source for source in data['source_priority']]


        with open(path, 'w', encoding='utf-8') as f:
            if format.lower() == 'yaml':
                yaml.dump(data, f, default_flow_style=False, indent=2)
            elif format.lower() == 'json':
                json.dump(data, f, indent=2)
            else:
                raise ValueError(f"Unsupported output file format: {format}")

    def update_from_user_preferences(self, preferences: Dict[str, Any]):
        """Update configuration from user preferences"""
        preference_mapping = {
            'classification_confidence_threshold': ('thresholds', 'entity_confidence_min'),
            'semantic_overlap_shared': ('thresholds', 'semantic_overlap_shared'),
            'semantic_overlap_custom': ('thresholds', 'semantic_overlap_custom'),
            'working_memory_target_ms': ('performance_targets', 'working_memory_response_ms'),
            'cache_enabled': ('cache', 'enabled'),
            'cache_ttl': ('cache', 'ttl_seconds'),
            'monitoring_enabled': ('monitoring', 'enabled'),
            'adaptive_learning': ('enable_adaptive_learning', None), # Direct attribute
            'learning_rate': ('preference_learning_rate', None)    # Direct attribute
        }
        for pref_key, (config_attr_name, nested_key) in preference_mapping.items():
            if pref_key in preferences:
                value = preferences[pref_key]
                if nested_key is None: # Direct attribute of ClassificationConfig
                    if hasattr(self, config_attr_name):
                        setattr(self, config_attr_name, value)
                else: # Nested attribute
                    if hasattr(self, config_attr_name):
                        nested_obj = getattr(self, config_attr_name)
                        if hasattr(nested_obj, nested_key):
                            setattr(nested_obj, nested_key, value)
        self.last_updated = datetime.now()

    def validate(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        if not (0.0 <= self.thresholds.semantic_overlap_shared <= 1.0):
            issues.append("semantic_overlap_shared must be between 0.0 and 1.0")
        if not (0.0 <= self.thresholds.semantic_overlap_custom <= 1.0):
            issues.append("semantic_overlap_custom must be between 0.0 and 1.0")
        if not (0.0 <= self.thresholds.entity_confidence_min <= 1.0):
            issues.append("entity_confidence_min must be between 0.0 and 1.0")
        if self.performance_targets.working_memory_response_ms <= 0:
            issues.append("working_memory_response_ms must be positive")
        if self.performance_targets.classification_decision_ms <= 0:
            issues.append("classification_decision_ms must be positive")
        if self.cache.enabled and self.cache.ttl_seconds <= 0:
            issues.append("cache ttl_seconds must be positive if cache is enabled")
        if self.cache.enabled and self.cache.max_size_mb <= 0: # Corrected from documentation's max_size_mb to max_size_mb
            issues.append("cache max_size_mb must be positive if cache is enabled")
        if self.working_memory_size <= 0:
            issues.append("working_memory_size must be positive")
        if self.batch_size <= 0:
            issues.append("batch_size must be positive")
        if not (0.0 <= self.preference_learning_rate <= 1.0):
            issues.append("preference_learning_rate must be between 0.0 and 1.0")
        return issues

    def get_effective_value(self, key_path: str) -> Any:
        """Get effective configuration value (simplified for direct attribute access for now)"""
        # This is a simplified version. A full implementation would respect source_priority.
        parts = key_path.split('.')
        try:
            value = self
            for part in parts:
                if isinstance(value, dict): # For dicts like alert_thresholds
                    value = value.get(part)
                else: # For dataclass attributes
                    value = getattr(value, part)
                if value is None: return None # Path doesn't fully exist
            return value
        except AttributeError:
            return None

    def override_runtime(self, overrides: Dict[str, Any]):
        """Apply runtime configuration overrides"""
        self._update_from_dict(overrides) # Use the existing recursive update logic
        self.last_updated = datetime.now()


class ConfigurationManager:
    """Manages configuration loading, validation, and updates"""
    def __init__(self, config_file: Optional[str] = None, load_on_init: bool = True):
        self.config_file: Optional[Path] = Path(config_file) if config_file else None
        self.user_config_dir: Path = Path(os.getenv("XDG_CONFIG_HOME") or Path.home() / ".config") / "memory_v2"
        self.user_config_file: Path = self.user_config_dir / "user_preferences.json"
        self.config: ClassificationConfig = ClassificationConfig() # Start with defaults
        self.config_history: List[ClassificationConfig] = []

        if load_on_init:
            self.load_configuration()

    def load_configuration(self) -> ClassificationConfig:
        """Load configuration according to source_priority."""
        # Order: Default (already set) -> File -> Env -> User Prefs -> Runtime (applied separately)

        loaded_config = ClassificationConfig() # Start with defaults

        # 1. Load from main config file (if specified and exists)
        if self.config_file and self.config_file.exists():
            try:
                file_config = ClassificationConfig.from_file(str(self.config_file))
                loaded_config._update_from_dict(file_config.to_dict())
            except Exception as e:
                print(f"Warning: Failed to load main config file {self.config_file}: {e}")

        # 2. Override with environment variables
        # from_env already starts with defaults and then applies env vars.
        # So, we create a fresh env_config and use its values to update loaded_config.
        env_config_values = ClassificationConfig.from_env().to_dict()
        loaded_config._update_from_dict(env_config_values)


        # 3. Load user preferences (if file exists)
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, 'r') as f:
                    user_prefs_data = json.load(f)
                loaded_config.update_from_user_preferences(user_prefs_data) # Apply mapped preferences
            except Exception as e:
                print(f"Warning: Failed to load user preferences from {self.user_config_file}: {e}")

        # Validate final configuration
        issues = loaded_config.validate()
        if issues:
            # Use logging if available, otherwise print
            log_func = getattr(env_loader, "get_logger", print) if hasattr(env_loader, "get_logger") else print
            log_func(f"WARNING: Configuration validation issues: {', '.join(issues)}")

        self.config = loaded_config
        self.config.last_updated = datetime.now()
        return self.config


    def save_configuration(self, file_path: Optional[str] = None):
        """Save current configuration to the main config file."""
        save_target_path = Path(file_path) if file_path else self.config_file
        if not save_target_path:
            raise ValueError("No file path specified for saving configuration.")
        self.config.to_file(str(save_target_path))
        print(f"Configuration saved to {save_target_path}")

    def save_user_preferences(self, preferences: Dict[str, Any]):
        """Save user preferences to the user-specific config file."""
        self.user_config_dir.mkdir(parents=True, exist_ok=True)
        try:
            with open(self.user_config_file, 'w') as f:
                json.dump(preferences, f, indent=2)
            # Optionally, reload config to apply these preferences immediately
            # self.load_configuration()
            print(f"User preferences saved to {self.user_config_file}")
        except Exception as e:
            print(f"Error saving user preferences: {e}")


    def backup_configuration(self):
        """Create backup of current configuration state."""
        current_config_copy = ClassificationConfig()
        current_config_copy._update_from_dict(self.config.to_dict()) # Deep copy
        current_config_copy.last_updated = self.config.last_updated
        self.config_history.append(current_config_copy)
        if len(self.config_history) > 10: # Keep last 10 backups
            self.config_history.pop(0)

    def restore_configuration(self, backup_index: int = -1) -> bool:
        """Restore configuration from history."""
        if not self.config_history:
            print("No configuration history to restore from.")
            return False
        try:
            config_to_restore = self.config_history[backup_index]
            self.config = ClassificationConfig() # Reset to default
            self.config._update_from_dict(config_to_restore.to_dict()) # Apply backup
            self.config.last_updated = config_to_restore.last_updated
            print(f"Configuration restored from backup (index {backup_index}).")
            return True
        except IndexError:
            print(f"Error: Backup index {backup_index} is out of range.")
            return False

    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get summary of current configuration."""
        if not self.config: return {}
        return {
            'config_version': self.config.config_version,
            'last_updated': self.config.last_updated.isoformat() if self.config.last_updated else None,
            'backend_type': self.config.backend_type,
            'cache_enabled': self.config.cache.enabled,
            'monitoring_enabled': self.config.monitoring.enabled,
            'adaptive_learning': self.config.enable_adaptive_learning,
            'debug_mode': self.config.debug_mode,
            'validation_issues': self.config.validate() # Re-validate current state
        }

# Global configuration instances
# classification_config is loaded based on environment at import time.
classification_config = ClassificationConfig.from_env()

# config_manager can be initialized with a specific config file path if needed,
# e.g., config_manager = ConfigurationManager(config_file="config/app_config.json")
# For now, default initialization. It will load based on its internal logic.
config_manager = ConfigurationManager()

# Example of how to use it (typically in your application's main entry point):
# current_active_config = config_manager.load_configuration()
# print(f"Loaded backend type from manager: {current_active_config.backend_type}")

if __name__ == "__main__":
    print("--- classification_config (loaded from env at import) ---")
    print(f"Backend type: {classification_config.backend_type}")
    print(f"Cache enabled: {classification_config.cache.enabled}")
    print(f"Working memory target: {classification_config.performance_targets.working_memory_response_ms}ms")
    print(f"Debug mode: {classification_config.debug_mode}")
    print(f"Log level (monitoring): {classification_config.monitoring.log_level}")

    print("\n--- config_manager (loading sequence) ---")
    # Assuming .env is in project root, and this script is in python/helpers
    # The default env_loader path is just ".env"
    # Create a dummy config file for testing from_file logic
    test_config_dir = Path(__file__).parent.parent.parent / "temp_config"
    test_config_dir.mkdir(exist_ok=True)
    test_config_file = test_config_dir / "test_settings.json"

    sample_config_data = {
        "backend_type": "test_file_backend",
        "cache": {"enabled": False, "ttl_seconds": 120},
        "monitoring": {"log_level": "DEBUG_FILE"},
        "debug_mode": True # This should be overridden by .env if .env has DEBUG_MODE=False
    }
    with open(test_config_file, 'w') as f:
        json.dump(sample_config_data, f, indent=2)

    # Test manager with a file
    manager_with_file = ConfigurationManager(config_file=str(test_config_file))
    # load_configuration is called on init, or call explicitly:
    # loaded_conf_from_manager = manager_with_file.load_configuration()
    loaded_conf_from_manager = manager_with_file.config

    print(f"\nManager - Backend type: {loaded_conf_from_manager.backend_type} (Expected: .env value or 'test_file_backend' if .env not set)")
    print(f"Manager - Cache enabled: {loaded_conf_from_manager.cache.enabled} (Expected: .env value or False from file)")
    print(f"Manager - Cache TTL: {loaded_conf_from_manager.cache.ttl_seconds} (Expected: .env value or 120 from file)")
    print(f"Manager - Monitoring Log Level: {loaded_conf_from_manager.monitoring.log_level} (Expected: .env LOG_LEVEL or 'DEBUG_FILE')")
    print(f"Manager - Debug mode: {loaded_conf_from_manager.debug_mode} (Expected: .env DEBUG_MODE value)")


    summary = manager_with_file.get_configuration_summary()
    print(f"\nConfiguration summary from manager: {summary}")

    issues = loaded_conf_from_manager.validate()
    if issues:
        print(f"Configuration issues: {issues}")
    else:
        print("Configuration is valid.")

    # Cleanup dummy file
    # os.remove(test_config_file)
    # os.rmdir(test_config_dir)
    print(f"\nNote: A test config file was created at {test_config_file}. It can be manually deleted.")
