"""
Service Container for Memory System Dependency Injection

This module provides a lightweight dependency injection system specifically for the
intelligent classification and memory system components. It manages service registration,
discovery, and lifecycle for memory-related services only.

This is NOT intended to replace Agent Zero's core dynamic loading system.
"""

import asyncio
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar
from dataclasses import dataclass, field
from enum import Enum

# Import foundation layer dependencies
from .logging_config import get_logger

T = TypeVar('T')

class ServiceLifecycle(Enum):
    """Service lifecycle management options"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"

class ServiceState(Enum):
    """Service registration state"""
    REGISTERED = "registered"
    INITIALIZING = "initializing"
    INITIALIZED = "initialized"
    FAILED = "failed"

@dataclass
class ServiceRegistration:
    """Service registration information"""
    name: str
    service_type: Type
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    lifecycle: ServiceLifecycle = ServiceLifecycle.SINGLETON
    dependencies: List[str] = field(default_factory=list)
    state: ServiceState = ServiceState.REGISTERED
    initialization_error: Optional[Exception] = None

class ServiceContainer:
    """
    Central service container for dependency injection.
    
    Provides service registration, discovery, and lifecycle management
    with support for dependency resolution and validation.
    """
    
    def __init__(self):
        self._services: Dict[str, ServiceRegistration] = {}
        self._initialization_lock = asyncio.Lock()
        self._logger = get_logger(__name__)
        self._initialization_order: List[str] = []
        
    def register_service(
        self, 
        name: str, 
        service_type: Type[T], 
        instance: Optional[T] = None,
        lifecycle: ServiceLifecycle = ServiceLifecycle.SINGLETON
    ) -> 'ServiceContainer':
        """
        Register a service instance.
        
        Args:
            name: Service name for lookup
            service_type: Type of the service
            instance: Pre-created instance (optional)
            lifecycle: Service lifecycle management
            
        Returns:
            Self for method chaining
        """
        if name in self._services:
            self._logger.warning(f"Service '{name}' is already registered. Overwriting.")
            
        registration = ServiceRegistration(
            name=name,
            service_type=service_type,
            instance=instance,
            lifecycle=lifecycle,
            state=ServiceState.INITIALIZED if instance else ServiceState.REGISTERED
        )
        
        self._services[name] = registration
        self._logger.debug(f"Registered service '{name}' of type {service_type.__name__}")
        return self
        
    def register_factory(
        self, 
        name: str, 
        service_type: Type[T], 
        factory: Callable[..., T],
        dependencies: Optional[List[str]] = None,
        lifecycle: ServiceLifecycle = ServiceLifecycle.SINGLETON
    ) -> 'ServiceContainer':
        """
        Register a service factory.
        
        Args:
            name: Service name for lookup
            service_type: Type of the service
            factory: Factory function to create the service
            dependencies: List of dependency service names
            lifecycle: Service lifecycle management
            
        Returns:
            Self for method chaining
        """
        if name in self._services:
            self._logger.warning(f"Service '{name}' is already registered. Overwriting.")
            
        registration = ServiceRegistration(
            name=name,
            service_type=service_type,
            factory=factory,
            dependencies=dependencies or [],
            lifecycle=lifecycle
        )
        
        self._services[name] = registration
        self._logger.debug(f"Registered factory for service '{name}' of type {service_type.__name__}")
        return self
        
    def get_service(self, name: str) -> Any:
        """
        Get a service instance by name.
        
        Args:
            name: Service name
            
        Returns:
            Service instance
            
        Raises:
            ServiceNotFoundError: If service is not registered
            ServiceInitializationError: If service initialization fails
        """
        if name not in self._services:
            raise ServiceNotFoundError(f"Service '{name}' is not registered")
            
        registration = self._services[name]
        
        # Return existing instance for singleton services
        if registration.lifecycle == ServiceLifecycle.SINGLETON and registration.instance is not None:
            return registration.instance
            
        # Check for failed initialization
        if registration.state == ServiceState.FAILED:
            raise ServiceInitializationError(
                f"Service '{name}' failed to initialize: {registration.initialization_error}"
            )
            
        # Initialize service if needed
        if registration.instance is None or registration.lifecycle == ServiceLifecycle.TRANSIENT:
            instance = self._create_service_instance(registration)
            if registration.lifecycle == ServiceLifecycle.SINGLETON:
                registration.instance = instance
            return instance

        return registration.instance
        
    def _create_service_instance(self, registration: ServiceRegistration) -> Any:
        """Create a service instance from registration."""
        try:
            registration.state = ServiceState.INITIALIZING
            
            if registration.factory is None:
                # Try to create instance using default constructor
                instance = registration.service_type()
            else:
                # Resolve dependencies
                dependencies = {}
                for dep_name in registration.dependencies:
                    dependencies[dep_name] = self.get_service(dep_name)
                    
                # Call factory with dependencies
                if dependencies:
                    instance = registration.factory(**dependencies)
                else:
                    instance = registration.factory()
                    
            registration.state = ServiceState.INITIALIZED
            self._logger.debug(f"Successfully initialized service '{registration.name}'")
            return instance
            
        except Exception as e:
            registration.state = ServiceState.FAILED
            registration.initialization_error = e
            self._logger.error(f"Failed to initialize service '{registration.name}': {e}")
            raise ServiceInitializationError(f"Failed to initialize service '{registration.name}': {e}") from e

    def has_service(self, name: str) -> bool:
        """Check if a service is registered."""
        return name in self._services

    def get_service_names(self) -> List[str]:
        """Get list of all registered service names."""
        return list(self._services.keys())

    def validate_dependencies(self) -> List[str]:
        """
        Validate all service dependencies.

        Returns:
            List of validation errors (empty if all valid)
        """
        errors = []

        for name, registration in self._services.items():
            for dep_name in registration.dependencies:
                if dep_name not in self._services:
                    errors.append(f"Service '{name}' depends on unregistered service '{dep_name}'")

        # Check for circular dependencies (simple check)
        visited = set()
        rec_stack = set()

        def has_cycle(service_name: str) -> bool:
            if service_name in rec_stack:
                return True
            if service_name in visited:
                return False

            visited.add(service_name)
            rec_stack.add(service_name)

            if service_name in self._services:
                for dep in self._services[service_name].dependencies:
                    if has_cycle(dep):
                        return True

            rec_stack.remove(service_name)
            return False

        for service_name in self._services:
            if service_name not in visited:
                if has_cycle(service_name):
                    errors.append(f"Circular dependency detected involving service '{service_name}'")

        return errors

class ServiceNotFoundError(Exception):
    """Raised when a requested service is not found."""
    pass

class ServiceInitializationError(Exception):
    """Raised when service initialization fails."""
    pass

class DependencyValidationError(Exception):
    """Raised when dependency validation fails."""
    pass

# Global service container instance for memory system
memory_service_container = ServiceContainer()
