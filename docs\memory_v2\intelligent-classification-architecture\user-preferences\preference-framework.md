# Adaptive User Preference Framework
## Configurable Importance Weighting with Behavioral Learning

This document outlines the adaptive user preference framework that enables personalized data classification and retrieval optimization based on user behavior and explicit preferences.

## 🎯 Framework Overview

The Adaptive User Preference Framework provides a multi-layered approach to understanding and adapting to user needs:

1. **Explicit Preferences**: User-configured settings and weights
2. **Behavioral Learning**: Automatic adaptation based on usage patterns
3. **Domain Specialization**: Context-aware preference adjustment
4. **Feedback Integration**: Learning from user corrections and feedback

## 🔧 Configurable Importance Weighting System

### Data Type Weights

**Default Weight Configuration**:
```yaml
data_type_weights:
  code:
    base_weight: 1.0
    description: "Source code, APIs, technical documentation"
    priority: "high"
    
  documentation:
    base_weight: 0.8
    description: "Technical docs, guides, specifications"
    priority: "medium-high"
    
  conversations:
    base_weight: 0.6
    description: "Chat history, discussions, Q&A"
    priority: "medium"
    
  general_knowledge:
    base_weight: 0.4
    description: "General facts, common knowledge"
    priority: "low-medium"
    
  experimental:
    base_weight: 0.3
    description: "Temporary, experimental content"
    priority: "low"
```

**Dynamic Weight Calculation**:
```python
class DataTypeWeightManager:
    def __init__(self, user_config):
        self.base_weights = user_config.get('data_type_weights', DEFAULT_WEIGHTS)
        self.learned_adjustments = {}
        self.context_multipliers = {}
    
    def calculate_effective_weight(self, data_type, context=None):
        """
        Calculate effective weight considering base weight, learned adjustments, and context
        """
        base_weight = self.base_weights[data_type]['base_weight']
        
        # Apply learned adjustments
        learned_adjustment = self.learned_adjustments.get(data_type, 0.0)
        
        # Apply context multipliers
        context_multiplier = 1.0
        if context and context in self.context_multipliers:
            context_multiplier = self.context_multipliers[context].get(data_type, 1.0)
        
        # Calculate effective weight
        effective_weight = base_weight * (1 + learned_adjustment) * context_multiplier
        
        # Ensure weight stays within reasonable bounds
        return max(0.1, min(2.0, effective_weight))
```

### Domain-Specific Multipliers

**Domain Weight Configuration**:
```python
class DomainWeightManager:
    def __init__(self):
        self.domain_multipliers = {
            'primary_domain': 2.0,    # User's main work domain
            'secondary_domains': 1.5,  # Related work domains
            'general_topics': 1.0,     # General knowledge
            'unrelated_domains': 0.7   # Less relevant domains
        }
        
        self.user_domains = {}  # User's domain preferences
        self.domain_detection = DomainDetectionEngine()
    
    def get_domain_multiplier(self, content, entities):
        """
        Calculate domain-specific multiplier for content
        """
        detected_domains = self.domain_detection.detect_domains(content, entities)
        
        max_multiplier = 1.0
        for domain in detected_domains:
            domain_category = self._categorize_domain(domain)
            multiplier = self.domain_multipliers.get(domain_category, 1.0)
            max_multiplier = max(max_multiplier, multiplier)
        
        return max_multiplier
    
    def _categorize_domain(self, domain):
        """
        Categorize domain based on user preferences
        """
        if domain in self.user_domains.get('primary', []):
            return 'primary_domain'
        elif domain in self.user_domains.get('secondary', []):
            return 'secondary_domains'
        elif domain in self.user_domains.get('unrelated', []):
            return 'unrelated_domains'
        else:
            return 'general_topics'
```

### Privacy Classification System

**Privacy Level Configuration**:
```python
class PrivacyClassificationManager:
    def __init__(self, user_privacy_settings):
        self.privacy_levels = {
            'public': {
                'weight_multiplier': 1.0,
                'sharing_allowed': True,
                'storage_location': 'shared_ontologies'
            },
            'internal': {
                'weight_multiplier': 1.2,  # Slightly higher priority
                'sharing_allowed': False,
                'storage_location': 'custom_ontologies'
            },
            'private': {
                'weight_multiplier': 1.5,  # High priority for user
                'sharing_allowed': False,
                'storage_location': 'isolated_namespaces'
            },
            'confidential': {
                'weight_multiplier': 2.0,  # Highest priority
                'sharing_allowed': False,
                'storage_location': 'isolated_namespaces',
                'encryption_required': True
            }
        }
        
        self.user_privacy_settings = user_privacy_settings
    
    def classify_privacy_level(self, content, metadata):
        """
        Determine privacy level based on content analysis and user settings
        """
        # Check for explicit privacy markers
        if 'privacy_level' in metadata:
            return metadata['privacy_level']
        
        # Analyze content for privacy indicators
        privacy_score = self._analyze_privacy_indicators(content)
        
        # Apply user privacy sensitivity settings
        sensitivity = self.user_privacy_settings.get('sensitivity', 'medium')
        
        if sensitivity == 'high':
            privacy_score *= 1.5
        elif sensitivity == 'low':
            privacy_score *= 0.7
        
        # Map score to privacy level
        if privacy_score >= 0.8:
            return 'confidential'
        elif privacy_score >= 0.6:
            return 'private'
        elif privacy_score >= 0.3:
            return 'internal'
        else:
            return 'public'
```

## 🧠 Behavioral Learning Algorithms

### Query Pattern Analysis

**Pattern Learning System**:
```python
class QueryPatternLearner:
    def __init__(self):
        self.query_history = []
        self.pattern_weights = {}
        self.temporal_patterns = {}
        self.context_patterns = {}
    
    def learn_from_query(self, query, results, user_interactions):
        """
        Learn from user query and interaction patterns
        """
        # Record query with timestamp and context
        query_record = {
            'query': query,
            'timestamp': datetime.now(),
            'results': results,
            'user_interactions': user_interactions,
            'context': self._extract_context()
        }
        
        self.query_history.append(query_record)
        
        # Update pattern weights based on user interactions
        self._update_pattern_weights(query_record)
        
        # Learn temporal patterns
        self._learn_temporal_patterns(query_record)
        
        # Learn context patterns
        self._learn_context_patterns(query_record)
    
    def _update_pattern_weights(self, query_record):
        """
        Update pattern weights based on user interaction quality
        """
        for result in query_record['results']:
            interaction_score = self._calculate_interaction_score(
                result, query_record['user_interactions']
            )
            
            # Extract patterns from high-quality interactions
            if interaction_score > 0.7:
                patterns = self._extract_patterns(result)
                for pattern in patterns:
                    self.pattern_weights[pattern] = (
                        self.pattern_weights.get(pattern, 0.5) * 0.9 + 
                        interaction_score * 0.1
                    )
```

### Domain Preference Detection

**Automatic Domain Learning**:
```python
class DomainPreferenceLearner:
    def __init__(self):
        self.domain_interactions = {}
        self.domain_success_rates = {}
        self.domain_time_spent = {}
    
    def track_domain_interaction(self, domain, interaction_type, duration, success):
        """
        Track user interactions with different domains
        """
        if domain not in self.domain_interactions:
            self.domain_interactions[domain] = []
        
        interaction = {
            'type': interaction_type,
            'duration': duration,
            'success': success,
            'timestamp': datetime.now()
        }
        
        self.domain_interactions[domain].append(interaction)
        
        # Update success rates
        self._update_domain_success_rate(domain, success)
        
        # Update time spent
        self._update_domain_time_spent(domain, duration)
    
    def get_learned_domain_preferences(self):
        """
        Calculate learned domain preferences based on interaction history
        """
        domain_scores = {}
        
        for domain in self.domain_interactions:
            # Calculate composite score
            success_rate = self.domain_success_rates.get(domain, 0.5)
            time_spent = self.domain_time_spent.get(domain, 0)
            interaction_frequency = len(self.domain_interactions[domain])
            
            # Weighted combination
            domain_score = (
                success_rate * 0.4 +
                min(1.0, time_spent / 3600) * 0.3 +  # Normalize time to hours
                min(1.0, interaction_frequency / 100) * 0.3  # Normalize frequency
            )
            
            domain_scores[domain] = domain_score
        
        return domain_scores
```

### Feedback Integration System

**User Correction Learning**:
```python
class FeedbackLearningSystem:
    def __init__(self):
        self.correction_history = []
        self.classification_adjustments = {}
        self.threshold_adjustments = {}
    
    def process_user_correction(self, original_classification, corrected_classification, content_features):
        """
        Learn from user corrections to improve future classifications
        """
        correction = {
            'original': original_classification,
            'corrected': corrected_classification,
            'features': content_features,
            'timestamp': datetime.now()
        }
        
        self.correction_history.append(correction)
        
        # Analyze correction patterns
        self._analyze_correction_patterns()
        
        # Adjust classification thresholds
        self._adjust_classification_thresholds(correction)
        
        # Update feature weights
        self._update_feature_weights(correction)
    
    def _analyze_correction_patterns(self):
        """
        Identify patterns in user corrections
        """
        recent_corrections = [
            c for c in self.correction_history 
            if (datetime.now() - c['timestamp']).days <= 30
        ]
        
        # Group corrections by type
        correction_patterns = {}
        for correction in recent_corrections:
            pattern_key = f"{correction['original']} -> {correction['corrected']}"
            if pattern_key not in correction_patterns:
                correction_patterns[pattern_key] = []
            correction_patterns[pattern_key].append(correction)
        
        # Identify frequent correction patterns
        frequent_patterns = {
            pattern: corrections 
            for pattern, corrections in correction_patterns.items()
            if len(corrections) >= 3  # At least 3 occurrences
        }
        
        return frequent_patterns
```

## 🎛️ User Configuration Interface

### Configuration Schema

**User Preference Configuration**:
```yaml
user_preferences:
  # Basic Settings
  user_id: "user_123"
  profile_name: "Software Developer"
  
  # Data Type Preferences
  data_type_weights:
    code: 1.0
    documentation: 0.8
    conversations: 0.6
    general_knowledge: 0.4
  
  # Domain Preferences
  primary_domains:
    - "software_development"
    - "machine_learning"
  
  secondary_domains:
    - "data_science"
    - "web_development"
  
  # Privacy Settings
  privacy_sensitivity: "medium"  # low, medium, high
  default_privacy_level: "internal"
  auto_privacy_detection: true
  
  # Learning Settings
  behavioral_learning_enabled: true
  feedback_learning_enabled: true
  adaptation_rate: 0.1  # How quickly to adapt (0.0 - 1.0)
  
  # Performance Preferences
  response_time_priority: "balanced"  # speed, balanced, accuracy
  cache_preference: "aggressive"      # conservative, balanced, aggressive
  
  # Notification Settings
  classification_notifications: false
  performance_alerts: true
  learning_updates: true
```

### Dynamic Configuration Updates

**Real-time Preference Updates**:
```python
class PreferenceManager:
    def __init__(self, user_id):
        self.user_id = user_id
        self.preferences = self._load_user_preferences()
        self.learning_engine = BehavioralLearningEngine()
        self.change_listeners = []
    
    def update_preference(self, preference_path, new_value):
        """
        Update user preference with validation and propagation
        """
        # Validate new value
        if not self._validate_preference_value(preference_path, new_value):
            raise ValueError(f"Invalid value for preference: {preference_path}")
        
        # Update preference
        old_value = self._get_nested_preference(preference_path)
        self._set_nested_preference(preference_path, new_value)
        
        # Notify change listeners
        self._notify_preference_change(preference_path, old_value, new_value)
        
        # Persist changes
        self._save_user_preferences()
    
    def get_adaptive_preferences(self):
        """
        Get preferences adjusted by behavioral learning
        """
        base_preferences = self.preferences.copy()
        learned_adjustments = self.learning_engine.get_learned_adjustments()
        
        # Apply learned adjustments
        adapted_preferences = self._apply_learned_adjustments(
            base_preferences, learned_adjustments
        )
        
        return adapted_preferences
```

## 📊 Performance and Adaptation Metrics

### Learning Effectiveness Measurement

**Adaptation Success Metrics**:
```python
class AdaptationMetrics:
    def __init__(self):
        self.baseline_performance = {}
        self.current_performance = {}
        self.adaptation_history = []
    
    def measure_adaptation_effectiveness(self):
        """
        Measure how well the system is adapting to user preferences
        """
        metrics = {
            'query_satisfaction': self._calculate_query_satisfaction(),
            'classification_accuracy': self._calculate_classification_accuracy(),
            'response_time_improvement': self._calculate_response_time_improvement(),
            'user_correction_rate': self._calculate_correction_rate()
        }
        
        return metrics
    
    def _calculate_query_satisfaction(self):
        """
        Calculate user satisfaction with query results
        """
        recent_queries = self._get_recent_queries(days=30)
        
        satisfaction_scores = []
        for query in recent_queries:
            # Calculate satisfaction based on user interactions
            satisfaction = self._calculate_query_satisfaction_score(query)
            satisfaction_scores.append(satisfaction)
        
        return sum(satisfaction_scores) / len(satisfaction_scores) if satisfaction_scores else 0.0
```

---

*This adaptive user preference framework provides a comprehensive system for personalizing Agent Zero's behavior while continuously learning and improving from user interactions.*
