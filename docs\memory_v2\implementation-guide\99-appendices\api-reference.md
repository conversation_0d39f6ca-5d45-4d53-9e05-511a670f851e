# API Reference
## Complete API Documentation

This document provides comprehensive API reference for all components of the intelligent data classification system.

## 🧠 Classification Engine API

### IntelligentClassificationEngine

**Class**: `python.helpers.intelligent_classification_engine.IntelligentClassificationEngine`

#### Constructor
```python
IntelligentClassificationEngine(config: ClassificationConfig)
```

#### Methods

**`async initialize()`**
- **Description**: Initialize the classification engine
- **Returns**: `None`
- **Raises**: `ClassificationConfigurationError` if configuration is invalid

**`async classify_content(content: str, metadata: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ClassificationResult`**
- **Description**: Classify content and return classification result
- **Parameters**:
  - `content`: Text content to classify
  - `metadata`: Content metadata (type, source, etc.)
  - `context`: Optional additional context
- **Returns**: `ClassificationResult` object
- **Performance Target**: <50ms (99th percentile)

**`async extract_entities(content: str) -> List[Dict[str, Any]]`**
- **Description**: Extract entities from content
- **Parameters**:
  - `content`: Text content to analyze
- **Returns**: List of entity dictionaries with labels and confidence scores

**`async classify_batch(requests: List[ClassificationRequest]) -> List[ClassificationResult]`**
- **Description**: Classify multiple content items in batch
- **Parameters**:
  - `requests`: List of classification requests
- **Returns**: List of classification results

### ClassificationResult

**Class**: `python.helpers.classification_engine.ClassificationResult`

#### Properties
```python
strategy: ClassificationStrategy          # Classification strategy chosen
confidence: float                        # Confidence score (0.0-1.0)
metrics: ClassificationMetrics          # Detailed classification metrics
reasoning: str                          # Human-readable reasoning
processing_time_ms: float              # Processing time in milliseconds
namespace: Optional[str]                # Target namespace
domain: Optional[str]                   # Target domain
```

#### Methods

**`to_dict() -> Dict[str, Any]`**
- **Description**: Convert result to dictionary
- **Returns**: Dictionary representation

**`from_dict(data: Dict[str, Any]) -> ClassificationResult`**
- **Description**: Create result from dictionary
- **Parameters**: `data` - Dictionary data
- **Returns**: ClassificationResult instance

### ClassificationMetrics

**Class**: `python.helpers.classification_engine.ClassificationMetrics`

#### Properties
```python
semantic_overlap: float                 # Semantic overlap score (0.0-1.0)
entity_confidence: float               # Entity confidence score (0.0-1.0)
domain_specificity: float             # Domain specificity score (0.0-1.0)
relationship_density: float           # Relationship density score (0.0-1.0)
privacy_score: int                     # Privacy score (0-10)
privacy_flags: bool                    # Privacy flags detected
interconnectedness: float             # Interconnectedness score (0.0-1.0)
temporal_relevance: float             # Temporal relevance score (0.0-1.0)
related_entities_count: int           # Number of related entities
```

## 💾 Memory System API

### HierarchicalMemoryManager

**Class**: `python.helpers.hierarchical_memory_manager.HierarchicalMemoryManager`

#### Methods

**`async store_memory(content: str, metadata: Dict[str, Any], classification_result: ClassificationResult) -> str`**
- **Description**: Store memory in appropriate tier
- **Parameters**:
  - `content`: Memory content
  - `metadata`: Memory metadata
  - `classification_result`: Classification result
- **Returns**: Memory item ID

**`async retrieve_memories(query: MemoryQuery, strategy: str = "hierarchical") -> List[MemoryItem]`**
- **Description**: Retrieve memories using specified strategy
- **Parameters**:
  - `query`: Memory query object
  - `strategy`: Search strategy ("hierarchical", "parallel", "tier_specific")
- **Returns**: List of matching memory items
- **Performance Targets**:
  - Working Memory: <100ms
  - Long-term Memory: <500ms
  - Episodic Memory: <1s

**`async get_memory_hierarchy_stats() -> Dict[str, Any]`**
- **Description**: Get comprehensive hierarchy statistics
- **Returns**: Statistics dictionary

### MemoryItem

**Class**: `python.helpers.memory_tiers.MemoryItem`

#### Properties
```python
id: str                               # Unique identifier
content: Any                          # Memory content
metadata: Dict[str, Any]              # Memory metadata
tier: MemoryTier                      # Memory tier (WORKING, LONG_TERM, EPISODIC)
created_at: datetime                  # Creation timestamp
last_accessed: datetime              # Last access timestamp
access_count: int                     # Access count
importance_score: float               # Importance score (0.0-1.0)
```

### MemoryQuery

**Class**: `python.helpers.memory_tiers.MemoryQuery`

#### Constructor
```python
MemoryQuery(
    query_text: str,
    tier_preference: Optional[MemoryTier] = None,
    time_range: Optional[Tuple[datetime, datetime]] = None,
    importance_threshold: float = 0.0,
    limit: int = 10,
    include_metadata: bool = True
)
```

## 👤 User Preferences API

### UserPreferenceManager

**Class**: `python.helpers.user_preferences.UserPreferenceManager`

#### Constructor
```python
UserPreferenceManager(user_id: str)
```

#### Methods

**`async get_preference(preference_key: str, default_value: Any = None) -> Any`**
- **Description**: Get user preference value
- **Parameters**:
  - `preference_key`: Preference key
  - `default_value`: Default value if not found
- **Returns**: Preference value

**`async set_preference(preference_key: str, value: Any, preference_type: PreferenceType, user_explicit: bool = True) -> bool`**
- **Description**: Set user preference
- **Parameters**:
  - `preference_key`: Preference key
  - `value`: Preference value
  - `preference_type`: Type of preference
  - `user_explicit`: Whether explicitly set by user
- **Returns**: Success boolean

**`async record_user_feedback(action: str, context: Dict[str, Any], feedback_type: LearningSignal) -> None`**
- **Description**: Record user feedback for learning
- **Parameters**:
  - `action`: Action that triggered feedback
  - `context`: Context information
  - `feedback_type`: Type of feedback signal

**`async export_preferences() -> Dict[str, Any]`**
- **Description**: Export user preferences
- **Returns**: Preferences data dictionary

**`async import_preferences(preferences_data: Dict[str, Any]) -> bool`**
- **Description**: Import user preferences
- **Parameters**: `preferences_data` - Preferences data
- **Returns**: Success boolean

## 🔒 Data Separation API

### DataSeparationFramework

**Class**: `python.helpers.data_separation_framework.DataSeparationFramework`

#### Methods

**`async classify_data_domain(data: Any, metadata: Dict[str, Any]) -> DataDomain`**
- **Description**: Classify data into appropriate domain
- **Parameters**:
  - `data`: Data to classify
  - `metadata`: Data metadata
- **Returns**: DataDomain enum value

**`async separate_mixed_data(data: Any, metadata: Dict[str, Any]) -> SeparationResult`**
- **Description**: Separate mixed data into domains
- **Parameters**:
  - `data`: Mixed data to separate
  - `metadata`: Data metadata
- **Returns**: SeparationResult object

### BoundaryEnforcementSystem

**Class**: `python.helpers.boundary_enforcement.BoundaryEnforcementSystem`

#### Methods

**`async validate_access_request(request: AccessRequest) -> bool`**
- **Description**: Validate access request
- **Parameters**: `request` - Access request object
- **Returns**: Access allowed boolean

**`async check_data_boundaries(data_items: List[DataItem]) -> BoundaryCheckResult`**
- **Description**: Check data boundary compliance
- **Parameters**: `data_items` - List of data items to check
- **Returns**: BoundaryCheckResult object

## 📊 Performance Monitoring API

### PerformanceMonitor

**Class**: `python.helpers.performance_monitoring.PerformanceMonitor`

#### Methods

**`async start_monitoring()`**
- **Description**: Start performance monitoring
- **Returns**: None

**`async stop_monitoring()`**
- **Description**: Stop performance monitoring
- **Returns**: None

**`async record_metric(name: str, value: float, metric_type: MetricType, labels: Optional[Dict[str, str]] = None)`**
- **Description**: Record a performance metric
- **Parameters**:
  - `name`: Metric name
  - `value`: Metric value
  - `metric_type`: Type of metric
  - `labels`: Optional labels

**`get_performance_summary() -> Dict[str, Any]`**
- **Description**: Get performance summary
- **Returns**: Performance summary dictionary

### PerformanceTimer

**Class**: `python.helpers.performance_monitoring.PerformanceTimer`

#### Usage
```python
# Context manager usage
async with PerformanceTimer('operation_name'):
    # Your code here
    pass

# Decorator usage
@performance_timer('function_name')
async def my_function():
    pass
```

## 🗄️ Caching System API

### MemoryCache

**Class**: `python.helpers.caching_system.MemoryCache`

#### Constructor
```python
MemoryCache(max_size: int = 10000, policy: CachePolicy = CachePolicy.LRU)
```

#### Methods

**`async get(key: str) -> Optional[Any]`**
- **Description**: Get value from cache
- **Parameters**: `key` - Cache key
- **Returns**: Cached value or None

**`async set(key: str, value: Any, ttl_seconds: Optional[int] = None) -> bool`**
- **Description**: Set value in cache
- **Parameters**:
  - `key`: Cache key
  - `value`: Value to cache
  - `ttl_seconds`: Time to live in seconds
- **Returns**: Success boolean

**`async get_stats() -> CacheStats`**
- **Description**: Get cache statistics
- **Returns**: CacheStats object

## 🔧 Configuration API

### ClassificationConfig

**Class**: `python.helpers.classification_config.ClassificationConfig`

#### Class Methods

**`from_env() -> ClassificationConfig`**
- **Description**: Load configuration from environment variables
- **Returns**: Configuration instance

**`from_file(file_path: str) -> ClassificationConfig`**
- **Description**: Load configuration from file
- **Parameters**: `file_path` - Path to configuration file
- **Returns**: Configuration instance

#### Methods

**`to_dict() -> Dict[str, Any]`**
- **Description**: Convert configuration to dictionary
- **Returns**: Configuration dictionary

**`to_file(file_path: str, format: str = 'json')`**
- **Description**: Save configuration to file
- **Parameters**:
  - `file_path`: Output file path
  - `format`: File format ('json' or 'yaml')

**`validate() -> List[str]`**
- **Description**: Validate configuration
- **Returns**: List of validation issues

## 🚀 Enhanced Memory Abstraction API

### EnhancedMemoryAbstractionLayer

**Class**: `python.helpers.memory_abstraction.EnhancedMemoryAbstractionLayer`

#### Constructor
```python
EnhancedMemoryAbstractionLayer(user_id: str = "default_user")
```

#### Methods

**`async classify_and_route_content(content: str, content_type: str, metadata: Optional[Dict[str, Any]] = None) -> Tuple[str, ClassificationResult]`**
- **Description**: Classify content and route to appropriate storage
- **Parameters**:
  - `content`: Content to classify and store
  - `content_type`: Type of content
  - `metadata`: Optional metadata
- **Returns**: Tuple of (item_id, classification_result)

**`async search_memories(query: str, search_strategy: str = "hierarchical", filters: Optional[Dict[str, Any]] = None, limit: int = 10) -> List[MemoryItem]`**
- **Description**: Search across all memory tiers
- **Parameters**:
  - `query`: Search query
  - `search_strategy`: Search strategy
  - `filters`: Optional filters
  - `limit`: Maximum results
- **Returns**: List of memory items

**`async get_system_status() -> Dict[str, Any]`**
- **Description**: Get comprehensive system status
- **Returns**: System status dictionary

**`async get_performance_metrics() -> Dict[str, Any]`**
- **Description**: Get performance metrics
- **Returns**: Performance metrics dictionary

## 📝 Error Handling

### Exception Classes

**`ClassificationError`**
- Base exception for classification errors

**`ClassificationTimeoutError`**
- Exception for classification timeouts

**`ClassificationValidationError`**
- Exception for validation errors

**`ClassificationConfigurationError`**
- Exception for configuration errors

### Error Response Format

All API methods that can fail return structured error information:

```python
{
    "error": {
        "type": "ClassificationError",
        "message": "Human readable error message",
        "code": "ERROR_CODE",
        "details": {
            "additional": "error details"
        }
    }
}
```

## 🔄 Async Patterns

All API methods are async and should be called with `await`:

```python
# Correct usage
result = await engine.classify_content(content, metadata)

# Incorrect usage (will return coroutine object)
result = engine.classify_content(content, metadata)  # Wrong!
```

## 📊 Performance Targets

| Component | Target Response Time | Percentile |
|-----------|---------------------|------------|
| Classification Engine | <50ms | 99th |
| Working Memory | <100ms | 95th |
| Long-term Memory | <500ms | 95th |
| Episodic Memory | <1s | 90th |
| Cache Hit Rate | >85% | Average |

## 🔍 Debugging

Enable debug logging for detailed API call information:

```python
import logging
logging.getLogger('agent_zero').setLevel(logging.DEBUG)
```

---

**For more examples and usage patterns, see the [End-to-End Testing](../07-testing-validation/end-to-end-testing.md) documentation.**
