import asyncio
import uuid
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum # Added Enum

# Import ClassificationResult directly - no fallbacks or mocks
# This enforces proper dependency management and prevents silent failures
from .classification_engine import ClassificationResult
from .classification_config import ClassificationStrategy

from .memory_tiers import (
    MemoryTierInterface,
    WorkingMemoryTier,
    LongTermMemoryTier,
    EpisodicMemoryTier,
    MemoryItem,
    MemoryQuery,
    MemoryTier,
)

class HierarchicalMemoryManager:
    """
    Manages different tiers of memory (Working, Long-Term, Episodic)
    and orchestrates storage and retrieval operations across them.
    """

    def __init__(self):
        self.working_memory: MemoryTierInterface = WorkingMemoryTier()
        self.long_term_memory: MemoryTierInterface = LongTermMemoryTier()
        self.episodic_memory: MemoryTierInterface = EpisodicMemoryTier() # Stubbed for now
        self.is_initialized = False
        self.initialization_lock = asyncio.Lock()

    async def initialize(self):
        """
        Initializes the memory manager and its underlying tiers.
        Currently, tiers are initialized in their constructors, but this
        provides a hook for future async initialization needs.
        """
        async with self.initialization_lock:
            if self.is_initialized:
                return

            # In the future, if tiers have their own async init methods:
            # await self.working_memory.initialize()
            # await self.long_term_memory.initialize()
            # await self.episodic_memory.initialize()
            self.is_initialized = True
            print("HierarchicalMemoryManager initialized.")

    async def _ensure_initialized(self):
        if not self.is_initialized:
            await self.initialize()

    async def store_memory(
        self, content: str, metadata: Dict[str, Any], classification_result: ClassificationResult
    ) -> str:
        """
        Stores a memory item into an appropriate tier based on classification
        and other heuristics.

        Args:
            content: The content to store.
            metadata: Additional metadata for the content.
            classification_result: The result from the ClassificationEngine.

        Returns:
            The ID of the stored memory item.
        """
        await self._ensure_initialized()

        item_id = metadata.get("id", uuid.uuid4().hex) # Use provided ID or generate new
        now = datetime.now(timezone.utc)

        # Determine importance score - might come from classification or be a default
        importance = classification_result.importance_score if hasattr(classification_result, 'importance_score') and classification_result.importance_score is not None else classification_result.confidence
        if importance is None: # Fallback if confidence is also None
            importance = 0.5


        # Basic tier selection logic (can be significantly expanded)
        target_tier_enum: MemoryTier
        target_tier_instance: MemoryTierInterface

        if metadata.get("target_tier") == MemoryTier.WORKING.value or            (hasattr(classification_result, 'strategy') and hasattr(classification_result.strategy, 'value') and classification_result.strategy.value == ClassificationStrategy.CUSTOM_DOMAIN.value and importance > 0.7): # Example condition
            target_tier_enum = MemoryTier.WORKING
            target_tier_instance = self.working_memory
        elif metadata.get("target_tier") == MemoryTier.EPISODIC.value or metadata.get("is_event", False):
            target_tier_enum = MemoryTier.EPISODIC
            target_tier_instance = self.episodic_memory
        else:
            target_tier_enum = MemoryTier.LONG_TERM
            target_tier_instance = self.long_term_memory

        memory_item = MemoryItem(
            id=item_id,
            content=content,
            metadata=metadata,
            tier=target_tier_enum,
            created_at=now,
            last_accessed=now,
            access_count=1,
            importance_score=float(importance), # Ensure it's float
            tags=metadata.get("tags", []),
            relationships=metadata.get("relationships", [])
        )

        stored_item_id = await target_tier_instance.store(memory_item)
        return stored_item_id

    async def retrieve_memories(
        self, query: MemoryQuery, strategy: str = "hierarchical"
    ) -> List[MemoryItem]:
        """
        Retrieves memories based on the query, employing a specified strategy.

        Args:
            query: The MemoryQuery object.
            strategy: The retrieval strategy (e.g., "hierarchical", "working_only",
                      "long_term_only", "episodic_only").

        Returns:
            A list of matching MemoryItem objects.
        """
        await self._ensure_initialized()
        results: List[MemoryItem] = []
        seen_ids = set()

        if query.tier_preference:
            tier_to_query: Optional[MemoryTierInterface] = None
            if query.tier_preference == MemoryTier.WORKING:
                tier_to_query = self.working_memory
            elif query.tier_preference == MemoryTier.LONG_TERM:
                tier_to_query = self.long_term_memory
            elif query.tier_preference == MemoryTier.EPISODIC:
                tier_to_query = self.episodic_memory

            if tier_to_query:
                tier_results = await tier_to_query.retrieve(query)
                for item in tier_results:
                    if item.id not in seen_ids:
                        results.append(item)
                        seen_ids.add(item.id)
            return results[:query.limit]

        if strategy == "hierarchical":
            working_results = await self.working_memory.retrieve(query)
            for item in working_results:
                if item.id not in seen_ids:
                    results.append(item)
                    seen_ids.add(item.id)

            if len(results) < query.limit:
                remaining_limit = query.limit - len(results)
                ltm_query = query
                if remaining_limit > 0 :
                    long_term_results = await self.long_term_memory.retrieve(ltm_query)
                    for item in long_term_results:
                        if item.id not in seen_ids:
                            results.append(item)
                            seen_ids.add(item.id)

            if len(results) < query.limit:
                remaining_limit = query.limit - len(results)
                episodic_query = query
                if remaining_limit > 0 :
                    episodic_results = await self.episodic_memory.retrieve(episodic_query)
                    for item in episodic_results:
                        if item.id not in seen_ids:
                            results.append(item)
                            seen_ids.add(item.id)

        elif strategy == "working_only":
            results = await self.working_memory.retrieve(query)
        elif strategy == "long_term_only":
            results = await self.long_term_memory.retrieve(query)
        elif strategy == "episodic_only":
            results = await self.episodic_memory.retrieve(query)
        else:
            print(f"HMM: Unknown retrieval strategy: {strategy}. Defaulting to hierarchical.")
            working_results = await self.working_memory.retrieve(query)
            for item in working_results:
                if item.id not in seen_ids:
                    results.append(item)
                    seen_ids.add(item.id)

        results.sort(key=lambda x: x.importance_score, reverse=True)
        return results[:query.limit]

    async def get_memory_hierarchy_stats(self) -> Dict[str, Any]:
        """
        Aggregates statistics from all memory tiers.

        Returns:
            A dictionary containing an overview, tier distribution, and performance summary.
        """
        await self._ensure_initialized()

        wm_stats = await self.working_memory.get_tier_stats()
        ltm_stats = await self.long_term_memory.get_tier_stats()
        em_stats = await self.episodic_memory.get_tier_stats()

        total_items = wm_stats.get('item_count', 0) +                       ltm_stats.get('total_items', ltm_stats.get('item_count',0)) +                       em_stats.get('item_count', 0)

        targets_met = True # Placeholder

        return {
            'hierarchy_overview': {
                'total_items': total_items,
                'last_cleanup_run': None
            },
            'tier_distribution': {
                MemoryTier.WORKING.value: wm_stats,
                MemoryTier.LONG_TERM.value: ltm_stats,
                MemoryTier.EPISODIC.value: em_stats,
            },
            'performance_summary': {
                'targets_met': targets_met,
                'notes': "Performance target check is currently a placeholder."
            }
        }

    async def shutdown(self):
        """
        Shuts down the memory manager and its components.
        (Currently, tiers don't have explicit shutdown methods)
        """
        self.is_initialized = False
        print("HierarchicalMemoryManager shutdown.")

    async def run_periodic_cleanup(self):
        """
        Periodically runs cleanup operations on all tiers.
        This might be called by an external scheduler.
        """
        await self._ensure_initialized()
        print(f"HMM: Running periodic cleanup at {datetime.now(timezone.utc)}")
        wm_cleaned = await self.working_memory.cleanup_expired()
        ltm_cleaned = await self.long_term_memory.cleanup_expired()
        em_cleaned = await self.episodic_memory.cleanup_expired()
        print(f"HMM: Cleanup complete. WM cleaned: {wm_cleaned}, LTM cleaned: {ltm_cleaned}, EM cleaned: {em_cleaned}")


if __name__ == "__main__":
    # Example usage (for testing the manager directly)
    async def main_test():
        print("--- Testing HierarchicalMemoryManager ---")
        manager = HierarchicalMemoryManager()
        await manager.initialize()

        # Mock ClassificationResult using real ClassificationStrategy
        from .classification_engine import ClassificationMetrics

        def create_mock_result(strategy, confidence, importance=None):
            return ClassificationResult(
                strategy=strategy,
                confidence=confidence,
                metrics=ClassificationMetrics(),
                reasoning="Mock classification for testing",
                processing_time_ms=1.0
            )

        item_id1 = await manager.store_memory(
            "This is a high importance working memory item",
            {"id": "item_wk_01", "tags": ["urgent", "code"]},
            create_mock_result(ClassificationStrategy.CUSTOM_DOMAIN, 0.9)
        )
        item_id2 = await manager.store_memory(
            "Standard long-term memory about project Alpha",
            {"id": "item_ltm_01", "project": "Alpha"},
            create_mock_result(ClassificationStrategy.SHARED_ONTOLOGY, 0.6)
        )
        item_id3 = await manager.store_memory(
            "An event: User logged in",
            {"id": "item_ep_01", "type": "event", "user": "test_user", "is_event": True},
            create_mock_result(ClassificationStrategy.ISOLATED_NAMESPACE, 0.5)
        )
        item_id4 = await manager.store_memory(
            "Another working memory item about API design",
            {"id": "item_wk_02", "tags": ["api"], "target_tier": "working"},
            create_mock_result(ClassificationStrategy.SHARED_ONTOLOGY, 0.8)
        )


        print(f"Stored items: {item_id1}, {item_id2}, {item_id3}, {item_id4}")

        print("\n--- Retrieval Tests ---")
        query1 = MemoryQuery(query_text="working memory", limit=5)
        results1 = await manager.retrieve_memories(query1, strategy="hierarchical")
        print(f"Query 1 ('working memory', hierarchical): Found {len(results1)} items.")
        for item in results1:
            print(f"  - {item.id} ({item.tier.value}): {item.content[:30]}... (Score: {item.importance_score:.2f})")
        assert any(i.id == "item_wk_01" for i in results1), "item_wk_01 not found by query1"


        query2 = MemoryQuery(query_text="project Alpha", limit=5, tier_preference=MemoryTier.LONG_TERM)
        results2 = await manager.retrieve_memories(query2)
        print(f"Query 2 ('project Alpha', LTM preferred): Found {len(results2)} items.")
        for item in results2:
            print(f"  - {item.id} ({item.tier.value}): {item.content[:30]}... (Score: {item.importance_score:.2f})")
        assert any(i.id == "item_ltm_01" for i in results2), "item_ltm_01 not found by query2"
        if results2:
             assert all(i.tier == MemoryTier.LONG_TERM for i in results2), "Query 2 returned items not from LTM"


        query3 = MemoryQuery(query_text="event", limit=5)
        results3 = await manager.retrieve_memories(query3, strategy="episodic_only")
        print(f"Query 3 ('event', episodic_only): Found {len(results3)} items.")
        for item in results3:
            print(f"  - {item.id} ({item.tier.value}): {item.content[:30]}... (Score: {item.importance_score:.2f})")


        stats = await manager.get_memory_hierarchy_stats()
        print(f"\n--- Memory Hierarchy Stats ---")
        print(f"Total items: {stats['hierarchy_overview']['total_items']}")
        print(f"Working Memory items: {stats['tier_distribution']['working']['item_count']}")
        print(f"Long-Term Memory items: {stats['tier_distribution']['long_term']['total_items']}")
        print(f"Episodic Memory items: {stats['tier_distribution']['episodic']['item_count']}")
        assert stats['hierarchy_overview']['total_items'] == 4

        print("\n--- Periodic Cleanup Test ---")
        await manager.run_periodic_cleanup()

        await manager.shutdown()
        print("\n--- HierarchicalMemoryManager Test Completed ---")

    asyncio.run(main_test())
