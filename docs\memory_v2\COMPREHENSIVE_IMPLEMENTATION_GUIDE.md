# Comprehensive Implementation Guide
## Intelligent Data Classification and Ontology Management Architecture
### Agent Zero v2.0 - Step-by-Step Implementation

This guide provides detailed, step-by-step instructions for implementing the intelligent data classification and ontology management architecture for Agent Zero v2.0. Each step includes exact file paths, code snippets, configuration changes, and validation criteria.

## 📋 Prerequisites

**Required Knowledge:**
- Basic Python programming (classes, async/await, type hints)
- Understanding of Agent Zero's existing codebase structure
- Basic familiarity with vector databases and embeddings

**System Requirements:**
- Python 3.8+
- Agent Zero v2.0 codebase
- Access to OpenAI API or compatible embedding service
- Minimum 8GB RAM for development
- 50GB+ storage for knowledge graphs

## 🎯 Implementation Overview

The implementation follows a 7-phase approach:

1. **Phase 1: Foundation Setup** (4-6 hours)
2. **Phase 2: Classification Engine** (8-12 hours)
3. **Phase 3: Hierarchical Memory System** (12-16 hours)
4. **Phase 4: User Preference Framework** (6-8 hours)
5. **Phase 5: Data Separation Enforcement** (4-6 hours)
6. **Phase 6: Performance Optimization** (8-10 hours)
7. **Phase 7: Testing and Validation** (6-8 hours)

**Total Estimated Time:** 48-66 hours for a junior developer

---

# Phase 1: Foundation Setup

## Step 1.1: Install Dependencies

**File:** `requirements.txt`
**Action:** Add new dependencies

```bash
# Navigate to project root
cd /path/to/agent-zero-v2

# Add to requirements.txt
echo "scikit-learn>=1.3.0" >> requirements.txt
echo "numpy>=1.24.0" >> requirements.txt
echo "scipy>=1.10.0" >> requirements.txt
echo "networkx>=3.1" >> requirements.txt
echo "python-dateutil>=2.8.2" >> requirements.txt

# Install dependencies
pip install -r requirements.txt
```

**Validation:**
```python
# Test imports
import sklearn
import numpy as np
import scipy
import networkx as nx
from dateutil import parser
print("All dependencies installed successfully")
```

## Step 1.2: Create Configuration Structure

**File:** `python/helpers/classification_config.py`
**Action:** Create new file

```python
"""
Configuration management for intelligent data classification system
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
import os
from enum import Enum

class ClassificationStrategy(Enum):
    SHARED_ONTOLOGY = "shared_ontology"
    CUSTOM_DOMAIN = "custom_domain"
    ISOLATED_NAMESPACE = "isolated_namespace"

@dataclass
class ClassificationThresholds:
    """Thresholds for classification decisions"""
    semantic_overlap_shared: float = 0.70
    semantic_overlap_custom: float = 0.30
    entity_confidence_min: float = 0.70
    domain_specificity_threshold: float = 0.40
    privacy_score_threshold: int = 3
    interconnectedness_threshold: float = 0.30
    min_related_entities: int = 5

@dataclass
class PerformanceTargets:
    """Performance targets for the system"""
    working_memory_response_ms: int = 100
    long_term_memory_response_ms: int = 500
    episodic_memory_response_ms: int = 1000
    classification_decision_ms: int = 50
    cache_hit_rate_target: float = 0.85
    classification_accuracy_target: float = 0.90

@dataclass
class ClassificationConfig:
    """Main configuration for classification system"""
    thresholds: ClassificationThresholds = field(default_factory=ClassificationThresholds)
    performance_targets: PerformanceTargets = field(default_factory=PerformanceTargets)
    
    # Memory tier configuration
    working_memory_size: int = 50000  # Number of recent items
    working_memory_days: int = 7      # Days to keep in working memory
    
    # Batch processing
    batch_size: int = 100
    batch_timeout: int = 300
    
    # Caching
    enable_caching: bool = True
    cache_ttl_seconds: int = 3600
    
    # User preferences
    enable_adaptive_learning: bool = True
    preference_learning_rate: float = 0.1
    
    @classmethod
    def from_env(cls) -> 'ClassificationConfig':
        """Load configuration from environment variables"""
        config = cls()
        
        # Override with environment variables if present
        config.thresholds.semantic_overlap_shared = float(
            os.getenv('CLASSIFICATION_SEMANTIC_OVERLAP_SHARED', '0.70')
        )
        config.thresholds.semantic_overlap_custom = float(
            os.getenv('CLASSIFICATION_SEMANTIC_OVERLAP_CUSTOM', '0.30')
        )
        config.performance_targets.working_memory_response_ms = int(
            os.getenv('CLASSIFICATION_WORKING_MEMORY_TARGET_MS', '100')
        )
        
        return config

# Global configuration instance
classification_config = ClassificationConfig.from_env()
```

**Validation:**
```python
from python.helpers.classification_config import classification_config, ClassificationStrategy
print(f"Config loaded: {classification_config.thresholds.semantic_overlap_shared}")
print(f"Strategies available: {list(ClassificationStrategy)}")
```

## Step 1.3: Create Base Classification Framework

**File:** `python/helpers/classification_engine.py`
**Action:** Create new file

```python
"""
Base framework for intelligent data classification engine
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import asyncio
import time

from .classification_config import ClassificationConfig, ClassificationStrategy

@dataclass
class ClassificationMetrics:
    """Metrics calculated for classification decisions"""
    semantic_overlap: float
    entity_confidence: float
    domain_specificity: float
    relationship_density: float
    privacy_score: int
    interconnectedness: float
    temporal_relevance: float
    related_entities_count: int
    privacy_flags: bool = False
    user_specified_isolation: bool = False

@dataclass
class ClassificationResult:
    """Result of classification analysis"""
    strategy: ClassificationStrategy
    confidence: float
    metrics: ClassificationMetrics
    reasoning: str
    processing_time_ms: float
    namespace: Optional[str] = None
    domain: Optional[str] = None

class ClassificationEngine(ABC):
    """Abstract base class for classification engines"""
    
    def __init__(self, config: ClassificationConfig):
        self.config = config
        self.performance_tracker = PerformanceTracker()
    
    @abstractmethod
    async def classify_content(
        self, 
        content: str, 
        metadata: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
        """Classify content and return classification result"""
        pass
    
    @abstractmethod
    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extract entities from content"""
        pass
    
    @abstractmethod
    async def calculate_semantic_overlap(
        self, 
        entities: List[Dict[str, Any]], 
        existing_schemas: List[Dict[str, Any]]
    ) -> float:
        """Calculate semantic overlap with existing schemas"""
        pass

class PerformanceTracker:
    """Track performance metrics for classification operations"""
    
    def __init__(self):
        self.metrics_history: List[Dict[str, Any]] = []
        self.start_time: Optional[float] = None
    
    def start_timing(self):
        """Start timing an operation"""
        self.start_time = time.time()
    
    def end_timing(self) -> float:
        """End timing and return duration in milliseconds"""
        if self.start_time is None:
            return 0.0
        
        duration_ms = (time.time() - self.start_time) * 1000
        self.start_time = None
        return duration_ms
    
    def record_classification(self, result: ClassificationResult):
        """Record classification performance metrics"""
        self.metrics_history.append({
            'timestamp': datetime.now(),
            'strategy': result.strategy.value,
            'confidence': result.confidence,
            'processing_time_ms': result.processing_time_ms,
            'semantic_overlap': result.metrics.semantic_overlap,
            'entity_confidence': result.metrics.entity_confidence
        })
        
        # Keep only last 1000 records
        if len(self.metrics_history) > 1000:
            self.metrics_history = self.metrics_history[-1000:]
    
    def get_average_processing_time(self) -> float:
        """Get average processing time in milliseconds"""
        if not self.metrics_history:
            return 0.0
        
        times = [m['processing_time_ms'] for m in self.metrics_history]
        return sum(times) / len(times)
    
    def get_classification_accuracy(self) -> float:
        """Get classification accuracy (placeholder for now)"""
        # This would be calculated based on user feedback
        # For now, return a placeholder value
        return 0.85
```

**Validation:**
```python
from python.helpers.classification_engine import ClassificationEngine, ClassificationMetrics
from python.helpers.classification_config import classification_config

# Test that classes can be imported and instantiated
metrics = ClassificationMetrics(
    semantic_overlap=0.8,
    entity_confidence=0.9,
    domain_specificity=0.6,
    relationship_density=0.7,
    privacy_score=1,
    interconnectedness=0.8,
    temporal_relevance=0.9,
    related_entities_count=10
)
print(f"Metrics created: {metrics.semantic_overlap}")
```

## Step 1.4: Update Memory Abstraction Layer

**File:** `python/helpers/memory_abstraction.py`
**Action:** Extend existing file

Add the following imports at the top:
```python
from .classification_engine import ClassificationEngine, ClassificationResult
from .classification_config import classification_config
```

Add this method to the `EnhancedMemoryAbstractionLayer` class:
```python
async def classify_and_route_content(
    self, 
    content: str, 
    content_type: str, 
    metadata: Dict[str, Any]
) -> Tuple[str, ClassificationResult]:
    """Classify content and route to appropriate storage"""
    await self._ensure_initialized()
    
    # Get classification engine (will be implemented in Phase 2)
    classification_engine = await self._get_classification_engine()
    
    # Classify the content
    classification_result = await classification_engine.classify_content(
        content, metadata
    )
    
    # Route based on classification
    if classification_result.strategy == ClassificationStrategy.SHARED_ONTOLOGY:
        doc_id = await self._store_in_shared_ontology(content, metadata, classification_result)
    elif classification_result.strategy == ClassificationStrategy.CUSTOM_DOMAIN:
        doc_id = await self._store_in_custom_domain(content, metadata, classification_result)
    else:  # ISOLATED_NAMESPACE
        doc_id = await self._store_in_isolated_namespace(content, metadata, classification_result)
    
    return doc_id, classification_result

async def _get_classification_engine(self):
    """Get or create classification engine instance"""
    if not hasattr(self, '_classification_engine'):
        # Will be implemented in Phase 2
        from .intelligent_classification_engine import IntelligentClassificationEngine
        self._classification_engine = IntelligentClassificationEngine(classification_config)
    return self._classification_engine

# Placeholder methods for routing (will be implemented in Phase 3)
async def _store_in_shared_ontology(self, content: str, metadata: Dict[str, Any], result: ClassificationResult) -> str:
    """Store content in shared ontology"""
    # For now, use existing backend
    return await self.backend.insert_knowledge_document(content, metadata)

async def _store_in_custom_domain(self, content: str, metadata: Dict[str, Any], result: ClassificationResult) -> str:
    """Store content in custom domain ontology"""
    # For now, use existing backend
    return await self.backend.insert_knowledge_document(content, metadata)

async def _store_in_isolated_namespace(self, content: str, metadata: Dict[str, Any], result: ClassificationResult) -> str:
    """Store content in isolated namespace"""
    # For now, use existing backend
    return await self.backend.insert_text(content, metadata)
```

**Validation:**
```python
# Test that the updated memory abstraction layer can be imported
from python.helpers.memory_abstraction import EnhancedMemoryAbstractionLayer
print("Memory abstraction layer updated successfully")
```

---

**End of Phase 1 Foundation Setup**

✅ **Phase 1 Complete - Validation Checklist:**
- [ ] All dependencies installed without errors
- [ ] Configuration classes can be imported and instantiated
- [ ] Base classification framework classes are available
- [ ] Memory abstraction layer has been extended with classification methods
- [ ] No import errors when testing the new modules

**Next:** Proceed to Phase 2 - Classification Engine Implementation

---

# Phase 2: Classification Engine Implementation

## Step 2.1: Create Intelligent Classification Engine

**File:** `python/helpers/intelligent_classification_engine.py`
**Action:** Create new file

```python
"""
Intelligent Classification Engine with decision matrix and quantitative metrics
"""

import asyncio
import time
import math
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer

from .classification_engine import ClassificationEngine, ClassificationResult, ClassificationMetrics
from .classification_config import ClassificationConfig, ClassificationStrategy

class IntelligentClassificationEngine(ClassificationEngine):
    """
    Advanced classification engine with quantitative decision matrix
    """

    def __init__(self, config: ClassificationConfig):
        super().__init__(config)
        self.tfidf_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.entity_cache: Dict[str, Dict[str, Any]] = {}
        self.schema_cache: Dict[str, List[Dict[str, Any]]] = {}
        self.domain_keywords = self._initialize_domain_keywords()

    def _initialize_domain_keywords(self) -> Dict[str, List[str]]:
        """Initialize domain-specific keywords for classification"""
        return {
            'programming': [
                'function', 'class', 'variable', 'api', 'framework', 'library',
                'code', 'algorithm', 'database', 'server', 'client', 'debug',
                'compile', 'runtime', 'syntax', 'method', 'object', 'interface'
            ],
            'medical': [
                'patient', 'diagnosis', 'treatment', 'symptom', 'medication',
                'therapy', 'clinical', 'hospital', 'doctor', 'nurse', 'surgery',
                'disease', 'condition', 'prescription', 'medical', 'health'
            ],
            'legal': [
                'contract', 'clause', 'jurisdiction', 'liability', 'agreement',
                'court', 'judge', 'lawyer', 'legal', 'law', 'statute', 'regulation',
                'compliance', 'litigation', 'settlement', 'defendant', 'plaintiff'
            ],
            'finance': [
                'investment', 'portfolio', 'risk', 'return', 'profit', 'loss',
                'market', 'stock', 'bond', 'asset', 'liability', 'revenue',
                'expense', 'budget', 'financial', 'accounting', 'audit'
            ],
            'academic': [
                'research', 'study', 'analysis', 'theory', 'hypothesis',
                'experiment', 'data', 'methodology', 'conclusion', 'literature',
                'publication', 'journal', 'conference', 'peer-review', 'citation'
            ]
        }

    async def classify_content(
        self,
        content: str,
        metadata: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
        """
        Classify content using intelligent decision matrix
        """
        self.performance_tracker.start_timing()

        try:
            # Extract entities from content
            entities = await self.extract_entities(content)

            # Calculate all classification metrics
            metrics = await self._calculate_classification_metrics(
                content, entities, metadata, context
            )

            # Apply decision matrix
            strategy, confidence, reasoning = self._apply_decision_matrix(metrics)

            # Determine namespace/domain if applicable
            namespace, domain = self._determine_storage_location(strategy, metrics, metadata)

            processing_time = self.performance_tracker.end_timing()

            result = ClassificationResult(
                strategy=strategy,
                confidence=confidence,
                metrics=metrics,
                reasoning=reasoning,
                processing_time_ms=processing_time,
                namespace=namespace,
                domain=domain
            )

            # Record performance metrics
            self.performance_tracker.record_classification(result)

            return result

        except Exception as e:
            processing_time = self.performance_tracker.end_timing()
            # Return default classification on error
            return ClassificationResult(
                strategy=ClassificationStrategy.ISOLATED_NAMESPACE,
                confidence=0.5,
                metrics=ClassificationMetrics(
                    semantic_overlap=0.0,
                    entity_confidence=0.0,
                    domain_specificity=0.0,
                    relationship_density=0.0,
                    privacy_score=10,  # High privacy score for safety
                    interconnectedness=0.0,
                    temporal_relevance=1.0,
                    related_entities_count=0,
                    privacy_flags=True
                ),
                reasoning=f"Classification failed, defaulting to isolated storage: {str(e)}",
                processing_time_ms=processing_time,
                namespace="error_isolation"
            )

    async def extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """
        Extract entities from content using rule-based approach
        (In production, this would use a proper NER model)
        """
        # Simple entity extraction using patterns
        entities = []

        # Extract potential entities using common patterns
        patterns = {
            'PERSON': r'\b[A-Z][a-z]+ [A-Z][a-z]+\b',
            'EMAIL': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'PHONE': r'\b\d{3}-\d{3}-\d{4}\b|\b\(\d{3}\)\s*\d{3}-\d{4}\b',
            'URL': r'https?://[^\s]+',
            'DATE': r'\b\d{1,2}/\d{1,2}/\d{4}\b|\b\d{4}-\d{2}-\d{2}\b',
            'MONEY': r'\$\d+(?:,\d{3})*(?:\.\d{2})?',
            'ORGANIZATION': r'\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Corporation)\b'
        }

        for entity_type, pattern in patterns.items():
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                entities.append({
                    'text': match.group(),
                    'label': entity_type,
                    'start': match.start(),
                    'end': match.end(),
                    'confidence_score': 0.8  # Default confidence
                })

        # Extract domain-specific terms
        content_lower = content.lower()
        for domain, keywords in self.domain_keywords.items():
            for keyword in keywords:
                if keyword.lower() in content_lower:
                    entities.append({
                        'text': keyword,
                        'label': f'DOMAIN_{domain.upper()}',
                        'start': content_lower.find(keyword.lower()),
                        'end': content_lower.find(keyword.lower()) + len(keyword),
                        'confidence_score': 0.7
                    })

        return entities

    async def calculate_semantic_overlap(
        self,
        entities: List[Dict[str, Any]],
        existing_schemas: List[Dict[str, Any]]
    ) -> float:
        """
        Calculate semantic overlap using TF-IDF similarity
        """
        if not entities or not existing_schemas:
            return 0.0

        # Extract entity texts
        entity_texts = [entity['text'] for entity in entities]
        entity_content = ' '.join(entity_texts)

        # Extract schema texts
        schema_texts = []
        for schema in existing_schemas:
            schema_content = ' '.join(schema.get('entities', []))
            schema_texts.append(schema_content)

        if not schema_texts:
            return 0.0

        try:
            # Combine all texts for vectorization
            all_texts = [entity_content] + schema_texts

            # Calculate TF-IDF vectors
            tfidf_matrix = self.tfidf_vectorizer.fit_transform(all_texts)

            # Calculate cosine similarity between entity content and schemas
            entity_vector = tfidf_matrix[0:1]
            schema_vectors = tfidf_matrix[1:]

            similarities = cosine_similarity(entity_vector, schema_vectors)[0]

            # Return maximum similarity
            return float(np.max(similarities)) if len(similarities) > 0 else 0.0

        except Exception as e:
            print(f"Error calculating semantic overlap: {e}")
            return 0.0

    async def _calculate_classification_metrics(
        self,
        content: str,
        entities: List[Dict[str, Any]],
        metadata: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ClassificationMetrics:
        """Calculate all metrics needed for classification decision"""

        # 1. Semantic overlap analysis
        existing_schemas = await self._get_existing_schemas()
        semantic_overlap = await self.calculate_semantic_overlap(entities, existing_schemas)

        # 2. Entity confidence score
        entity_confidence = self._calculate_entity_confidence(entities)

        # 3. Domain specificity scoring
        domain_specificity, detected_domain = self._calculate_domain_specificity(content, entities)

        # 4. Relationship density analysis
        relationship_density = self._calculate_relationship_density(entities)

        # 5. Privacy flag detection
        privacy_score, privacy_flags = self._detect_privacy_flags(content, entities)

        # 6. Interconnectedness analysis
        interconnectedness = await self._calculate_interconnectedness(entities)

        # 7. Temporal relevance weighting
        temporal_relevance = self._calculate_temporal_relevance(metadata.get('timestamp'))

        # 8. Related entities count
        related_entities_count = len(entities)

        # 9. User-specified isolation
        user_specified_isolation = metadata.get('force_isolation', False)

        return ClassificationMetrics(
            semantic_overlap=semantic_overlap,
            entity_confidence=entity_confidence,
            domain_specificity=domain_specificity,
            relationship_density=relationship_density,
            privacy_score=privacy_score,
            interconnectedness=interconnectedness,
            temporal_relevance=temporal_relevance,
            related_entities_count=related_entities_count,
            privacy_flags=privacy_flags,
            user_specified_isolation=user_specified_isolation
        )

    def _calculate_entity_confidence(self, entities: List[Dict[str, Any]]) -> float:
        """Calculate average confidence score for extracted entities"""
        if not entities:
            return 0.0

        confidence_scores = [entity.get('confidence_score', 0.5) for entity in entities]
        return sum(confidence_scores) / len(confidence_scores)

    def _calculate_domain_specificity(self, content: str, entities: List[Dict[str, Any]]) -> Tuple[float, Optional[str]]:
        """Calculate how domain-specific the content is"""
        content_lower = content.lower()
        domain_scores = {}

        for domain, keywords in self.domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword.lower() in content_lower)
            domain_scores[domain] = score / len(keywords)

        # Also check entities for domain indicators
        for entity in entities:
            if entity['label'].startswith('DOMAIN_'):
                domain = entity['label'].replace('DOMAIN_', '').lower()
                if domain in domain_scores:
                    domain_scores[domain] += 0.1  # Boost for entity detection

        if not domain_scores:
            return 0.0, None

        max_domain = max(domain_scores, key=domain_scores.get)
        max_score = domain_scores[max_domain]

        return max_score, max_domain if max_score > 0.1 else None

    def _calculate_relationship_density(self, entities: List[Dict[str, Any]]) -> float:
        """Calculate density of relationships between entities"""
        if len(entities) <= 1:
            return 0.0

        # Simple heuristic: entities that appear close together are related
        relationships = 0
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities[i+1:], i+1):
                # If entities are within 100 characters, consider them related
                distance = abs(entity1['start'] - entity2['start'])
                if distance < 100:
                    relationships += 1

        max_possible = len(entities) * (len(entities) - 1) / 2
        return relationships / max_possible if max_possible > 0 else 0.0

    def _detect_privacy_flags(self, content: str, entities: List[Dict[str, Any]]) -> Tuple[int, bool]:
        """Detect privacy-sensitive content"""
        privacy_indicators = [
            'personal', 'private', 'confidential', 'secret', 'classified',
            'password', 'api_key', 'token', 'credential', 'sensitive',
            'internal', 'proprietary', 'restricted', 'confidential'
        ]

        content_lower = content.lower()
        privacy_score = 0

        # Check for privacy keywords
        for indicator in privacy_indicators:
            if indicator in content_lower:
                privacy_score += 1

        # Check for PII entities
        pii_entities = ['PERSON', 'EMAIL', 'PHONE', 'SSN', 'CREDIT_CARD']
        for entity in entities:
            if entity['label'] in pii_entities:
                privacy_score += 2

        privacy_flags = privacy_score >= self.config.thresholds.privacy_score_threshold
        return privacy_score, privacy_flags

    async def _calculate_interconnectedness(self, entities: List[Dict[str, Any]]) -> float:
        """Calculate how interconnected entities are with existing knowledge"""
        # Placeholder implementation - in production, this would query the knowledge graph
        # For now, return a value based on entity types and confidence
        if not entities:
            return 0.0

        # Higher interconnectedness for common entity types
        common_types = ['PERSON', 'ORGANIZATION', 'DOMAIN_PROGRAMMING']
        interconnected_entities = sum(1 for entity in entities if entity['label'] in common_types)

        return interconnected_entities / len(entities) if entities else 0.0

    def _calculate_temporal_relevance(self, timestamp: Optional[str]) -> float:
        """Calculate temporal relevance with decay function"""
        if not timestamp:
            return 1.0  # Assume current if no timestamp

        try:
            from dateutil import parser
            event_time = parser.parse(timestamp) if isinstance(timestamp, str) else timestamp
            current_time = datetime.now()

            time_diff_hours = (current_time - event_time).total_seconds() / 3600

            # Exponential decay with half-life of 30 days (720 hours)
            half_life = 720
            decay_factor = math.exp(-0.693 * time_diff_hours / half_life)

            return max(0.1, decay_factor)  # Minimum relevance of 0.1

        except Exception:
            return 1.0  # Default to full relevance on error

    def _apply_decision_matrix(self, metrics: ClassificationMetrics) -> Tuple[ClassificationStrategy, float, str]:
        """Apply decision matrix to determine classification strategy"""

        # Rule 1: Isolated Namespace (highest priority)
        if self._classify_as_isolated(metrics):
            confidence = 0.9 if metrics.privacy_flags else 0.8
            reasoning = self._build_isolation_reasoning(metrics)
            return ClassificationStrategy.ISOLATED_NAMESPACE, confidence, reasoning

        # Rule 2: Shared Ontology
        if self._classify_as_shared_ontology(metrics):
            confidence = min(0.95, 0.7 + (metrics.semantic_overlap * 0.3))
            reasoning = f"High semantic overlap ({metrics.semantic_overlap:.2f}) with existing schemas, " \
                       f"good entity confidence ({metrics.entity_confidence:.2f}), " \
                       f"suitable for shared knowledge base"
            return ClassificationStrategy.SHARED_ONTOLOGY, confidence, reasoning

        # Rule 3: Custom Domain
        if self._classify_as_custom_domain(metrics):
            confidence = min(0.9, 0.6 + (metrics.domain_specificity * 0.3))
            reasoning = f"Domain-specific content (specificity: {metrics.domain_specificity:.2f}), " \
                       f"sufficient entities ({metrics.related_entities_count}), " \
                       f"low semantic overlap ({metrics.semantic_overlap:.2f})"
            return ClassificationStrategy.CUSTOM_DOMAIN, confidence, reasoning

        # Default: Custom Domain with lower confidence
        confidence = 0.5
        reasoning = "Default classification - content doesn't clearly fit other categories"
        return ClassificationStrategy.CUSTOM_DOMAIN, confidence, reasoning

    def _classify_as_shared_ontology(self, metrics: ClassificationMetrics) -> bool:
        """Determine if content should go to shared ontology"""
        return (
            metrics.semantic_overlap >= self.config.thresholds.semantic_overlap_shared and
            metrics.entity_confidence >= self.config.thresholds.entity_confidence_min and
            not metrics.privacy_flags and
            metrics.domain_specificity < 0.60 and
            not metrics.user_specified_isolation
        )

    def _classify_as_custom_domain(self, metrics: ClassificationMetrics) -> bool:
        """Determine if content should go to custom domain ontology"""
        return (
            metrics.semantic_overlap <= self.config.thresholds.semantic_overlap_custom and
            metrics.related_entities_count >= self.config.thresholds.min_related_entities and
            metrics.entity_confidence >= self.config.thresholds.entity_confidence_min and
            metrics.domain_specificity >= self.config.thresholds.domain_specificity_threshold and
            not metrics.privacy_flags and
            not metrics.user_specified_isolation
        )

    def _classify_as_isolated(self, metrics: ClassificationMetrics) -> bool:
        """Determine if content should go to isolated namespace"""
        return (
            metrics.privacy_flags or
            metrics.interconnectedness < self.config.thresholds.interconnectedness_threshold or
            metrics.entity_confidence < 0.50 or
            metrics.user_specified_isolation
        )

    def _build_isolation_reasoning(self, metrics: ClassificationMetrics) -> str:
        """Build reasoning string for isolation classification"""
        reasons = []

        if metrics.privacy_flags:
            reasons.append(f"privacy concerns detected (score: {metrics.privacy_score})")
        if metrics.interconnectedness < self.config.thresholds.interconnectedness_threshold:
            reasons.append(f"low interconnectedness ({metrics.interconnectedness:.2f})")
        if metrics.entity_confidence < 0.50:
            reasons.append(f"low entity confidence ({metrics.entity_confidence:.2f})")
        if metrics.user_specified_isolation:
            reasons.append("user-specified isolation")

        return f"Isolated storage due to: {', '.join(reasons)}"

    def _determine_storage_location(
        self,
        strategy: ClassificationStrategy,
        metrics: ClassificationMetrics,
        metadata: Dict[str, Any]
    ) -> Tuple[Optional[str], Optional[str]]:
        """Determine specific namespace and domain for storage"""

        if strategy == ClassificationStrategy.ISOLATED_NAMESPACE:
            # Create isolated namespace based on user or content type
            user_id = metadata.get('user_id', 'default')
            content_type = metadata.get('content_type', 'general')
            namespace = f"isolated_{user_id}_{content_type}"
            return namespace, None

        elif strategy == ClassificationStrategy.CUSTOM_DOMAIN:
            # Determine domain from content analysis
            domain = self._detect_primary_domain(metrics, metadata)
            return None, domain

        else:  # SHARED_ONTOLOGY
            return None, "shared"

    def _detect_primary_domain(self, metrics: ClassificationMetrics, metadata: Dict[str, Any]) -> str:
        """Detect the primary domain for custom domain classification"""
        # Check metadata first
        if 'domain' in metadata:
            return metadata['domain']

        # Analyze content for domain indicators
        content = metadata.get('content', '')
        content_lower = content.lower()

        domain_scores = {}
        for domain, keywords in self.domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword.lower() in content_lower)
            if score > 0:
                domain_scores[domain] = score

        if domain_scores:
            return max(domain_scores, key=domain_scores.get)

        # Default domain
        return 'general'

    async def _get_existing_schemas(self) -> List[Dict[str, Any]]:
        """Get existing schemas for semantic overlap calculation"""
        # Placeholder - in production, this would query the knowledge graph
        # For now, return some mock schemas
        return [
            {
                'domain': 'programming',
                'entities': ['function', 'class', 'variable', 'api', 'code'],
                'schema_id': 'prog_001'
            },
            {
                'domain': 'medical',
                'entities': ['patient', 'diagnosis', 'treatment', 'medication'],
                'schema_id': 'med_001'
            }
        ]
```

**Validation:**
```python
# Test the intelligent classification engine
from python.helpers.intelligent_classification_engine import IntelligentClassificationEngine
from python.helpers.classification_config import classification_config

engine = IntelligentClassificationEngine(classification_config)

# Test entity extraction
test_content = "John Smith works at Microsoft Corporation. His <NAME_EMAIL>"
entities = await engine.extract_entities(test_content)
print(f"Extracted {len(entities)} entities")

# Test classification
result = await engine.classify_content(test_content, {'content_type': 'text'})
print(f"Classification: {result.strategy.value} with confidence {result.confidence}")
```

## Step 2.2: Create Entity Relationship Analyzer

**File:** `python/helpers/entity_relationship_analyzer.py`
**Action:** Create new file

```python
"""
Entity Relationship Analyzer for intelligent classification
"""

from typing import Dict, Any, List, Tuple, Set
from dataclasses import dataclass
import networkx as nx
from collections import defaultdict

@dataclass
class EntityRelationship:
    """Represents a relationship between two entities"""
    source_entity: str
    target_entity: str
    relationship_type: str
    confidence: float
    context: str

class EntityRelationshipAnalyzer:
    """Analyzes relationships between entities for classification decisions"""

    def __init__(self):
        self.relationship_graph = nx.DiGraph()
        self.entity_cooccurrence = defaultdict(lambda: defaultdict(int))
        self.relationship_patterns = self._initialize_relationship_patterns()

    def _initialize_relationship_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for detecting relationships"""
        return {
            'works_at': [
                r'{entity1} works at {entity2}',
                r'{entity1} is employed by {entity2}',
                r'{entity1} at {entity2}'
            ],
            'located_in': [
                r'{entity1} in {entity2}',
                r'{entity1} located in {entity2}',
                r'{entity1}, {entity2}'
            ],
            'part_of': [
                r'{entity1} of {entity2}',
                r'{entity1} belongs to {entity2}'
            ],
            'related_to': [
                r'{entity1} and {entity2}',
                r'{entity1} with {entity2}',
                r'{entity1}, {entity2}'
            ]
        }

    async def analyze_entity_relationships(
        self,
        entities: List[Dict[str, Any]],
        content: str
    ) -> List[EntityRelationship]:
        """Analyze relationships between entities in content"""
        relationships = []

        # Analyze pairwise relationships
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities[i+1:], i+1):
                relationship = await self._detect_relationship(entity1, entity2, content)
                if relationship:
                    relationships.append(relationship)
                    self._update_cooccurrence(entity1['text'], entity2['text'])

        return relationships

    async def _detect_relationship(
        self,
        entity1: Dict[str, Any],
        entity2: Dict[str, Any],
        content: str
    ) -> Optional[EntityRelationship]:
        """Detect relationship between two entities"""

        # Calculate distance between entities
        distance = abs(entity1['start'] - entity2['start'])

        # If entities are too far apart, unlikely to be related
        if distance > 200:  # 200 characters
            return None

        # Extract context around entities
        start_pos = min(entity1['start'], entity2['start']) - 50
        end_pos = max(entity1['end'], entity2['end']) + 50
        context = content[max(0, start_pos):min(len(content), end_pos)]

        # Detect relationship type using patterns
        relationship_type = self._classify_relationship_type(entity1, entity2, context)

        # Calculate confidence based on distance and entity types
        confidence = self._calculate_relationship_confidence(entity1, entity2, distance, context)

        if confidence > 0.3:  # Minimum confidence threshold
            return EntityRelationship(
                source_entity=entity1['text'],
                target_entity=entity2['text'],
                relationship_type=relationship_type,
                confidence=confidence,
                context=context
            )

        return None

    def _classify_relationship_type(
        self,
        entity1: Dict[str, Any],
        entity2: Dict[str, Any],
        context: str
    ) -> str:
        """Classify the type of relationship between entities"""

        # Check for specific relationship patterns
        for rel_type, patterns in self.relationship_patterns.items():
            for pattern in patterns:
                # Simple pattern matching (in production, use more sophisticated NLP)
                if any(word in context.lower() for word in pattern.split()):
                    return rel_type

        # Default relationship based on entity types
        if entity1['label'] == 'PERSON' and entity2['label'] == 'ORGANIZATION':
            return 'works_at'
        elif entity1['label'] == 'ORGANIZATION' and entity2['label'] == 'PERSON':
            return 'employs'
        elif 'DOMAIN_' in entity1['label'] and 'DOMAIN_' in entity2['label']:
            return 'related_to'
        else:
            return 'mentioned_with'

    def _calculate_relationship_confidence(
        self,
        entity1: Dict[str, Any],
        entity2: Dict[str, Any],
        distance: int,
        context: str
    ) -> float:
        """Calculate confidence score for relationship"""

        # Base confidence from entity confidence scores
        base_confidence = (entity1.get('confidence_score', 0.5) +
                          entity2.get('confidence_score', 0.5)) / 2

        # Distance penalty (closer entities are more likely to be related)
        distance_factor = max(0.1, 1.0 - (distance / 200.0))

        # Context richness bonus
        context_words = len(context.split())
        context_factor = min(1.0, context_words / 20.0)  # Normalize to 20 words

        # Combine factors
        confidence = base_confidence * distance_factor * (0.7 + 0.3 * context_factor)

        return min(1.0, confidence)

    def _update_cooccurrence(self, entity1: str, entity2: str):
        """Update entity co-occurrence statistics"""
        self.entity_cooccurrence[entity1][entity2] += 1
        self.entity_cooccurrence[entity2][entity1] += 1

    def get_entity_interconnectedness(self, entity: str) -> float:
        """Get interconnectedness score for an entity"""
        if entity not in self.entity_cooccurrence:
            return 0.0

        # Calculate based on number of connections and their strengths
        connections = self.entity_cooccurrence[entity]
        if not connections:
            return 0.0

        total_strength = sum(connections.values())
        num_connections = len(connections)

        # Normalize by maximum possible connections (arbitrary scale)
        max_connections = 100
        interconnectedness = min(1.0, (num_connections / max_connections) *
                               (total_strength / (num_connections * 10)))

        return interconnectedness

    def analyze_domain_clustering(self, entities: List[Dict[str, Any]]) -> Dict[str, float]:
        """Analyze how entities cluster by domain"""
        domain_counts = defaultdict(int)
        total_entities = len(entities)

        for entity in entities:
            if entity['label'].startswith('DOMAIN_'):
                domain = entity['label'].replace('DOMAIN_', '').lower()
                domain_counts[domain] += 1

        # Calculate domain distribution
        domain_distribution = {}
        for domain, count in domain_counts.items():
            domain_distribution[domain] = count / total_entities if total_entities > 0 else 0.0

        return domain_distribution
```

**Validation:**
```python
# Test entity relationship analyzer
from python.helpers.entity_relationship_analyzer import EntityRelationshipAnalyzer

analyzer = EntityRelationshipAnalyzer()

# Test with sample entities
entities = [
    {'text': 'John Smith', 'label': 'PERSON', 'start': 0, 'end': 10, 'confidence_score': 0.9},
    {'text': 'Microsoft', 'label': 'ORGANIZATION', 'start': 20, 'end': 29, 'confidence_score': 0.8}
]

content = "John Smith works at Microsoft Corporation"
relationships = await analyzer.analyze_entity_relationships(entities, content)
print(f"Found {len(relationships)} relationships")
```

## Step 2.3: Update Memory Abstraction Layer with Classification

**File:** `python/helpers/memory_abstraction.py`
**Action:** Update existing file

Add these imports at the top:
```python
from .intelligent_classification_engine import IntelligentClassificationEngine
from .entity_relationship_analyzer import EntityRelationshipAnalyzer
```

Replace the placeholder `_get_classification_engine` method:
```python
async def _get_classification_engine(self):
    """Get or create classification engine instance"""
    if not hasattr(self, '_classification_engine'):
        self._classification_engine = IntelligentClassificationEngine(classification_config)
        self._relationship_analyzer = EntityRelationshipAnalyzer()
    return self._classification_engine

async def get_classification_stats(self) -> Dict[str, Any]:
    """Get classification performance statistics"""
    await self._ensure_initialized()

    if hasattr(self, '_classification_engine'):
        engine = self._classification_engine
        return {
            'average_processing_time_ms': engine.performance_tracker.get_average_processing_time(),
            'classification_accuracy': engine.performance_tracker.get_classification_accuracy(),
            'total_classifications': len(engine.performance_tracker.metrics_history),
            'cache_size': len(engine.entity_cache),
            'schema_cache_size': len(engine.schema_cache)
        }
    else:
        return {'status': 'classification_engine_not_initialized'}
```

---

**End of Phase 2 Classification Engine Implementation**

✅ **Phase 2 Complete - Validation Checklist:**
- [ ] Intelligent classification engine can be imported and instantiated
- [ ] Entity extraction works with sample content
- [ ] Classification decision matrix produces reasonable results
- [ ] Entity relationship analyzer detects relationships
- [ ] Memory abstraction layer integrates with classification engine
- [ ] Performance tracking records classification metrics

**Next:** Proceed to Phase 3 - Hierarchical Memory System

---

# Phase 3: Hierarchical Memory System

## Step 3.1: Create Memory Tier Interfaces

**File:** `python/helpers/memory_tiers.py`
**Action:** Create new file

```python
"""
Hierarchical Memory System - Three-tier architecture implementation
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import time

from .classification_config import classification_config
from .memory_abstraction import MemoryDocument

class MemoryTier(Enum):
    WORKING = "working"
    LONG_TERM = "long_term"
    EPISODIC = "episodic"

@dataclass
class MemoryItem:
    """Unified memory item across all tiers"""
    id: str
    content: str
    metadata: Dict[str, Any]
    tier: MemoryTier
    created_at: datetime
    last_accessed: datetime
    access_count: int
    importance_score: float
    embedding: Optional[List[float]] = None

@dataclass
class MemoryQuery:
    """Query structure for memory retrieval"""
    query_text: str
    tier_preference: Optional[MemoryTier] = None
    time_range: Optional[Tuple[datetime, datetime]] = None
    importance_threshold: float = 0.0
    limit: int = 10
    include_metadata: bool = True

class MemoryTierInterface(ABC):
    """Abstract interface for memory tiers"""

    @abstractmethod
    async def store(self, item: MemoryItem) -> str:
        """Store item in this tier"""
        pass

    @abstractmethod
    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Retrieve items from this tier"""
        pass

    @abstractmethod
    async def update_access(self, item_id: str) -> None:
        """Update access statistics for item"""
        pass

    @abstractmethod
    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get statistics for this tier"""
        pass

    @abstractmethod
    async def cleanup_expired(self) -> int:
        """Clean up expired items, return count removed"""
        pass

class WorkingMemoryTier(MemoryTierInterface):
    """
    Working Memory: Recent 7 days + top 20% frequent entities
    Target: <100ms retrieval
    """

    def __init__(self):
        self.items: Dict[str, MemoryItem] = {}
        self.access_index: Dict[str, List[str]] = {}  # keyword -> item_ids
        self.time_index: Dict[str, List[str]] = {}    # date -> item_ids
        self.max_items = classification_config.working_memory_size
        self.retention_days = classification_config.working_memory_days

    async def store(self, item: MemoryItem) -> str:
        """Store item in working memory with automatic cleanup"""

        # Check if we need to make space
        if len(self.items) >= self.max_items:
            await self._evict_least_important()

        # Store the item
        self.items[item.id] = item

        # Update indices
        await self._update_indices(item)

        return item.id

    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Fast retrieval from working memory using indices"""
        start_time = time.time()

        candidate_items = []

        # Use keyword index for fast lookup
        query_keywords = query.query_text.lower().split()
        candidate_ids = set()

        for keyword in query_keywords:
            if keyword in self.access_index:
                candidate_ids.update(self.access_index[keyword])

        # Filter by time range if specified
        if query.time_range:
            time_filtered_ids = self._filter_by_time_range(query.time_range)
            candidate_ids = candidate_ids.intersection(time_filtered_ids)

        # Get candidate items
        for item_id in candidate_ids:
            if item_id in self.items:
                item = self.items[item_id]
                if item.importance_score >= query.importance_threshold:
                    candidate_items.append(item)

        # Sort by relevance (combination of recency, frequency, and importance)
        candidate_items.sort(
            key=lambda x: self._calculate_relevance_score(x, query),
            reverse=True
        )

        # Update access statistics
        for item in candidate_items[:query.limit]:
            await self.update_access(item.id)

        processing_time = (time.time() - start_time) * 1000

        # Ensure we meet performance target
        if processing_time > classification_config.performance_targets.working_memory_response_ms:
            print(f"Warning: Working memory query took {processing_time:.2f}ms, "
                  f"target is {classification_config.performance_targets.working_memory_response_ms}ms")

        return candidate_items[:query.limit]

    async def update_access(self, item_id: str) -> None:
        """Update access statistics"""
        if item_id in self.items:
            item = self.items[item_id]
            item.last_accessed = datetime.now()
            item.access_count += 1

    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get working memory statistics"""
        if not self.items:
            return {
                'tier': 'working',
                'item_count': 0,
                'avg_importance': 0.0,
                'oldest_item_age_hours': 0,
                'index_sizes': {'access': 0, 'time': 0}
            }

        items = list(self.items.values())
        avg_importance = sum(item.importance_score for item in items) / len(items)

        oldest_item = min(items, key=lambda x: x.created_at)
        oldest_age_hours = (datetime.now() - oldest_item.created_at).total_seconds() / 3600

        return {
            'tier': 'working',
            'item_count': len(self.items),
            'avg_importance': avg_importance,
            'oldest_item_age_hours': oldest_age_hours,
            'index_sizes': {
                'access': len(self.access_index),
                'time': len(self.time_index)
            },
            'max_capacity': self.max_items,
            'capacity_utilization': len(self.items) / self.max_items
        }

    async def cleanup_expired(self) -> int:
        """Remove items older than retention period"""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        expired_ids = []

        for item_id, item in self.items.items():
            if item.created_at < cutoff_date:
                expired_ids.append(item_id)

        # Remove expired items
        for item_id in expired_ids:
            await self._remove_item(item_id)

        return len(expired_ids)

    def _calculate_relevance_score(self, item: MemoryItem, query: MemoryQuery) -> float:
        """Calculate relevance score for ranking"""

        # Recency score (more recent = higher score)
        hours_since_created = (datetime.now() - item.created_at).total_seconds() / 3600
        recency_score = max(0.1, 1.0 - (hours_since_created / (24 * 7)))  # 7-day decay

        # Frequency score (more accessed = higher score)
        frequency_score = min(1.0, item.access_count / 10.0)  # Normalize to 10 accesses

        # Importance score (already normalized 0-1)
        importance_score = item.importance_score

        # Combine scores with weights
        relevance = (
            0.4 * recency_score +
            0.3 * frequency_score +
            0.3 * importance_score
        )

        return relevance

    async def _update_indices(self, item: MemoryItem):
        """Update search indices for fast retrieval"""

        # Update keyword index
        keywords = item.content.lower().split()
        for keyword in keywords:
            if keyword not in self.access_index:
                self.access_index[keyword] = []
            self.access_index[keyword].append(item.id)

        # Update time index
        date_key = item.created_at.strftime('%Y-%m-%d')
        if date_key not in self.time_index:
            self.time_index[date_key] = []
        self.time_index[date_key].append(item.id)

    def _filter_by_time_range(self, time_range: Tuple[datetime, datetime]) -> set:
        """Filter item IDs by time range"""
        start_date, end_date = time_range
        filtered_ids = set()

        current_date = start_date
        while current_date <= end_date:
            date_key = current_date.strftime('%Y-%m-%d')
            if date_key in self.time_index:
                filtered_ids.update(self.time_index[date_key])
            current_date += timedelta(days=1)

        return filtered_ids

    async def _evict_least_important(self):
        """Evict least important items to make space"""
        if not self.items:
            return

        # Find least important item
        least_important = min(
            self.items.values(),
            key=lambda x: self._calculate_eviction_score(x)
        )

        await self._remove_item(least_important.id)

    def _calculate_eviction_score(self, item: MemoryItem) -> float:
        """Calculate score for eviction (lower = more likely to evict)"""

        # Combine recency, frequency, and importance
        hours_since_access = (datetime.now() - item.last_accessed).total_seconds() / 3600
        recency_penalty = hours_since_access / 24.0  # Days since last access

        frequency_bonus = item.access_count / 10.0
        importance_bonus = item.importance_score

        # Lower score = more likely to evict
        eviction_score = importance_bonus + frequency_bonus - recency_penalty

        return eviction_score

    async def _remove_item(self, item_id: str):
        """Remove item and update indices"""
        if item_id not in self.items:
            return

        item = self.items[item_id]

        # Remove from keyword index
        keywords = item.content.lower().split()
        for keyword in keywords:
            if keyword in self.access_index and item_id in self.access_index[keyword]:
                self.access_index[keyword].remove(item_id)
                if not self.access_index[keyword]:
                    del self.access_index[keyword]

        # Remove from time index
        date_key = item.created_at.strftime('%Y-%m-%d')
        if date_key in self.time_index and item_id in self.time_index[date_key]:
            self.time_index[date_key].remove(item_id)
            if not self.time_index[date_key]:
                del self.time_index[date_key]

        # Remove from main storage
        del self.items[item_id]
```

**Validation:**
```python
# Test working memory tier
from python.helpers.memory_tiers import WorkingMemoryTier, MemoryItem, MemoryQuery, MemoryTier
from datetime import datetime

working_memory = WorkingMemoryTier()

# Create test item
test_item = MemoryItem(
    id="test_001",
    content="Python programming tutorial",
    metadata={"type": "document"},
    tier=MemoryTier.WORKING,
    created_at=datetime.now(),
    last_accessed=datetime.now(),
    access_count=1,
    importance_score=0.8
)

# Store and retrieve
item_id = await working_memory.store(test_item)
query = MemoryQuery(query_text="Python programming", limit=5)
results = await working_memory.retrieve(query)
print(f"Stored item {item_id}, retrieved {len(results)} results")
```

## Step 3.2: Implement Long-term and Episodic Memory Tiers

**File:** `python/helpers/memory_tiers.py`
**Action:** Continue adding to existing file

```python
class LongTermMemoryTier(MemoryTierInterface):
    """
    Long-term Memory: Semantic clusters with consolidated knowledge
    Target: <500ms retrieval
    """

    def __init__(self):
        self.clusters: Dict[str, Dict[str, Any]] = {}  # cluster_id -> cluster_data
        self.item_to_cluster: Dict[str, str] = {}      # item_id -> cluster_id
        self.semantic_index: Dict[str, List[str]] = {} # concept -> cluster_ids
        self.consolidation_threshold = 10  # Items needed to form cluster

    async def store(self, item: MemoryItem) -> str:
        """Store item in long-term memory with semantic clustering"""

        # Find or create appropriate cluster
        cluster_id = await self._find_or_create_cluster(item)

        # Add item to cluster
        if cluster_id not in self.clusters:
            self.clusters[cluster_id] = {
                'items': {},
                'consolidated_summary': '',
                'concepts': set(),
                'created_at': datetime.now(),
                'last_updated': datetime.now()
            }

        self.clusters[cluster_id]['items'][item.id] = item
        self.clusters[cluster_id]['last_updated'] = datetime.now()
        self.item_to_cluster[item.id] = cluster_id

        # Update semantic concepts
        await self._update_cluster_concepts(cluster_id, item)

        # Consolidate cluster if it has enough items
        if len(self.clusters[cluster_id]['items']) >= self.consolidation_threshold:
            await self._consolidate_cluster(cluster_id)

        return item.id

    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Retrieve from long-term memory using semantic clustering"""
        start_time = time.time()

        # Find relevant clusters
        relevant_clusters = await self._find_relevant_clusters(query)

        candidate_items = []

        # Collect items from relevant clusters
        for cluster_id, relevance_score in relevant_clusters:
            cluster = self.clusters[cluster_id]

            for item in cluster['items'].values():
                if item.importance_score >= query.importance_threshold:
                    # Boost item score based on cluster relevance
                    item_score = self._calculate_semantic_relevance(item, query) * relevance_score
                    candidate_items.append((item, item_score))

        # Sort by relevance score
        candidate_items.sort(key=lambda x: x[1], reverse=True)

        # Update access statistics
        result_items = []
        for item, score in candidate_items[:query.limit]:
            await self.update_access(item.id)
            result_items.append(item)

        processing_time = (time.time() - start_time) * 1000

        # Check performance target
        if processing_time > classification_config.performance_targets.long_term_memory_response_ms:
            print(f"Warning: Long-term memory query took {processing_time:.2f}ms, "
                  f"target is {classification_config.performance_targets.long_term_memory_response_ms}ms")

        return result_items

    async def update_access(self, item_id: str) -> None:
        """Update access statistics"""
        cluster_id = self.item_to_cluster.get(item_id)
        if cluster_id and cluster_id in self.clusters:
            item = self.clusters[cluster_id]['items'].get(item_id)
            if item:
                item.last_accessed = datetime.now()
                item.access_count += 1

    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get long-term memory statistics"""
        total_items = sum(len(cluster['items']) for cluster in self.clusters.values())
        consolidated_clusters = sum(1 for cluster in self.clusters.values()
                                  if cluster['consolidated_summary'])

        return {
            'tier': 'long_term',
            'cluster_count': len(self.clusters),
            'total_items': total_items,
            'consolidated_clusters': consolidated_clusters,
            'consolidation_rate': consolidated_clusters / len(self.clusters) if self.clusters else 0,
            'avg_cluster_size': total_items / len(self.clusters) if self.clusters else 0,
            'semantic_concepts': len(self.semantic_index)
        }

    async def cleanup_expired(self) -> int:
        """Clean up old, unused clusters"""
        cutoff_date = datetime.now() - timedelta(days=90)  # 90 days for long-term
        removed_items = 0

        clusters_to_remove = []
        for cluster_id, cluster in self.clusters.items():
            if cluster['last_updated'] < cutoff_date:
                # Check if cluster has low-importance items
                avg_importance = sum(item.importance_score for item in cluster['items'].values()) / len(cluster['items'])
                if avg_importance < 0.3:  # Low importance threshold
                    clusters_to_remove.append(cluster_id)
                    removed_items += len(cluster['items'])

        # Remove old clusters
        for cluster_id in clusters_to_remove:
            await self._remove_cluster(cluster_id)

        return removed_items

    async def _find_or_create_cluster(self, item: MemoryItem) -> str:
        """Find existing cluster or create new one for item"""

        # Extract concepts from item
        item_concepts = self._extract_concepts(item.content)

        # Find best matching cluster
        best_cluster = None
        best_similarity = 0.0

        for cluster_id, cluster in self.clusters.items():
            similarity = self._calculate_cluster_similarity(item_concepts, cluster['concepts'])
            if similarity > best_similarity and similarity > 0.6:  # Similarity threshold
                best_similarity = similarity
                best_cluster = cluster_id

        if best_cluster:
            return best_cluster
        else:
            # Create new cluster
            new_cluster_id = f"cluster_{len(self.clusters):04d}_{int(time.time())}"
            return new_cluster_id

    def _extract_concepts(self, content: str) -> set:
        """Extract semantic concepts from content"""
        # Simple concept extraction (in production, use more sophisticated NLP)
        words = content.lower().split()

        # Filter out common words and extract meaningful concepts
        stopwords = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        concepts = set()

        for word in words:
            if len(word) > 3 and word not in stopwords:
                concepts.add(word)

        return concepts

    def _calculate_cluster_similarity(self, item_concepts: set, cluster_concepts: set) -> float:
        """Calculate similarity between item concepts and cluster concepts"""
        if not item_concepts or not cluster_concepts:
            return 0.0

        intersection = item_concepts.intersection(cluster_concepts)
        union = item_concepts.union(cluster_concepts)

        return len(intersection) / len(union) if union else 0.0

    async def _update_cluster_concepts(self, cluster_id: str, item: MemoryItem):
        """Update cluster concepts with new item"""
        item_concepts = self._extract_concepts(item.content)
        self.clusters[cluster_id]['concepts'].update(item_concepts)

        # Update semantic index
        for concept in item_concepts:
            if concept not in self.semantic_index:
                self.semantic_index[concept] = []
            if cluster_id not in self.semantic_index[concept]:
                self.semantic_index[concept].append(cluster_id)

    async def _consolidate_cluster(self, cluster_id: str):
        """Consolidate cluster items into summary"""
        cluster = self.clusters[cluster_id]

        if cluster['consolidated_summary']:
            return  # Already consolidated

        # Create consolidated summary from cluster items
        items = list(cluster['items'].values())
        contents = [item.content for item in items]

        # Simple consolidation (in production, use LLM summarization)
        summary = f"Consolidated knowledge from {len(items)} items about: {', '.join(list(cluster['concepts'])[:5])}"
        cluster['consolidated_summary'] = summary

    async def _find_relevant_clusters(self, query: MemoryQuery) -> List[Tuple[str, float]]:
        """Find clusters relevant to query"""
        query_concepts = self._extract_concepts(query.query_text)
        cluster_scores = {}

        # Score clusters based on concept overlap
        for concept in query_concepts:
            if concept in self.semantic_index:
                for cluster_id in self.semantic_index[concept]:
                    if cluster_id not in cluster_scores:
                        cluster_scores[cluster_id] = 0.0
                    cluster_scores[cluster_id] += 1.0

        # Normalize scores
        for cluster_id in cluster_scores:
            cluster_concepts = self.clusters[cluster_id]['concepts']
            cluster_scores[cluster_id] = cluster_scores[cluster_id] / len(cluster_concepts) if cluster_concepts else 0.0

        # Sort by relevance
        relevant_clusters = sorted(cluster_scores.items(), key=lambda x: x[1], reverse=True)

        return relevant_clusters[:10]  # Top 10 clusters

    def _calculate_semantic_relevance(self, item: MemoryItem, query: MemoryQuery) -> float:
        """Calculate semantic relevance of item to query"""
        item_concepts = self._extract_concepts(item.content)
        query_concepts = self._extract_concepts(query.query_text)

        if not item_concepts or not query_concepts:
            return 0.0

        intersection = item_concepts.intersection(query_concepts)
        union = item_concepts.union(query_concepts)

        semantic_score = len(intersection) / len(union) if union else 0.0

        # Boost by importance and recency
        importance_boost = item.importance_score
        recency_boost = max(0.1, 1.0 - (datetime.now() - item.last_accessed).days / 30.0)

        return semantic_score * (0.6 + 0.2 * importance_boost + 0.2 * recency_boost)

    async def _remove_cluster(self, cluster_id: str):
        """Remove cluster and update indices"""
        if cluster_id not in self.clusters:
            return

        cluster = self.clusters[cluster_id]

        # Remove items from item_to_cluster mapping
        for item_id in cluster['items']:
            if item_id in self.item_to_cluster:
                del self.item_to_cluster[item_id]

        # Remove from semantic index
        for concept in cluster['concepts']:
            if concept in self.semantic_index and cluster_id in self.semantic_index[concept]:
                self.semantic_index[concept].remove(cluster_id)
                if not self.semantic_index[concept]:
                    del self.semantic_index[concept]

        # Remove cluster
        del self.clusters[cluster_id]

class EpisodicMemoryTier(MemoryTierInterface):
    """
    Episodic Memory: Temporal events with time-based indexing
    Target: <1s for date-range queries
    """

    def __init__(self):
        self.episodes: Dict[str, MemoryItem] = {}
        self.temporal_index: Dict[str, List[str]] = {}  # date -> episode_ids
        self.event_chains: Dict[str, List[str]] = {}    # chain_id -> episode_ids
        self.location_index: Dict[str, List[str]] = {}  # location -> episode_ids

    async def store(self, item: MemoryItem) -> str:
        """Store episodic memory with temporal indexing"""

        # Store the episode
        self.episodes[item.id] = item

        # Update temporal index
        date_key = item.created_at.strftime('%Y-%m-%d')
        if date_key not in self.temporal_index:
            self.temporal_index[date_key] = []
        self.temporal_index[date_key].append(item.id)

        # Detect and link event chains
        await self._link_to_event_chains(item)

        # Update location index if location metadata exists
        location = item.metadata.get('location')
        if location:
            if location not in self.location_index:
                self.location_index[location] = []
            self.location_index[location].append(item.id)

        return item.id

    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Retrieve episodic memories with temporal filtering"""
        start_time = time.time()

        candidate_episodes = []

        # Filter by time range if specified
        if query.time_range:
            episode_ids = self._get_episodes_in_time_range(query.time_range)
        else:
            episode_ids = list(self.episodes.keys())

        # Filter and score episodes
        for episode_id in episode_ids:
            if episode_id in self.episodes:
                episode = self.episodes[episode_id]

                if episode.importance_score >= query.importance_threshold:
                    relevance_score = self._calculate_temporal_relevance(episode, query)
                    candidate_episodes.append((episode, relevance_score))

        # Sort by temporal relevance
        candidate_episodes.sort(key=lambda x: x[1], reverse=True)

        # Update access statistics
        result_episodes = []
        for episode, score in candidate_episodes[:query.limit]:
            await self.update_access(episode.id)
            result_episodes.append(episode)

        processing_time = (time.time() - start_time) * 1000

        # Check performance target
        if processing_time > classification_config.performance_targets.episodic_memory_response_ms:
            print(f"Warning: Episodic memory query took {processing_time:.2f}ms, "
                  f"target is {classification_config.performance_targets.episodic_memory_response_ms}ms")

        return result_episodes

    async def update_access(self, item_id: str) -> None:
        """Update access statistics"""
        if item_id in self.episodes:
            episode = self.episodes[item_id]
            episode.last_accessed = datetime.now()
            episode.access_count += 1

    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get episodic memory statistics"""
        if not self.episodes:
            return {
                'tier': 'episodic',
                'episode_count': 0,
                'date_range_days': 0,
                'event_chains': 0,
                'locations': 0
            }

        episodes = list(self.episodes.values())
        oldest_episode = min(episodes, key=lambda x: x.created_at)
        newest_episode = max(episodes, key=lambda x: x.created_at)

        date_range_days = (newest_episode.created_at - oldest_episode.created_at).days

        return {
            'tier': 'episodic',
            'episode_count': len(self.episodes),
            'date_range_days': date_range_days,
            'event_chains': len(self.event_chains),
            'locations': len(self.location_index),
            'temporal_index_size': len(self.temporal_index)
        }

    async def cleanup_expired(self) -> int:
        """Clean up very old episodes"""
        cutoff_date = datetime.now() - timedelta(days=365)  # 1 year for episodic
        expired_ids = []

        for episode_id, episode in self.episodes.items():
            if episode.created_at < cutoff_date and episode.importance_score < 0.2:
                expired_ids.append(episode_id)

        # Remove expired episodes
        for episode_id in expired_ids:
            await self._remove_episode(episode_id)

        return len(expired_ids)

    def _get_episodes_in_time_range(self, time_range: Tuple[datetime, datetime]) -> List[str]:
        """Get episode IDs within time range"""
        start_date, end_date = time_range
        episode_ids = []

        current_date = start_date
        while current_date <= end_date:
            date_key = current_date.strftime('%Y-%m-%d')
            if date_key in self.temporal_index:
                episode_ids.extend(self.temporal_index[date_key])
            current_date += timedelta(days=1)

        return episode_ids

    def _calculate_temporal_relevance(self, episode: MemoryItem, query: MemoryQuery) -> float:
        """Calculate temporal relevance score"""

        # Base relevance from content similarity
        content_relevance = self._simple_text_similarity(episode.content, query.query_text)

        # Temporal proximity bonus if time range specified
        temporal_bonus = 0.0
        if query.time_range:
            start_date, end_date = query.time_range
            if start_date <= episode.created_at <= end_date:
                # Bonus for being in exact time range
                temporal_bonus = 0.3

        # Recency bonus (more recent episodes get slight boost)
        days_ago = (datetime.now() - episode.created_at).days
        recency_bonus = max(0.0, 0.1 * (1.0 - days_ago / 365.0))  # Decay over 1 year

        # Importance bonus
        importance_bonus = episode.importance_score * 0.2

        total_relevance = content_relevance + temporal_bonus + recency_bonus + importance_bonus

        return min(1.0, total_relevance)

    def _simple_text_similarity(self, text1: str, text2: str) -> float:
        """Simple text similarity calculation"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    async def _link_to_event_chains(self, episode: MemoryItem):
        """Link episode to event chains based on temporal and semantic similarity"""

        # Find recent episodes that might be part of the same event chain
        recent_cutoff = episode.created_at - timedelta(hours=24)  # 24-hour window

        related_episodes = []
        for existing_id, existing_episode in self.episodes.items():
            if (existing_episode.created_at >= recent_cutoff and
                existing_episode.created_at < episode.created_at):

                # Check semantic similarity
                similarity = self._simple_text_similarity(episode.content, existing_episode.content)
                if similarity > 0.3:  # Similarity threshold
                    related_episodes.append((existing_id, similarity))

        if related_episodes:
            # Find the best matching episode and its chain
            best_episode_id, best_similarity = max(related_episodes, key=lambda x: x[1])

            # Find which chain the best episode belongs to
            chain_id = None
            for cid, episode_ids in self.event_chains.items():
                if best_episode_id in episode_ids:
                    chain_id = cid
                    break

            if chain_id:
                # Add to existing chain
                self.event_chains[chain_id].append(episode.id)
            else:
                # Create new chain
                new_chain_id = f"chain_{len(self.event_chains):04d}_{int(time.time())}"
                self.event_chains[new_chain_id] = [best_episode_id, episode.id]

    async def _remove_episode(self, episode_id: str):
        """Remove episode and update indices"""
        if episode_id not in self.episodes:
            return

        episode = self.episodes[episode_id]

        # Remove from temporal index
        date_key = episode.created_at.strftime('%Y-%m-%d')
        if date_key in self.temporal_index and episode_id in self.temporal_index[date_key]:
            self.temporal_index[date_key].remove(episode_id)
            if not self.temporal_index[date_key]:
                del self.temporal_index[date_key]

        # Remove from location index
        location = episode.metadata.get('location')
        if location and location in self.location_index and episode_id in self.location_index[location]:
            self.location_index[location].remove(episode_id)
            if not self.location_index[location]:
                del self.location_index[location]

        # Remove from event chains
        chains_to_update = []
        for chain_id, episode_ids in self.event_chains.items():
            if episode_id in episode_ids:
                chains_to_update.append(chain_id)

        for chain_id in chains_to_update:
            self.event_chains[chain_id].remove(episode_id)
            if len(self.event_chains[chain_id]) <= 1:
                del self.event_chains[chain_id]

        # Remove episode
        del self.episodes[episode_id]
```

**Validation:**
```python
# Test all memory tiers
from python.helpers.memory_tiers import WorkingMemoryTier, LongTermMemoryTier, EpisodicMemoryTier
from python.helpers.memory_tiers import MemoryItem, MemoryQuery, MemoryTier
from datetime import datetime

# Test working memory
working = WorkingMemoryTier()
long_term = LongTermMemoryTier()
episodic = EpisodicMemoryTier()

# Create test items for each tier
test_items = [
    MemoryItem("w1", "Recent Python code", {}, MemoryTier.WORKING, datetime.now(), datetime.now(), 1, 0.8),
    MemoryItem("lt1", "Machine learning concepts", {}, MemoryTier.LONG_TERM, datetime.now(), datetime.now(), 5, 0.9),
    MemoryItem("ep1", "Meeting with client yesterday", {"location": "office"}, MemoryTier.EPISODIC, datetime.now(), datetime.now(), 1, 0.7)
]

# Store in appropriate tiers
await working.store(test_items[0])
await long_term.store(test_items[1])
await episodic.store(test_items[2])

print("All memory tiers tested successfully")
```

## Step 3.3: Create Hierarchical Memory Manager

**File:** `python/helpers/hierarchical_memory_manager.py`
**Action:** Create new file

```python
"""
Hierarchical Memory Manager - Coordinates all memory tiers
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum

from .memory_tiers import (
    MemoryTierInterface, WorkingMemoryTier, LongTermMemoryTier, EpisodicMemoryTier,
    MemoryItem, MemoryQuery, MemoryTier
)
from .classification_engine import ClassificationResult
from .classification_config import classification_config

class MemoryPromotionStrategy(Enum):
    FREQUENCY_BASED = "frequency"
    IMPORTANCE_BASED = "importance"
    HYBRID = "hybrid"

class HierarchicalMemoryManager:
    """
    Manages the three-tier memory hierarchy with automatic promotion/demotion
    """

    def __init__(self):
        self.working_memory = WorkingMemoryTier()
        self.long_term_memory = LongTermMemoryTier()
        self.episodic_memory = EpisodicMemoryTier()

        self.promotion_strategy = MemoryPromotionStrategy.HYBRID
        self.promotion_thresholds = {
            'working_to_longterm': {'access_count': 5, 'importance': 0.7},
            'working_to_episodic': {'temporal_significance': 0.6},
            'longterm_to_episodic': {'temporal_context': 0.8}
        }

        # Performance tracking
        self.query_stats = {
            'total_queries': 0,
            'tier_hits': {'working': 0, 'long_term': 0, 'episodic': 0},
            'avg_response_times': {'working': 0.0, 'long_term': 0.0, 'episodic': 0.0}
        }

    async def store_memory(
        self,
        content: str,
        metadata: Dict[str, Any],
        classification_result: ClassificationResult
    ) -> str:
        """
        Store memory in appropriate tier based on classification and content analysis
        """

        # Determine initial tier placement
        initial_tier = self._determine_initial_tier(content, metadata, classification_result)

        # Create memory item
        memory_item = MemoryItem(
            id=self._generate_memory_id(),
            content=content,
            metadata=metadata,
            tier=initial_tier,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            importance_score=self._calculate_initial_importance(content, metadata, classification_result)
        )

        # Store in appropriate tier
        if initial_tier == MemoryTier.WORKING:
            item_id = await self.working_memory.store(memory_item)
        elif initial_tier == MemoryTier.LONG_TERM:
            item_id = await self.long_term_memory.store(memory_item)
        else:  # EPISODIC
            item_id = await self.episodic_memory.store(memory_item)

        return item_id

    async def retrieve_memories(
        self,
        query: MemoryQuery,
        search_strategy: str = "hierarchical"
    ) -> List[MemoryItem]:
        """
        Retrieve memories using hierarchical search strategy
        """
        import time
        start_time = time.time()

        self.query_stats['total_queries'] += 1

        if search_strategy == "hierarchical":
            results = await self._hierarchical_search(query)
        elif search_strategy == "parallel":
            results = await self._parallel_search(query)
        elif search_strategy == "tier_specific":
            results = await self._tier_specific_search(query)
        else:
            results = await self._hierarchical_search(query)  # Default

        # Update performance statistics
        total_time = (time.time() - start_time) * 1000
        self._update_query_stats(results, total_time)

        return results

    async def _hierarchical_search(self, query: MemoryQuery) -> List[MemoryItem]:
        """
        Search tiers hierarchically: Working -> Long-term -> Episodic
        """
        results = []
        remaining_limit = query.limit

        # 1. Search working memory first (fastest)
        if remaining_limit > 0:
            working_query = MemoryQuery(
                query_text=query.query_text,
                tier_preference=MemoryTier.WORKING,
                time_range=query.time_range,
                importance_threshold=query.importance_threshold,
                limit=remaining_limit,
                include_metadata=query.include_metadata
            )

            working_results = await self.working_memory.retrieve(working_query)
            results.extend(working_results)
            remaining_limit -= len(working_results)
            self.query_stats['tier_hits']['working'] += len(working_results)

        # 2. Search long-term memory if needed
        if remaining_limit > 0:
            longterm_query = MemoryQuery(
                query_text=query.query_text,
                tier_preference=MemoryTier.LONG_TERM,
                time_range=query.time_range,
                importance_threshold=query.importance_threshold,
                limit=remaining_limit,
                include_metadata=query.include_metadata
            )

            longterm_results = await self.long_term_memory.retrieve(longterm_query)
            results.extend(longterm_results)
            remaining_limit -= len(longterm_results)
            self.query_stats['tier_hits']['long_term'] += len(longterm_results)

        # 3. Search episodic memory if still needed
        if remaining_limit > 0:
            episodic_query = MemoryQuery(
                query_text=query.query_text,
                tier_preference=MemoryTier.EPISODIC,
                time_range=query.time_range,
                importance_threshold=query.importance_threshold,
                limit=remaining_limit,
                include_metadata=query.include_metadata
            )

            episodic_results = await self.episodic_memory.retrieve(episodic_query)
            results.extend(episodic_results)
            self.query_stats['tier_hits']['episodic'] += len(episodic_results)

        return results[:query.limit]

    async def _parallel_search(self, query: MemoryQuery) -> List[MemoryItem]:
        """
        Search all tiers in parallel and merge results
        """

        # Create queries for each tier
        working_query = MemoryQuery(
            query_text=query.query_text,
            tier_preference=MemoryTier.WORKING,
            time_range=query.time_range,
            importance_threshold=query.importance_threshold,
            limit=query.limit,
            include_metadata=query.include_metadata
        )

        longterm_query = MemoryQuery(
            query_text=query.query_text,
            tier_preference=MemoryTier.LONG_TERM,
            time_range=query.time_range,
            importance_threshold=query.importance_threshold,
            limit=query.limit,
            include_metadata=query.include_metadata
        )

        episodic_query = MemoryQuery(
            query_text=query.query_text,
            tier_preference=MemoryTier.EPISODIC,
            time_range=query.time_range,
            importance_threshold=query.importance_threshold,
            limit=query.limit,
            include_metadata=query.include_metadata
        )

        # Execute searches in parallel
        working_task = asyncio.create_task(self.working_memory.retrieve(working_query))
        longterm_task = asyncio.create_task(self.long_term_memory.retrieve(longterm_query))
        episodic_task = asyncio.create_task(self.episodic_memory.retrieve(episodic_query))

        # Wait for all results
        working_results, longterm_results, episodic_results = await asyncio.gather(
            working_task, longterm_task, episodic_task
        )

        # Update statistics
        self.query_stats['tier_hits']['working'] += len(working_results)
        self.query_stats['tier_hits']['long_term'] += len(longterm_results)
        self.query_stats['tier_hits']['episodic'] += len(episodic_results)

        # Merge and rank results
        all_results = working_results + longterm_results + episodic_results

        # Remove duplicates and sort by relevance
        unique_results = self._deduplicate_results(all_results)
        ranked_results = self._rank_merged_results(unique_results, query)

        return ranked_results[:query.limit]

    async def _tier_specific_search(self, query: MemoryQuery) -> List[MemoryItem]:
        """
        Search only the specified tier
        """
        if query.tier_preference == MemoryTier.WORKING:
            results = await self.working_memory.retrieve(query)
            self.query_stats['tier_hits']['working'] += len(results)
        elif query.tier_preference == MemoryTier.LONG_TERM:
            results = await self.long_term_memory.retrieve(query)
            self.query_stats['tier_hits']['long_term'] += len(results)
        elif query.tier_preference == MemoryTier.EPISODIC:
            results = await self.episodic_memory.retrieve(query)
            self.query_stats['tier_hits']['episodic'] += len(results)
        else:
            # Default to hierarchical if no preference
            results = await self._hierarchical_search(query)

        return results

    async def promote_memories(self) -> Dict[str, int]:
        """
        Promote memories between tiers based on access patterns and importance
        """
        promotion_stats = {
            'working_to_longterm': 0,
            'working_to_episodic': 0,
            'longterm_to_episodic': 0
        }

        # Promote from working to long-term memory
        working_stats = await self.working_memory.get_tier_stats()
        if working_stats['item_count'] > 0:
            promoted_count = await self._promote_working_to_longterm()
            promotion_stats['working_to_longterm'] = promoted_count

        # Promote from working to episodic memory
        episodic_promoted = await self._promote_working_to_episodic()
        promotion_stats['working_to_episodic'] = episodic_promoted

        # Promote from long-term to episodic memory
        longterm_episodic_promoted = await self._promote_longterm_to_episodic()
        promotion_stats['longterm_to_episodic'] = longterm_episodic_promoted

        return promotion_stats

    async def cleanup_expired_memories(self) -> Dict[str, int]:
        """
        Clean up expired memories across all tiers
        """
        cleanup_stats = {}

        # Cleanup each tier
        working_removed = await self.working_memory.cleanup_expired()
        longterm_removed = await self.long_term_memory.cleanup_expired()
        episodic_removed = await self.episodic_memory.cleanup_expired()

        cleanup_stats = {
            'working_memory_removed': working_removed,
            'long_term_memory_removed': longterm_removed,
            'episodic_memory_removed': episodic_removed,
            'total_removed': working_removed + longterm_removed + episodic_removed
        }

        return cleanup_stats

    async def get_memory_hierarchy_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics for the memory hierarchy
        """
        working_stats = await self.working_memory.get_tier_stats()
        longterm_stats = await self.long_term_memory.get_tier_stats()
        episodic_stats = await self.episodic_memory.get_tier_stats()

        total_items = (working_stats.get('item_count', 0) +
                      longterm_stats.get('total_items', 0) +
                      episodic_stats.get('episode_count', 0))

        return {
            'hierarchy_overview': {
                'total_items': total_items,
                'working_memory': working_stats,
                'long_term_memory': longterm_stats,
                'episodic_memory': episodic_stats
            },
            'query_performance': self.query_stats,
            'tier_distribution': {
                'working_percentage': (working_stats.get('item_count', 0) / total_items * 100) if total_items > 0 else 0,
                'longterm_percentage': (longterm_stats.get('total_items', 0) / total_items * 100) if total_items > 0 else 0,
                'episodic_percentage': (episodic_stats.get('episode_count', 0) / total_items * 100) if total_items > 0 else 0
            }
        }

    def _determine_initial_tier(
        self,
        content: str,
        metadata: Dict[str, Any],
        classification_result: ClassificationResult
    ) -> MemoryTier:
        """
        Determine which tier to initially store the memory in
        """

        # Check for temporal/episodic indicators
        temporal_indicators = ['yesterday', 'today', 'meeting', 'event', 'happened', 'occurred']
        if any(indicator in content.lower() for indicator in temporal_indicators):
            return MemoryTier.EPISODIC

        # Check for high importance or knowledge content
        if (classification_result.confidence > 0.8 or
            metadata.get('content_type') == 'knowledge_document'):
            return MemoryTier.LONG_TERM

        # Default to working memory for recent, active content
        return MemoryTier.WORKING

    def _calculate_initial_importance(
        self,
        content: str,
        metadata: Dict[str, Any],
        classification_result: ClassificationResult
    ) -> float:
        """
        Calculate initial importance score for memory item
        """

        # Base importance from classification confidence
        base_importance = classification_result.confidence

        # Boost for knowledge documents
        if metadata.get('content_type') == 'knowledge_document':
            base_importance += 0.2

        # Boost for user-marked important content
        if metadata.get('user_marked_important', False):
            base_importance += 0.3

        # Boost for content with many entities
        entity_count = len(classification_result.metrics.__dict__.get('entities', []))
        entity_boost = min(0.2, entity_count * 0.02)

        total_importance = min(1.0, base_importance + entity_boost)

        return total_importance

    def _generate_memory_id(self) -> str:
        """Generate unique memory ID"""
        import uuid
        return f"mem_{uuid.uuid4().hex[:12]}"

    def _deduplicate_results(self, results: List[MemoryItem]) -> List[MemoryItem]:
        """Remove duplicate results based on content similarity"""
        unique_results = []
        seen_content = set()

        for result in results:
            # Simple deduplication based on content hash
            content_hash = hash(result.content[:100])  # First 100 chars
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                unique_results.append(result)

        return unique_results

    def _rank_merged_results(self, results: List[MemoryItem], query: MemoryQuery) -> List[MemoryItem]:
        """Rank merged results from multiple tiers"""

        def calculate_ranking_score(item: MemoryItem) -> float:
            # Tier preference bonus
            tier_bonus = 0.0
            if item.tier == MemoryTier.WORKING:
                tier_bonus = 0.3  # Recent items get bonus
            elif item.tier == MemoryTier.LONG_TERM:
                tier_bonus = 0.2  # Knowledge items get moderate bonus
            else:  # EPISODIC
                tier_bonus = 0.1  # Episodic items get small bonus

            # Importance and access frequency
            importance_score = item.importance_score
            frequency_score = min(1.0, item.access_count / 10.0)

            # Recency score
            hours_since_access = (datetime.now() - item.last_accessed).total_seconds() / 3600
            recency_score = max(0.1, 1.0 - (hours_since_access / (24 * 7)))  # 7-day decay

            total_score = (
                0.3 * importance_score +
                0.2 * frequency_score +
                0.2 * recency_score +
                0.3 * tier_bonus
            )

            return total_score

        # Sort by ranking score
        results.sort(key=calculate_ranking_score, reverse=True)

        return results

    def _update_query_stats(self, results: List[MemoryItem], response_time_ms: float):
        """Update query performance statistics"""

        # Update tier hit distribution
        tier_counts = {'working': 0, 'long_term': 0, 'episodic': 0}
        for result in results:
            if result.tier == MemoryTier.WORKING:
                tier_counts['working'] += 1
            elif result.tier == MemoryTier.LONG_TERM:
                tier_counts['long_term'] += 1
            else:
                tier_counts['episodic'] += 1

        # Update average response times (simple moving average)
        total_queries = self.query_stats['total_queries']
        for tier in ['working', 'long_term', 'episodic']:
            if tier_counts[tier] > 0:
                current_avg = self.query_stats['avg_response_times'][tier]
                new_avg = ((current_avg * (total_queries - 1)) + response_time_ms) / total_queries
                self.query_stats['avg_response_times'][tier] = new_avg

    # Promotion methods (simplified implementations)
    async def _promote_working_to_longterm(self) -> int:
        """Promote frequently accessed working memory items to long-term"""
        # Implementation would analyze working memory items and promote based on thresholds
        return 0  # Placeholder

    async def _promote_working_to_episodic(self) -> int:
        """Promote temporal working memory items to episodic"""
        # Implementation would identify temporal content and promote
        return 0  # Placeholder

    async def _promote_longterm_to_episodic(self) -> int:
        """Promote temporal long-term items to episodic"""
        # Implementation would identify temporal context in long-term memory
        return 0  # Placeholder
```

**Validation:**
```python
# Test hierarchical memory manager
from python.helpers.hierarchical_memory_manager import HierarchicalMemoryManager
from python.helpers.memory_tiers import MemoryQuery
from python.helpers.classification_engine import ClassificationResult, ClassificationMetrics
from python.helpers.classification_config import ClassificationStrategy

manager = HierarchicalMemoryManager()

# Create mock classification result
mock_metrics = ClassificationMetrics(
    semantic_overlap=0.8, entity_confidence=0.9, domain_specificity=0.6,
    relationship_density=0.7, privacy_score=1, interconnectedness=0.8,
    temporal_relevance=0.9, related_entities_count=5
)

mock_result = ClassificationResult(
    strategy=ClassificationStrategy.SHARED_ONTOLOGY,
    confidence=0.85,
    metrics=mock_metrics,
    reasoning="Test classification",
    processing_time_ms=45.0
)

# Store test memory
memory_id = await manager.store_memory(
    "Python programming tutorial",
    {"content_type": "knowledge_document"},
    mock_result
)

# Retrieve memories
query = MemoryQuery(query_text="Python programming", limit=5)
results = await manager.retrieve_memories(query)

print(f"Stored memory {memory_id}, retrieved {len(results)} results")

# Get hierarchy stats
stats = await manager.get_memory_hierarchy_stats()
print(f"Total items in hierarchy: {stats['hierarchy_overview']['total_items']}")
```

---

**End of Phase 3 Hierarchical Memory System**

✅ **Phase 3 Complete - Validation Checklist:**
- [ ] All three memory tiers (working, long-term, episodic) are implemented
- [ ] Hierarchical memory manager coordinates tier operations
- [ ] Memory promotion/demotion logic is in place
- [ ] Performance targets are monitored and enforced
- [ ] Comprehensive statistics and monitoring are available
- [ ] Memory cleanup and maintenance functions work

**Next:** Proceed to Phase 4 - User Preference Framework

---

# Phase 4: User Preference Framework

## Step 4.1: Create User Preference System

**File:** `python/helpers/user_preferences.py`
**Action:** Create new file

```python
"""
Adaptive User Preference Framework with Behavioral Learning
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import asyncio

from .classification_config import classification_config

class PreferenceType(Enum):
    DOMAIN_WEIGHT = "domain_weight"
    PRIVACY_SENSITIVITY = "privacy_sensitivity"
    CLASSIFICATION_THRESHOLD = "classification_threshold"
    MEMORY_RETENTION = "memory_retention"
    SEARCH_PREFERENCE = "search_preference"

class LearningSignal(Enum):
    POSITIVE_FEEDBACK = "positive"
    NEGATIVE_FEEDBACK = "negative"
    IMPLICIT_USAGE = "implicit"
    EXPLICIT_SETTING = "explicit"

@dataclass
class PreferenceItem:
    """Individual preference item with learning history"""
    preference_type: PreferenceType
    key: str
    value: Any
    confidence: float
    last_updated: datetime
    learning_history: List[Dict[str, Any]] = field(default_factory=list)
    user_explicit: bool = False

@dataclass
class UserFeedback:
    """User feedback for preference learning"""
    action: str
    context: Dict[str, Any]
    feedback_type: LearningSignal
    timestamp: datetime
    value: Any

class UserPreferenceManager:
    """
    Manages user preferences with adaptive learning capabilities
    """

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.preferences: Dict[str, PreferenceItem] = {}
        self.feedback_history: List[UserFeedback] = []
        self.learning_rate = classification_config.preference_learning_rate
        self.adaptation_enabled = classification_config.enable_adaptive_learning

        # Initialize default preferences
        self._initialize_default_preferences()

    def _initialize_default_preferences(self):
        """Initialize default user preferences"""

        default_prefs = {
            # Domain weights
            'domain_weight_programming': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'programming', 1.0, 0.5, datetime.now()
            ),
            'domain_weight_documentation': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'documentation', 0.8, 0.5, datetime.now()
            ),
            'domain_weight_conversations': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'conversations', 0.6, 0.5, datetime.now()
            ),
            'domain_weight_general': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'general', 0.4, 0.5, datetime.now()
            ),

            # Privacy sensitivity
            'privacy_sensitivity_level': PreferenceItem(
                PreferenceType.PRIVACY_SENSITIVITY, 'level', 'medium', 0.7, datetime.now()
            ),
            'privacy_auto_isolation': PreferenceItem(
                PreferenceType.PRIVACY_SENSITIVITY, 'auto_isolation', True, 0.8, datetime.now()
            ),

            # Classification thresholds
            'classification_confidence_threshold': PreferenceItem(
                PreferenceType.CLASSIFICATION_THRESHOLD, 'confidence', 0.7, 0.6, datetime.now()
            ),
            'classification_manual_review_threshold': PreferenceItem(
                PreferenceType.CLASSIFICATION_THRESHOLD, 'manual_review', 0.6, 0.6, datetime.now()
            ),

            # Memory retention preferences
            'memory_working_retention_days': PreferenceItem(
                PreferenceType.MEMORY_RETENTION, 'working_days', 7, 0.8, datetime.now()
            ),
            'memory_importance_threshold': PreferenceItem(
                PreferenceType.MEMORY_RETENTION, 'importance_threshold', 0.5, 0.7, datetime.now()
            ),

            # Search preferences
            'search_strategy_preference': PreferenceItem(
                PreferenceType.SEARCH_PREFERENCE, 'strategy', 'hierarchical', 0.6, datetime.now()
            ),
            'search_result_limit': PreferenceItem(
                PreferenceType.SEARCH_PREFERENCE, 'result_limit', 10, 0.8, datetime.now()
            )
        }

        self.preferences.update(default_prefs)

    async def get_preference(self, preference_key: str, default_value: Any = None) -> Any:
        """Get user preference value"""
        if preference_key in self.preferences:
            return self.preferences[preference_key].value
        return default_value

    async def set_preference(
        self,
        preference_key: str,
        value: Any,
        preference_type: PreferenceType,
        user_explicit: bool = True
    ) -> bool:
        """Set user preference explicitly"""

        try:
            if preference_key in self.preferences:
                # Update existing preference
                pref = self.preferences[preference_key]
                pref.value = value
                pref.last_updated = datetime.now()
                pref.user_explicit = user_explicit
                if user_explicit:
                    pref.confidence = 1.0  # High confidence for explicit settings
            else:
                # Create new preference
                self.preferences[preference_key] = PreferenceItem(
                    preference_type=preference_type,
                    key=preference_key,
                    value=value,
                    confidence=1.0 if user_explicit else 0.5,
                    last_updated=datetime.now(),
                    user_explicit=user_explicit
                )

            # Record the change
            await self._record_preference_change(preference_key, value, user_explicit)

            return True

        except Exception as e:
            print(f"Error setting preference {preference_key}: {e}")
            return False
