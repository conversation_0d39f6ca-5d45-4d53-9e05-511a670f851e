# Phase 3: Memory Hierarchy - Memory Tiers
## Three-Tier Memory System Implementation

This document provides implementation for the three-tier hierarchical memory system with working, long-term, and episodic memory tiers.

## 🏗️ Memory Tier Architecture

### Performance Targets
- **Working Memory**: <100ms response time (95th percentile)
- **Long-term Memory**: <500ms response time (95th percentile)  
- **Episodic Memory**: <1s response time (90th percentile)

### Tier Characteristics
1. **Working Memory**: Recent, frequently accessed items
2. **Long-term Memory**: Semantic clusters with consolidated knowledge
3. **Episodic Memory**: Temporal events with time-based indexing

## 🔧 Implementation

### Step 3.1: Core Memory Tier Framework

**File:** `python/helpers/memory_tiers.py`
**Action:** Create new file

```python
"""
Three-tier hierarchical memory system implementation
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import asyncio
import time
import json
import hashlib
from collections import deque, defaultdict

from .classification_config import classification_config

class MemoryTier(Enum):
    """Memory tier types"""
    WORKING = "working"
    LONG_TERM = "long_term"
    EPISODIC = "episodic"

@dataclass
class MemoryItem:
    """Individual memory item with metadata"""
    id: str
    content: Any
    metadata: Dict[str, Any]
    tier: MemoryTier
    created_at: datetime
    last_accessed: datetime
    access_count: int
    importance_score: float
    
    # Additional tracking
    size_bytes: int = 0
    tags: List[str] = field(default_factory=list)
    relationships: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if self.size_bytes == 0:
            self.size_bytes = len(str(self.content).encode('utf-8'))

@dataclass
class MemoryQuery:
    """Query for memory retrieval"""
    query_text: str
    tier_preference: Optional[MemoryTier] = None
    time_range: Optional[Tuple[datetime, datetime]] = None
    importance_threshold: float = 0.0
    limit: int = 10
    include_metadata: bool = True
    
    # Advanced query options
    semantic_similarity_threshold: float = 0.5
    exact_match: bool = False
    fuzzy_search: bool = True

class MemoryTierInterface(ABC):
    """Abstract interface for memory tiers"""
    
    @abstractmethod
    async def store(self, item: MemoryItem) -> str:
        """Store memory item and return item ID"""
        pass
    
    @abstractmethod
    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Retrieve memory items matching query"""
        pass
    
    @abstractmethod
    async def update_access(self, item_id: str) -> None:
        """Update access statistics for item"""
        pass
    
    @abstractmethod
    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get tier statistics"""
        pass
    
    @abstractmethod
    async def cleanup_expired(self) -> int:
        """Clean up expired items and return count removed"""
        pass

class WorkingMemoryTier(MemoryTierInterface):
    """
    Working Memory: Recent, frequently accessed items
    Target: <100ms response time
    """
    
    def __init__(self, max_items: int = 50000, retention_days: int = 7):
        self.max_items = max_items
        self.retention_days = retention_days
        self.items: Dict[str, MemoryItem] = {}
        self.access_order: deque = deque()  # For LRU eviction
        self.importance_index: Dict[float, Set[str]] = defaultdict(set)
        
        # Performance tracking
        self.query_count = 0
        self.hit_count = 0
        self.total_response_time = 0.0
    
    async def store(self, item: MemoryItem) -> str:
        """Store item in working memory with capacity management"""
        
        # Check if we need to evict items
        if len(self.items) >= self.max_items and item.id not in self.items:
            await self._evict_items(1)
        
        # Store the item
        self.items[item.id] = item
        
        # Update access order
        if item.id in self.access_order:
            self.access_order.remove(item.id)
        self.access_order.append(item.id)
        
        # Update importance index
        self.importance_index[item.importance_score].add(item.id)
        
        return item.id
    
    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Retrieve items from working memory"""
        start_time = time.time()
        self.query_count += 1
        
        if not query.query_text.strip():
            return []
        
        candidate_items = []
        query_words = set(query.query_text.lower().split())
        
        # Search through items
        for item in self.items.values():
            # Skip items below importance threshold
            if item.importance_score < query.importance_threshold:
                continue
            
            # Check time range if specified
            if query.time_range:
                start_time_range, end_time_range = query.time_range
                if not (start_time_range <= item.created_at <= end_time_range):
                    continue
            
            # Calculate relevance score
            relevance_score = self._calculate_relevance(item, query_words, query)
            
            if relevance_score > 0:
                candidate_items.append((item, relevance_score))
        
        # Sort by relevance and recency
        candidate_items.sort(key=lambda x: (x[1], x[0].last_accessed), reverse=True)
        
        # Update access statistics for returned items
        result_items = []
        for item, score in candidate_items[:query.limit]:
            await self.update_access(item.id)
            result_items.append(item)
        
        # Track performance
        response_time = (time.time() - start_time) * 1000
        self.total_response_time += response_time
        
        if result_items:
            self.hit_count += 1
        
        # Check performance target
        if response_time > classification_config.performance_targets.working_memory_response_ms:
            print(f"Warning: Working memory query took {response_time:.2f}ms, "
                  f"target is {classification_config.performance_targets.working_memory_response_ms}ms")
        
        return result_items
    
    def _calculate_relevance(self, item: MemoryItem, query_words: Set[str], query: MemoryQuery) -> float:
        """Calculate relevance score for item"""
        
        # Convert content to searchable text
        content_text = str(item.content).lower()
        content_words = set(content_text.split())
        
        # Calculate word overlap
        word_overlap = len(query_words.intersection(content_words))
        if word_overlap == 0:
            return 0.0
        
        # Base relevance from word overlap
        base_relevance = word_overlap / len(query_words)
        
        # Boost for exact matches
        if query.exact_match and query.query_text.lower() in content_text:
            base_relevance += 0.5
        
        # Boost for importance
        importance_boost = item.importance_score * 0.3
        
        # Boost for recency
        hours_since_access = (datetime.now() - item.last_accessed).total_seconds() / 3600
        recency_boost = max(0, 0.2 * (1 - hours_since_access / 24))  # Decay over 24 hours
        
        # Boost for access frequency
        frequency_boost = min(0.2, item.access_count * 0.02)
        
        total_relevance = base_relevance + importance_boost + recency_boost + frequency_boost
        
        return min(1.0, total_relevance)
    
    async def update_access(self, item_id: str) -> None:
        """Update access statistics"""
        if item_id in self.items:
            item = self.items[item_id]
            item.last_accessed = datetime.now()
            item.access_count += 1
            
            # Update access order
            if item_id in self.access_order:
                self.access_order.remove(item_id)
            self.access_order.append(item_id)
    
    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get working memory statistics"""
        if not self.items:
            return {
                'tier': 'working',
                'item_count': 0,
                'capacity_utilization': 0.0,
                'avg_importance': 0.0,
                'oldest_item_age_hours': 0,
                'hit_rate': 0.0,
                'avg_response_time_ms': 0.0
            }
        
        items = list(self.items.values())
        avg_importance = sum(item.importance_score for item in items) / len(items)
        
        oldest_item = min(items, key=lambda x: x.created_at)
        oldest_age_hours = (datetime.now() - oldest_item.created_at).total_seconds() / 3600
        
        hit_rate = self.hit_count / self.query_count if self.query_count > 0 else 0.0
        avg_response_time = self.total_response_time / self.query_count if self.query_count > 0 else 0.0
        
        return {
            'tier': 'working',
            'item_count': len(self.items),
            'capacity_utilization': len(self.items) / self.max_items,
            'avg_importance': avg_importance,
            'oldest_item_age_hours': oldest_age_hours,
            'hit_rate': hit_rate,
            'avg_response_time_ms': avg_response_time,
            'total_queries': self.query_count,
            'total_hits': self.hit_count
        }
    
    async def cleanup_expired(self) -> int:
        """Clean up expired items"""
        cutoff_date = datetime.now() - timedelta(days=self.retention_days)
        expired_ids = []
        
        for item_id, item in self.items.items():
            if item.created_at < cutoff_date and item.access_count <= 1:
                expired_ids.append(item_id)
        
        # Remove expired items
        for item_id in expired_ids:
            await self._remove_item(item_id)
        
        return len(expired_ids)
    
    async def _evict_items(self, count: int):
        """Evict least important/recently used items"""
        
        if not self.items:
            return
        
        # Find candidates for eviction (low importance, old access)
        eviction_candidates = []
        
        for item in self.items.values():
            # Score for eviction (lower is more likely to be evicted)
            hours_since_access = (datetime.now() - item.last_accessed).total_seconds() / 3600
            eviction_score = item.importance_score - (hours_since_access / 24) - (item.access_count * 0.1)
            eviction_candidates.append((item.id, eviction_score))
        
        # Sort by eviction score (lowest first)
        eviction_candidates.sort(key=lambda x: x[1])
        
        # Evict the lowest scoring items
        for i in range(min(count, len(eviction_candidates))):
            item_id = eviction_candidates[i][0]
            await self._remove_item(item_id)
    
    async def _remove_item(self, item_id: str):
        """Remove item from working memory"""
        if item_id not in self.items:
            return
        
        item = self.items[item_id]
        
        # Remove from importance index
        self.importance_index[item.importance_score].discard(item_id)
        if not self.importance_index[item.importance_score]:
            del self.importance_index[item.importance_score]
        
        # Remove from access order
        if item_id in self.access_order:
            self.access_order.remove(item_id)
        
        # Remove from main storage
        del self.items[item_id]

class LongTermMemoryTier(MemoryTierInterface):
    """
    Long-term Memory: Semantic clusters with consolidated knowledge
    Target: <500ms retrieval
    """
    
    def __init__(self):
        self.clusters: Dict[str, Dict[str, Any]] = {}  # cluster_id -> cluster_data
        self.item_to_cluster: Dict[str, str] = {}      # item_id -> cluster_id
        self.semantic_index: Dict[str, List[str]] = {} # concept -> cluster_ids
        self.consolidation_threshold = 10  # Items needed to form cluster
        
        # Performance tracking
        self.query_count = 0
        self.total_response_time = 0.0
    
    async def store(self, item: MemoryItem) -> str:
        """Store item in long-term memory with semantic clustering"""
        
        # Find or create appropriate cluster
        cluster_id = await self._find_or_create_cluster(item)
        
        # Add item to cluster
        if cluster_id not in self.clusters:
            self.clusters[cluster_id] = {
                'items': {},
                'consolidated_summary': '',
                'concepts': set(),
                'created_at': datetime.now(),
                'last_updated': datetime.now()
            }
        
        self.clusters[cluster_id]['items'][item.id] = item
        self.clusters[cluster_id]['last_updated'] = datetime.now()
        self.item_to_cluster[item.id] = cluster_id
        
        # Update semantic concepts
        await self._update_cluster_concepts(cluster_id, item)
        
        # Consolidate cluster if it has enough items
        if len(self.clusters[cluster_id]['items']) >= self.consolidation_threshold:
            await self._consolidate_cluster(cluster_id)
        
        return item.id
    
    async def retrieve(self, query: MemoryQuery) -> List[MemoryItem]:
        """Retrieve from long-term memory using semantic clustering"""
        start_time = time.time()
        self.query_count += 1
        
        # Find relevant clusters
        relevant_clusters = await self._find_relevant_clusters(query)
        
        candidate_items = []
        
        # Collect items from relevant clusters
        for cluster_id, relevance_score in relevant_clusters:
            cluster = self.clusters[cluster_id]
            
            for item in cluster['items'].values():
                if item.importance_score >= query.importance_threshold:
                    # Boost item score based on cluster relevance
                    item_score = self._calculate_semantic_relevance(item, query) * relevance_score
                    candidate_items.append((item, item_score))
        
        # Sort by relevance score
        candidate_items.sort(key=lambda x: x[1], reverse=True)
        
        # Update access statistics
        result_items = []
        for item, score in candidate_items[:query.limit]:
            await self.update_access(item.id)
            result_items.append(item)
        
        # Track performance
        processing_time = (time.time() - start_time) * 1000
        self.total_response_time += processing_time
        
        # Check performance target
        if processing_time > classification_config.performance_targets.long_term_memory_response_ms:
            print(f"Warning: Long-term memory query took {processing_time:.2f}ms, "
                  f"target is {classification_config.performance_targets.long_term_memory_response_ms}ms")
        
        return result_items
    
    async def update_access(self, item_id: str) -> None:
        """Update access statistics"""
        cluster_id = self.item_to_cluster.get(item_id)
        if cluster_id and cluster_id in self.clusters:
            item = self.clusters[cluster_id]['items'].get(item_id)
            if item:
                item.last_accessed = datetime.now()
                item.access_count += 1
    
    async def get_tier_stats(self) -> Dict[str, Any]:
        """Get long-term memory statistics"""
        total_items = sum(len(cluster['items']) for cluster in self.clusters.values())
        consolidated_clusters = sum(1 for cluster in self.clusters.values() 
                                  if cluster['consolidated_summary'])
        
        avg_response_time = self.total_response_time / self.query_count if self.query_count > 0 else 0.0
        
        return {
            'tier': 'long_term',
            'cluster_count': len(self.clusters),
            'total_items': total_items,
            'consolidated_clusters': consolidated_clusters,
            'consolidation_rate': consolidated_clusters / len(self.clusters) if self.clusters else 0,
            'avg_cluster_size': total_items / len(self.clusters) if self.clusters else 0,
            'semantic_concepts': len(self.semantic_index),
            'avg_response_time_ms': avg_response_time,
            'total_queries': self.query_count
        }
    
    async def cleanup_expired(self) -> int:
        """Clean up old, unused clusters"""
        cutoff_date = datetime.now() - timedelta(days=90)  # 90 days for long-term
        removed_items = 0
        
        clusters_to_remove = []
        for cluster_id, cluster in self.clusters.items():
            if cluster['last_updated'] < cutoff_date:
                # Check if cluster has low-importance items
                items = list(cluster['items'].values())
                if items:
                    avg_importance = sum(item.importance_score for item in items) / len(items)
                    if avg_importance < 0.3:  # Low importance threshold
                        clusters_to_remove.append(cluster_id)
                        removed_items += len(items)
        
        # Remove old clusters
        for cluster_id in clusters_to_remove:
            await self._remove_cluster(cluster_id)
        
        return removed_items
    
    async def _find_or_create_cluster(self, item: MemoryItem) -> str:
        """Find existing cluster or create new one for item"""
        
        # Extract concepts from item
        item_concepts = self._extract_concepts(item.content)
        
        # Find best matching cluster
        best_cluster = None
        best_similarity = 0.0
        
        for cluster_id, cluster in self.clusters.items():
            similarity = self._calculate_cluster_similarity(item_concepts, cluster['concepts'])
            if similarity > best_similarity and similarity > 0.6:  # Similarity threshold
                best_similarity = similarity
                best_cluster = cluster_id
        
        if best_cluster:
            return best_cluster
        else:
            # Create new cluster
            new_cluster_id = f"cluster_{len(self.clusters):04d}_{int(time.time())}"
            return new_cluster_id
    
    def _extract_concepts(self, content: str) -> set:
        """Extract semantic concepts from content"""
        # Simple concept extraction (in production, use more sophisticated NLP)
        words = str(content).lower().split()
        
        # Filter out common words and extract meaningful concepts
        stopwords = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        concepts = set()
        
        for word in words:
            if len(word) > 3 and word not in stopwords:
                concepts.add(word)
        
        return concepts
    
    def _calculate_cluster_similarity(self, item_concepts: set, cluster_concepts: set) -> float:
        """Calculate similarity between item concepts and cluster concepts"""
        if not item_concepts or not cluster_concepts:
            return 0.0
        
        intersection = item_concepts.intersection(cluster_concepts)
        union = item_concepts.union(cluster_concepts)
        
        return len(intersection) / len(union) if union else 0.0
    
    async def _update_cluster_concepts(self, cluster_id: str, item: MemoryItem):
        """Update cluster concepts with new item"""
        item_concepts = self._extract_concepts(item.content)
        self.clusters[cluster_id]['concepts'].update(item_concepts)
        
        # Update semantic index
        for concept in item_concepts:
            if concept not in self.semantic_index:
                self.semantic_index[concept] = []
            if cluster_id not in self.semantic_index[concept]:
                self.semantic_index[concept].append(cluster_id)
    
    async def _consolidate_cluster(self, cluster_id: str):
        """Consolidate cluster items into summary"""
        cluster = self.clusters[cluster_id]
        
        if cluster['consolidated_summary']:
            return  # Already consolidated
        
        # Create consolidated summary from cluster items
        items = list(cluster['items'].values())
        
        # Simple consolidation (in production, use LLM summarization)
        summary = f"Consolidated knowledge from {len(items)} items about: {', '.join(list(cluster['concepts'])[:5])}"
        cluster['consolidated_summary'] = summary
    
    async def _find_relevant_clusters(self, query: MemoryQuery) -> List[Tuple[str, float]]:
        """Find clusters relevant to query"""
        query_concepts = self._extract_concepts(query.query_text)
        cluster_scores = {}
        
        # Score clusters based on concept overlap
        for concept in query_concepts:
            if concept in self.semantic_index:
                for cluster_id in self.semantic_index[concept]:
                    if cluster_id not in cluster_scores:
                        cluster_scores[cluster_id] = 0.0
                    cluster_scores[cluster_id] += 1.0
        
        # Normalize scores
        for cluster_id in cluster_scores:
            cluster_concepts = self.clusters[cluster_id]['concepts']
            cluster_scores[cluster_id] = cluster_scores[cluster_id] / len(cluster_concepts) if cluster_concepts else 0.0
        
        # Sort by relevance
        relevant_clusters = sorted(cluster_scores.items(), key=lambda x: x[1], reverse=True)
        
        return relevant_clusters[:10]  # Top 10 clusters
    
    def _calculate_semantic_relevance(self, item: MemoryItem, query: MemoryQuery) -> float:
        """Calculate semantic relevance of item to query"""
        item_concepts = self._extract_concepts(item.content)
        query_concepts = self._extract_concepts(query.query_text)
        
        if not item_concepts or not query_concepts:
            return 0.0
        
        intersection = item_concepts.intersection(query_concepts)
        union = item_concepts.union(query_concepts)
        
        semantic_score = len(intersection) / len(union) if union else 0.0
        
        # Boost by importance and recency
        importance_boost = item.importance_score
        recency_boost = max(0.1, 1.0 - (datetime.now() - item.last_accessed).days / 30.0)
        
        return semantic_score * (0.6 + 0.2 * importance_boost + 0.2 * recency_boost)
    
    async def _remove_cluster(self, cluster_id: str):
        """Remove cluster and update indices"""
        if cluster_id not in self.clusters:
            return
        
        cluster = self.clusters[cluster_id]
        
        # Remove items from item_to_cluster mapping
        for item_id in cluster['items']:
            if item_id in self.item_to_cluster:
                del self.item_to_cluster[item_id]
        
        # Remove from semantic index
        for concept in cluster['concepts']:
            if concept in self.semantic_index and cluster_id in self.semantic_index[concept]:
                self.semantic_index[concept].remove(cluster_id)
                if not self.semantic_index[concept]:
                    del self.semantic_index[concept]
        
        # Remove cluster
        del self.clusters[cluster_id]
```

**Validation:**
```python
# Test memory tiers
from python.helpers.memory_tiers import WorkingMemoryTier, LongTermMemoryTier, MemoryItem, MemoryQuery, MemoryTier
from datetime import datetime

# Test working memory
working_memory = WorkingMemoryTier()

test_item = MemoryItem(
    id="test_001",
    content="Python programming tutorial",
    metadata={"type": "document"},
    tier=MemoryTier.WORKING,
    created_at=datetime.now(),
    last_accessed=datetime.now(),
    access_count=1,
    importance_score=0.8
)

# Store and retrieve
item_id = await working_memory.store(test_item)
query = MemoryQuery(query_text="Python programming", limit=5)
results = await working_memory.retrieve(query)
print(f"Working memory: stored {item_id}, retrieved {len(results)} results")

# Test long-term memory
long_term_memory = LongTermMemoryTier()
await long_term_memory.store(test_item)
results = await long_term_memory.retrieve(query)
print(f"Long-term memory: retrieved {len(results)} results")
```

---

**Next Step**: [Working Memory](working-memory.md)
