# Phase 4.3: Classification Integration
## Integrating User Preferences with Classification Engine

### Overview
This step integrates the user preference system with the classification engine to personalize classification decisions based on individual user preferences and learned behaviors.

### Time Estimate: 2-3 hours

---

## Step 4.3.1: Extend Classification Engine with Preference Support

**File:** `python/helpers/intelligent_classification_engine.py`
**Action:** Update existing file

Add these imports at the top:
```python
from .user_preferences import UserPreferenceManager
from .behavioral_analyzer import BehavioralPatternAnalyzer
```

Add this method to the `IntelligentClassificationEngine` class:
```python
async def classify_content_with_preferences(
    self,
    content: str,
    metadata: Dict[str, Any],
    user_id: str,
    context: Optional[Dict[str, Any]] = None
) -> ClassificationResult:
    """
    Classify content using user preferences and behavioral insights
    """
    self.performance_tracker.start_timing()

    try:
        # Get user preference manager
        preference_manager = await self._get_user_preference_manager(user_id)
        
        # Extract entities from content
        entities = await self.extract_entities(content)

        # Calculate base classification metrics
        base_metrics = await self._calculate_classification_metrics(
            content, entities, metadata, context
        )

        # Apply user preference adjustments
        adjusted_metrics = await self._apply_user_preferences(
            base_metrics, preference_manager, content, entities
        )

        # Apply decision matrix with adjusted metrics
        strategy, confidence, reasoning = self._apply_decision_matrix(adjusted_metrics)

        # Record interaction for behavioral learning
        await self._record_classification_interaction(
            user_id, content, entities, strategy, confidence, context
        )

        # Determine namespace/domain if applicable
        namespace, domain = self._determine_storage_location(strategy, adjusted_metrics, metadata)

        processing_time = self.performance_tracker.end_timing()

        result = ClassificationResult(
            strategy=strategy,
            confidence=confidence,
            metrics=adjusted_metrics,
            reasoning=f"{reasoning} (personalized for user {user_id})",
            processing_time_ms=processing_time,
            namespace=namespace,
            domain=domain
        )

        # Record performance metrics
        self.performance_tracker.record_classification(result)

        return result

    except Exception as e:
        processing_time = self.performance_tracker.end_timing()
        # Return default classification on error
        return ClassificationResult(
            strategy=ClassificationStrategy.ISOLATED_NAMESPACE,
            confidence=0.5,
            metrics=ClassificationMetrics(
                semantic_overlap=0.0,
                entity_confidence=0.0,
                domain_specificity=0.0,
                relationship_density=0.0,
                privacy_score=10,
                interconnectedness=0.0,
                temporal_relevance=1.0,
                related_entities_count=0,
                privacy_flags=True
            ),
            reasoning=f"Classification failed, defaulting to isolated storage: {str(e)}",
            processing_time_ms=processing_time,
            namespace="error_isolation"
        )

async def _get_user_preference_manager(self, user_id: str) -> UserPreferenceManager:
    """Get or create user preference manager"""
    if not hasattr(self, '_preference_managers'):
        self._preference_managers = {}
    
    if user_id not in self._preference_managers:
        self._preference_managers[user_id] = UserPreferenceManager(user_id)
    
    return self._preference_managers[user_id]

async def _apply_user_preferences(
    self,
    base_metrics: ClassificationMetrics,
    preference_manager: UserPreferenceManager,
    content: str,
    entities: List[Dict[str, Any]]
) -> ClassificationMetrics:
    """Apply user preferences to adjust classification metrics"""

    # Get user preferences
    domain_weights = await self._get_domain_weights(preference_manager)
    privacy_sensitivity = await preference_manager.get_preference('privacy_sensitivity_level', 'medium')
    confidence_threshold = await preference_manager.get_preference('classification_confidence_threshold', 0.7)

    # Adjust domain specificity based on user domain weights
    adjusted_domain_specificity = await self._adjust_domain_specificity(
        base_metrics.domain_specificity, content, entities, domain_weights
    )

    # Adjust privacy scoring based on user sensitivity
    adjusted_privacy_score, adjusted_privacy_flags = await self._adjust_privacy_scoring(
        base_metrics.privacy_score, base_metrics.privacy_flags, privacy_sensitivity, content
    )

    # Adjust semantic overlap based on user's historical preferences
    adjusted_semantic_overlap = await self._adjust_semantic_overlap(
        base_metrics.semantic_overlap, preference_manager
    )

    # Create adjusted metrics
    adjusted_metrics = ClassificationMetrics(
        semantic_overlap=adjusted_semantic_overlap,
        entity_confidence=base_metrics.entity_confidence,
        domain_specificity=adjusted_domain_specificity,
        relationship_density=base_metrics.relationship_density,
        privacy_score=adjusted_privacy_score,
        interconnectedness=base_metrics.interconnectedness,
        temporal_relevance=base_metrics.temporal_relevance,
        related_entities_count=base_metrics.related_entities_count,
        privacy_flags=adjusted_privacy_flags,
        user_specified_isolation=base_metrics.user_specified_isolation
    )

    return adjusted_metrics

async def _get_domain_weights(self, preference_manager: UserPreferenceManager) -> Dict[str, float]:
    """Get user's domain weight preferences"""
    domain_weights = {}
    
    for domain in ['programming', 'documentation', 'conversations', 'general', 'medical', 'legal', 'finance']:
        weight = await preference_manager.get_preference(f'domain_weight_{domain}', 0.5)
        domain_weights[domain] = weight
    
    return domain_weights

async def _adjust_domain_specificity(
    self,
    base_specificity: float,
    content: str,
    entities: List[Dict[str, Any]],
    domain_weights: Dict[str, float]
) -> float:
    """Adjust domain specificity based on user preferences"""

    # Detect primary domain
    detected_domain = None
    max_domain_score = 0.0
    
    content_lower = content.lower()
    for domain, keywords in self.domain_keywords.items():
        score = sum(1 for keyword in keywords if keyword.lower() in content_lower)
        normalized_score = score / len(keywords)
        
        if normalized_score > max_domain_score:
            max_domain_score = normalized_score
            detected_domain = domain

    # Apply user weight for detected domain
    if detected_domain and detected_domain in domain_weights:
        user_weight = domain_weights[detected_domain]
        # Boost specificity if user prefers this domain
        adjustment_factor = 0.5 + (user_weight * 0.5)  # Range: 0.5 to 1.0
        adjusted_specificity = base_specificity * adjustment_factor
        return min(1.0, adjusted_specificity)

    return base_specificity

async def _adjust_privacy_scoring(
    self,
    base_privacy_score: int,
    base_privacy_flags: bool,
    privacy_sensitivity: str,
    content: str
) -> Tuple[int, bool]:
    """Adjust privacy scoring based on user sensitivity level"""

    sensitivity_multipliers = {
        'low': 0.7,
        'medium': 1.0,
        'high': 1.5,
        'maximum': 2.0
    }

    multiplier = sensitivity_multipliers.get(privacy_sensitivity, 1.0)
    adjusted_score = int(base_privacy_score * multiplier)

    # Adjust privacy flags based on sensitivity
    if privacy_sensitivity in ['high', 'maximum']:
        # More aggressive privacy detection
        privacy_keywords = ['personal', 'private', 'confidential', 'internal', 'secret']
        content_lower = content.lower()
        additional_privacy_indicators = sum(1 for keyword in privacy_keywords if keyword in content_lower)
        
        if additional_privacy_indicators > 0:
            adjusted_score += additional_privacy_indicators
            adjusted_flags = True
        else:
            adjusted_flags = base_privacy_flags
    else:
        adjusted_flags = base_privacy_flags

    return adjusted_score, adjusted_flags

async def _adjust_semantic_overlap(
    self,
    base_overlap: float,
    preference_manager: UserPreferenceManager
) -> float:
    """Adjust semantic overlap based on user's sharing preferences"""

    # Get user's preference for shared vs isolated storage
    sharing_preference = await preference_manager.get_preference('sharing_preference', 'balanced')
    
    if sharing_preference == 'prefer_shared':
        # Boost semantic overlap to encourage shared storage
        return min(1.0, base_overlap * 1.2)
    elif sharing_preference == 'prefer_isolated':
        # Reduce semantic overlap to encourage isolated storage
        return base_overlap * 0.8
    else:
        # Balanced approach - no adjustment
        return base_overlap

async def _record_classification_interaction(
    self,
    user_id: str,
    content: str,
    entities: List[Dict[str, Any]],
    strategy: ClassificationStrategy,
    confidence: float,
    context: Optional[Dict[str, Any]]
) -> None:
    """Record classification interaction for behavioral learning"""

    # Get behavioral analyzer
    if not hasattr(self, '_behavioral_analyzers'):
        self._behavioral_analyzers = {}
    
    if user_id not in self._behavioral_analyzers:
        self._behavioral_analyzers[user_id] = BehavioralPatternAnalyzer(user_id)
    
    analyzer = self._behavioral_analyzers[user_id]

    # Record the interaction
    interaction_context = {
        'classification_strategy': strategy.value,
        'confidence': confidence,
        'entity_count': len(entities),
        'content_length': len(content),
        'domain': self._detect_primary_domain_from_entities(entities),
        'session_id': context.get('session_id', 'default') if context else 'default'
    }

    await analyzer.record_interaction('classification', interaction_context)

def _detect_primary_domain_from_entities(self, entities: List[Dict[str, Any]]) -> Optional[str]:
    """Detect primary domain from entities"""
    domain_counts = {}
    
    for entity in entities:
        if entity['label'].startswith('DOMAIN_'):
            domain = entity['label'].replace('DOMAIN_', '').lower()
            domain_counts[domain] = domain_counts.get(domain, 0) + 1
    
    if domain_counts:
        return max(domain_counts, key=domain_counts.get)
    
    return None
```

**Validation:**
```python
# Test classification with preferences
from python.helpers.intelligent_classification_engine import IntelligentClassificationEngine
from python.helpers.classification_config import classification_config

engine = IntelligentClassificationEngine(classification_config)

# Test classification with user preferences
test_content = "Python programming tutorial for machine learning"
metadata = {'content_type': 'document'}
user_id = "test_user"

result = await engine.classify_content_with_preferences(
    test_content, metadata, user_id
)

print(f"Personalized classification: {result.strategy.value}")
print(f"Confidence: {result.confidence}")
print(f"Reasoning: {result.reasoning}")
```

---

## Step 4.3.2: Create Preference-Aware Memory Routing

**File:** `python/helpers/memory_abstraction.py`
**Action:** Update existing file

Add this method to the `EnhancedMemoryAbstractionLayer` class:
```python
async def store_with_user_preferences(
    self,
    content: str,
    content_type: str,
    metadata: Dict[str, Any],
    user_id: str
) -> Tuple[str, ClassificationResult]:
    """Store content using user preferences for classification"""
    await self._ensure_initialized()

    # Use preference-aware classification
    from .intelligent_classification_engine import IntelligentClassificationEngine
    
    if not hasattr(self, '_preference_classification_engine'):
        self._preference_classification_engine = IntelligentClassificationEngine(classification_config)

    # Classify with user preferences
    classification_result = await self._preference_classification_engine.classify_content_with_preferences(
        content, metadata, user_id
    )

    # Route based on classification with user context
    if classification_result.strategy == ClassificationStrategy.SHARED_ONTOLOGY:
        doc_id = await self._store_in_shared_ontology_with_preferences(
            content, metadata, classification_result, user_id
        )
    elif classification_result.strategy == ClassificationStrategy.CUSTOM_DOMAIN:
        doc_id = await self._store_in_custom_domain_with_preferences(
            content, metadata, classification_result, user_id
        )
    else:  # ISOLATED_NAMESPACE
        doc_id = await self._store_in_isolated_namespace_with_preferences(
            content, metadata, classification_result, user_id
        )

    # Record user feedback opportunity
    await self._create_feedback_opportunity(user_id, doc_id, classification_result)

    return doc_id, classification_result

async def _store_in_shared_ontology_with_preferences(
    self,
    content: str,
    metadata: Dict[str, Any],
    result: ClassificationResult,
    user_id: str
) -> str:
    """Store in shared ontology with user preference tracking"""
    
    # Add user context to metadata
    enhanced_metadata = metadata.copy()
    enhanced_metadata.update({
        'user_id': user_id,
        'classification_confidence': result.confidence,
        'classification_reasoning': result.reasoning,
        'storage_tier': 'shared_ontology'
    })

    # Store using existing backend
    doc_id = await self.backend.insert_knowledge_document(content, enhanced_metadata)

    # Record storage decision for learning
    await self._record_storage_decision(user_id, doc_id, result, 'shared_ontology')

    return doc_id

async def _store_in_custom_domain_with_preferences(
    self,
    content: str,
    metadata: Dict[str, Any],
    result: ClassificationResult,
    user_id: str
) -> str:
    """Store in custom domain with user preference tracking"""
    
    enhanced_metadata = metadata.copy()
    enhanced_metadata.update({
        'user_id': user_id,
        'classification_confidence': result.confidence,
        'classification_reasoning': result.reasoning,
        'storage_tier': 'custom_domain',
        'domain': result.domain
    })

    doc_id = await self.backend.insert_knowledge_document(content, enhanced_metadata)
    await self._record_storage_decision(user_id, doc_id, result, 'custom_domain')

    return doc_id

async def _store_in_isolated_namespace_with_preferences(
    self,
    content: str,
    metadata: Dict[str, Any],
    result: ClassificationResult,
    user_id: str
) -> str:
    """Store in isolated namespace with user preference tracking"""
    
    enhanced_metadata = metadata.copy()
    enhanced_metadata.update({
        'user_id': user_id,
        'classification_confidence': result.confidence,
        'classification_reasoning': result.reasoning,
        'storage_tier': 'isolated_namespace',
        'namespace': result.namespace
    })

    doc_id = await self.backend.insert_text(content, enhanced_metadata)
    await self._record_storage_decision(user_id, doc_id, result, 'isolated_namespace')

    return doc_id

async def _record_storage_decision(
    self,
    user_id: str,
    doc_id: str,
    result: ClassificationResult,
    storage_location: str
) -> None:
    """Record storage decision for preference learning"""
    
    # Get user preference manager
    from .user_preferences import UserPreferenceManager
    
    if not hasattr(self, '_user_preference_managers'):
        self._user_preference_managers = {}
    
    if user_id not in self._user_preference_managers:
        self._user_preference_managers[user_id] = UserPreferenceManager(user_id)
    
    preference_manager = self._user_preference_managers[user_id]

    # Record as implicit feedback
    from .user_preferences import LearningSignal
    
    await preference_manager.record_feedback(
        action='storage_decision',
        context={
            'doc_id': doc_id,
            'classification_result': {
                'strategy': result.strategy.value,
                'confidence': result.confidence,
                'domain': result.domain,
                'namespace': result.namespace
            },
            'storage_location': storage_location,
            'metrics': {
                'semantic_overlap': result.metrics.semantic_overlap,
                'domain_specificity': result.metrics.domain_specificity,
                'privacy_score': result.metrics.privacy_score
            }
        },
        feedback_type=LearningSignal.IMPLICIT_USAGE
    )

async def _create_feedback_opportunity(
    self,
    user_id: str,
    doc_id: str,
    result: ClassificationResult
) -> None:
    """Create opportunity for user to provide explicit feedback"""
    
    # Store feedback opportunity for later collection
    if not hasattr(self, '_pending_feedback'):
        self._pending_feedback = {}
    
    if user_id not in self._pending_feedback:
        self._pending_feedback[user_id] = []
    
    feedback_opportunity = {
        'doc_id': doc_id,
        'classification_result': result,
        'timestamp': datetime.now(),
        'feedback_collected': False
    }
    
    self._pending_feedback[user_id].append(feedback_opportunity)
    
    # Keep only recent opportunities (last 50)
    if len(self._pending_feedback[user_id]) > 50:
        self._pending_feedback[user_id] = self._pending_feedback[user_id][-50:]

async def collect_user_feedback(
    self,
    user_id: str,
    doc_id: str,
    feedback_type: str,
    feedback_value: Any
) -> bool:
    """Collect explicit user feedback on classification decision"""
    
    if not hasattr(self, '_pending_feedback') or user_id not in self._pending_feedback:
        return False
    
    # Find the feedback opportunity
    opportunity = None
    for opp in self._pending_feedback[user_id]:
        if opp['doc_id'] == doc_id and not opp['feedback_collected']:
            opportunity = opp
            break
    
    if not opportunity:
        return False
    
    # Get user preference manager
    if not hasattr(self, '_user_preference_managers'):
        self._user_preference_managers = {}
    
    if user_id not in self._user_preference_managers:
        from .user_preferences import UserPreferenceManager
        self._user_preference_managers[user_id] = UserPreferenceManager(user_id)
    
    preference_manager = self._user_preference_managers[user_id]

    # Record the feedback
    from .user_preferences import LearningSignal
    
    signal_type = LearningSignal.POSITIVE_FEEDBACK if feedback_type == 'positive' else LearningSignal.NEGATIVE_FEEDBACK
    
    await preference_manager.record_feedback(
        action='classification_feedback',
        context={
            'doc_id': doc_id,
            'classification_result': opportunity['classification_result'].__dict__,
            'feedback_type': feedback_type,
            'feedback_value': feedback_value
        },
        feedback_type=signal_type,
        value=feedback_value
    )
    
    # Mark feedback as collected
    opportunity['feedback_collected'] = True
    
    return True
```

**Validation:**
```python
# Test preference-aware memory storage
from python.helpers.memory_abstraction import EnhancedMemoryAbstractionLayer

memory_layer = EnhancedMemoryAbstractionLayer()

# Test storing with user preferences
doc_id, result = await memory_layer.store_with_user_preferences(
    content="Python machine learning tutorial",
    content_type="document",
    metadata={"source": "tutorial"},
    user_id="test_user"
)

print(f"Stored document {doc_id} using strategy: {result.strategy.value}")

# Test collecting feedback
feedback_success = await memory_layer.collect_user_feedback(
    user_id="test_user",
    doc_id=doc_id,
    feedback_type="positive",
    feedback_value="classification_correct"
)

print(f"Feedback collected: {feedback_success}")
```

---

## Next Steps

✅ **Step 4.3 Complete - Validation Checklist:**
- [ ] Classification engine supports user preference integration
- [ ] User preferences adjust classification metrics appropriately
- [ ] Memory routing uses preference-aware classification
- [ ] Storage decisions are recorded for learning
- [ ] User feedback collection mechanism is in place
- [ ] Behavioral interactions are tracked for pattern analysis

**Next:** Proceed to Step 4.4 - Preference Interface
