"""
Base classification framework for intelligent data classification system
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple, Union, Type # Added Type
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time
import asyncio
from collections import deque, defaultdict
import uuid # For generating unique IDs

# Attempt to import ClassificationConfig and ClassificationStrategy
# Handle potential ImportError for robustness during setup or testing.
try:
    from .classification_config import ClassificationConfig, ClassificationStrategy
except ImportError:
    print("Warning: classification_config not fully imported in classification_engine. Using mock for setup.")
    # Define minimal mocks if needed for the script to be parsable.
    class MockClassificationStrategy(Enum):
        SHARED_ONTOLOGY = "shared_ontology"
        CUSTOM_DOMAIN = "custom_domain"
        ISOLATED_NAMESPACE = "isolated_namespace"
    ClassificationStrategy = MockClassificationStrategy # type: ignore

    @dataclass
    class MockCacheConfig:
        enabled: bool = True
    @dataclass
    class MockClassificationConfig:
        batch_size: int = 100
        batch_timeout: int = 300 # seconds
        backend_type: str = "mock"
        cache: MockCacheConfig = field(default_factory=MockCacheConfig)
        # Add other fields if they are accessed directly in this module during init
    ClassificationConfig = MockClassificationConfig # type: ignore


class ClassificationStatus(Enum):
    """Classification operation status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ClassificationPriority(Enum):
    """Classification priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class ClassificationMetrics:
    """Comprehensive metrics for classification decisions"""
    semantic_overlap: float = 0.0
    entity_confidence: float = 0.0
    domain_specificity: float = 0.0
    relationship_density: float = 0.0
    privacy_score: int = 0
    privacy_flags: bool = False # Indicates if any privacy-sensitive elements were detected
    interconnectedness: float = 0.0
    temporal_relevance: float = 1.0 # Default to fully relevant
    related_entities_count: int = 0
    user_specified_isolation: bool = False
    user_domain_preference: Optional[str] = None
    confidence_variance: float = 0.0
    processing_complexity: float = 0.0
    data_quality_score: float = 1.0 # Default to perfect quality
    detected_language: Optional[str] = None
    content_length: int = 0
    entity_types: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClassificationMetrics':
        # Filter data to only include fields defined in the dataclass
        known_fields = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_data = {k: v for k, v in data.items() if k in known_fields}
        return cls(**filtered_data)

def generate_id(prefix: str) -> str:
    """Generates a unique ID with a given prefix."""
    return f"{prefix}_{uuid.uuid4().hex[:12]}"

@dataclass
class ClassificationResult:
    """Result of a classification operation"""
    strategy: ClassificationStrategy
    confidence: float
    metrics: ClassificationMetrics
    reasoning: str
    processing_time_ms: float
    namespace: Optional[str] = None
    domain: Optional[str] = None
    classification_id: str = field(default_factory=lambda: generate_id("cls"))
    timestamp: datetime = field(default_factory=datetime.now)
    status: ClassificationStatus = ClassificationStatus.COMPLETED
    validation_passed: bool = True
    validation_warnings: List[str] = field(default_factory=list)
    fallback_used: bool = False
    cache_hit: bool = False

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['strategy'] = self.strategy.value
        data['status'] = self.status.value
        data['timestamp'] = self.timestamp.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClassificationResult':
        data['strategy'] = ClassificationStrategy(data['strategy'])
        data['status'] = ClassificationStatus(data['status'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['metrics'] = ClassificationMetrics.from_dict(data['metrics'])

        known_fields = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_data = {k: v for k, v in data.items() if k in known_fields}
        return cls(**filtered_data)

@dataclass
class ClassificationRequest:
    """Request for classification operation"""
    content: str
    metadata: Dict[str, Any]
    request_id: str = field(default_factory=lambda: generate_id("req"))
    priority: ClassificationPriority = ClassificationPriority.NORMAL
    timeout_seconds: float = 30.0
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    source_system: Optional[str] = None
    use_cache: bool = True
    force_reprocessing: bool = False
    include_debug_info: bool = False # For engines to optionally return more verbose reasoning
    created_at: datetime = field(default_factory=datetime.now)
    # These are set by the engine during processing
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class PerformanceTracker:
    """Tracks performance metrics for classification operations"""
    def __init__(self, max_history: int = 10000): # Max history for overall metrics
        self.max_history = max_history
        self.metrics_history: deque[Dict[str, Any]] = deque(maxlen=self.max_history)
        self.current_operations: Dict[str, float] = {} # Tracks start time of ongoing ops
        self.total_operations = 0
        self.successful_operations = 0
        self.failed_operations = 0
        # Max history for specific timing stats (e.g., response_times, strategy_performance)
        self.timing_stats_max_len = 1000
        self.response_times: deque[float] = deque(maxlen=self.timing_stats_max_len)
        self.strategy_performance: Dict[str, deque[float]] = defaultdict(lambda: deque(maxlen=self.timing_stats_max_len))
        self.cache_hits = 0
        self.cache_misses = 0

    def start_timing(self, operation_id: Optional[str] = None) -> str:
        op_id = operation_id or generate_id("op")
        self.current_operations[op_id] = time.perf_counter()
        return op_id

    def end_timing(self, operation_id: str) -> Optional[float]:
        start_time = self.current_operations.pop(operation_id, None)
        if start_time is None:
            return None # Operation ID not found or already ended
        duration_ms = (time.perf_counter() - start_time) * 1000
        self.response_times.append(duration_ms)
        return duration_ms

    def record_classification(self, result: ClassificationResult):
        self.total_operations += 1
        if result.status == ClassificationStatus.COMPLETED and result.validation_passed:
            self.successful_operations += 1
        else:
            self.failed_operations += 1

        strategy_key = result.strategy.value
        self.strategy_performance[strategy_key].append(result.processing_time_ms)

        if result.cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1 # Assuming a request that isn't a hit is a miss

        self.metrics_history.append({
            'timestamp': result.timestamp.isoformat(),
            'strategy': strategy_key,
            'confidence': result.confidence,
            'processing_time_ms': result.processing_time_ms,
            'cache_hit': result.cache_hit,
            'validation_passed': result.validation_passed,
            'status': result.status.value
        })

    def get_average_processing_time(self, strategy_key: Optional[str] = None) -> float:
        times_deque = self.strategy_performance.get(strategy_key) if strategy_key else self.response_times
        if not times_deque: return 0.0
        return sum(times_deque) / len(times_deque)

    def get_success_rate(self) -> float: # Renamed from accuracy
        if self.total_operations == 0: return 0.0
        return self.successful_operations / self.total_operations

    def get_cache_hit_rate(self) -> float:
        total_lookups = self.cache_hits + self.cache_misses
        if total_lookups == 0: return 0.0
        return self.cache_hits / total_lookups

    def _calculate_percentiles(self, data: deque[float]) -> Dict[str, float]:
        if not data:
            return {"p50": 0.0, "p90": 0.0, "p95": 0.0, "p99": 0.0}

        sorted_data = sorted(list(data))
        n = len(sorted_data)

        percentiles = {}
        for p_val in [50, 90, 95, 99]:
            idx = (p_val / 100) * (n - 1)
            if idx.is_integer():
                percentiles[f"p{p_val}"] = sorted_data[int(idx)]
            else:
                lower = sorted_data[int(idx)]
                upper = sorted_data[int(idx) + 1]
                percentiles[f"p{p_val}"] = lower + (upper - lower) * (idx - int(idx))
        return percentiles


    def get_performance_summary(self) -> Dict[str, Any]:
        response_time_percentiles = self._calculate_percentiles(self.response_times)

        summary = {
            'total_operations': self.total_operations,
            'successful_operations': self.successful_operations,
            'failed_operations': self.failed_operations,
            'success_rate': self.get_success_rate(),
            'average_response_time_ms': self.get_average_processing_time(),
            'response_time_p50_ms': response_time_percentiles['p50'],
            'response_time_p95_ms': response_time_percentiles['p95'],
            'response_time_p99_ms': response_time_percentiles['p99'],
            'cache_hit_rate': self.get_cache_hit_rate(),
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'strategy_performance': {
                s_key: {
                    'count': len(s_deque),
                    'avg_time_ms': sum(s_deque) / len(s_deque) if s_deque else 0.0,
                    'min_time_ms': min(s_deque) if s_deque else 0.0,
                    'max_time_ms': max(s_deque) if s_deque else 0.0,
                    **self._calculate_percentiles(s_deque) # Add p50,p90,p95,p99 for each strategy
                }
                for s_key, s_deque in self.strategy_performance.items()
            }
        }
        return summary


class ClassificationEngine(ABC):
    """Abstract base class for classification engines"""
    def __init__(self, config: ClassificationConfig):
        self.config = config
        self.performance_tracker = PerformanceTracker()
        self.is_initialized = False
        self.error_count = 0
        self.last_error: Optional[Exception] = None
        self.error_threshold = 10 # Example threshold
        self.request_queue: asyncio.Queue[ClassificationRequest] = asyncio.Queue()
        self.batch_processor_task: Optional[asyncio.Task] = None

    async def initialize(self):
        if self.is_initialized: return
        await self._initialize_engine()
        self.is_initialized = True
        if self.config.batch_size > 0: # batch_size > 1 in original, but >0 makes more sense
            self.batch_processor_task = asyncio.create_task(self._batch_processor())

    @abstractmethod
    async def _initialize_engine(self):
        """Engine-specific initialization logic."""
        pass

    @abstractmethod
    async def classify_content(self, content: str, metadata: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ClassificationResult:
        """Classify a single piece of content."""
        pass

    async def classify_batch(self, requests: List[ClassificationRequest]) -> List[ClassificationResult]:
        """Classify multiple content items in batch."""
        # This default implementation processes sequentially. Engines can override for true batching.
        results: List[ClassificationResult] = []
        for req in requests:
            req.started_at = datetime.now()
            try:
                # Use asyncio.wait_for for timeout per request
                result = await asyncio.wait_for(
                    self.classify_content(req.content, req.metadata),
                    timeout=req.timeout_seconds
                )
            except asyncio.TimeoutError:
                result = self._create_timeout_result(req)
            except Exception as e: # pylint: disable=broad-except
                result = self._create_error_result(req, e)
            req.completed_at = datetime.now()
            # Ensure processing_time_ms is set if not by classify_content
            if result.processing_time_ms == 0 and req.started_at and req.completed_at:
                 result.processing_time_ms = (req.completed_at - req.started_at).total_seconds() * 1000
            self.performance_tracker.record_classification(result)
            results.append(result)
        return results

    async def _batch_processor(self):
        """Background task to process requests from the queue in batches."""
        while True:
            batch: List[ClassificationRequest] = []
            try:
                # Wait for the first request with batch_timeout
                first_req = await asyncio.wait_for(self.request_queue.get(), timeout=self.config.batch_timeout/1000.0) # timeout is in s
                batch.append(first_req)
                self.request_queue.task_done() # Mark task as done for the first item

                # Fill the rest of the batch up to batch_size, without further long waits
                while len(batch) < self.config.batch_size:
                    try:
                        req = await asyncio.wait_for(self.request_queue.get(), timeout=0.01) # Very short timeout
                        batch.append(req)
                        self.request_queue.task_done()
                    except asyncio.TimeoutError:
                        break # No more items readily available

                if batch:
                    # The classify_batch method should ideally handle individual errors
                    # and return a list of results corresponding to each request.
                    # These results (including errors) are then typically handled by whoever put them in the queue.
                    await self.classify_batch(batch) # Results are handled by the caller of queue.put if needed

            except asyncio.TimeoutError:
                # No requests arrived within batch_timeout, loop and wait again.
                continue
            except Exception as e: # pylint: disable=broad-except
                self.last_error = e
                self.error_count += 1
                # Log this error, e.g., using a proper logger
                print(f"Error in batch processor: {e}")
                if self.error_count > self.error_threshold:
                    print(f"FATAL: Classification engine error threshold ({self.error_threshold}) exceeded. Shutting down batch processor.")
                    break # Exit the loop, stopping the batch processor.
                await asyncio.sleep(1) # Avoid rapid looping on persistent errors

    def _create_timeout_result(self, request: ClassificationRequest) -> ClassificationResult:
        return ClassificationResult(
            classification_id=generate_id("cls_err"),
            strategy=ClassificationStrategy.ISOLATED_NAMESPACE, # Default fallback strategy
            confidence=0.0,
            metrics=ClassificationMetrics(content_length=len(request.content)),
            reasoning=f"Classification timed out after {request.timeout_seconds}s for request {request.request_id}",
            processing_time_ms=request.timeout_seconds * 1000,
            status=ClassificationStatus.FAILED,
            validation_passed=False,
            validation_warnings=["Request timed out"],
            fallback_used=True
        )

    def _create_error_result(self, request: ClassificationRequest, error: Exception) -> ClassificationResult:
        processing_time_ms = 0.0
        if request.started_at:
            processing_time_ms = (datetime.now() - request.started_at).total_seconds() * 1000

        return ClassificationResult(
            classification_id=generate_id("cls_err"),
            strategy=ClassificationStrategy.ISOLATED_NAMESPACE, # Default fallback strategy
            confidence=0.0,
            metrics=ClassificationMetrics(content_length=len(request.content)),
            reasoning=f"Classification failed for request {request.request_id}: {str(error)}",
            processing_time_ms=processing_time_ms,
            status=ClassificationStatus.FAILED,
            validation_passed=False,
            validation_warnings=[f"Unhandled error: {str(error)}"],
            fallback_used=True
        )

    async def get_engine_status(self) -> Dict[str, Any]:
        return {
            'is_initialized': self.is_initialized,
            'error_count': self.error_count,
            'last_error': str(self.last_error) if self.last_error else None,
            'queue_size': self.request_queue.qsize(),
            'performance_summary': self.performance_tracker.get_performance_summary(),
            'config_summary': {
                'backend_type': self.config.backend_type,
                'batch_size': self.config.batch_size,
                'batch_timeout_ms': self.config.batch_timeout, # Renamed for clarity from .md
                'cache_enabled': self.config.cache.enabled,
            }
        }

    async def shutdown(self):
        if self.batch_processor_task and not self.batch_processor_task.done():
            self.batch_processor_task.cancel()
            try:
                await self.batch_processor_task
            except asyncio.CancelledError:
                pass # Expected
        await self._shutdown_engine()
        self.is_initialized = False

    @abstractmethod
    async def _shutdown_engine(self):
        """Engine-specific shutdown logic."""
        pass


class ClassificationEngineFactory:
    """Factory for creating classification engines."""
    _engines: Dict[str, Type[ClassificationEngine]] = {} # type: ignore

    @classmethod
    def register_engine(cls, name: str, engine_class: Type[ClassificationEngine]): # type: ignore
        if not issubclass(engine_class, ClassificationEngine):
            raise TypeError(f"{engine_class.__name__} is not a subclass of ClassificationEngine")
        cls._engines[name] = engine_class

    @classmethod
    def create_engine(cls, name: str, config: ClassificationConfig) -> ClassificationEngine:
        engine_class = cls._engines.get(name)
        if not engine_class:
            raise ValueError(f"Unknown classification engine name: {name}. Available: {list(cls._engines.keys())}")
        return engine_class(config)

    @classmethod
    def list_engines(cls) -> List[str]:
        return list(cls._engines.keys())

# Custom Exception Classes
class ClassificationError(Exception):
    """Base exception for all classification-related errors."""
    def __init__(self, message: str, request_id: Optional[str] = None, engine_name: Optional[str] = None):
        super().__init__(message)
        self.request_id = request_id
        self.engine_name = engine_name
    def __str__(self):
        return f"{self.__class__.__name__}: {super().__str__()} (Request: {self.request_id}, Engine: {self.engine_name})"

class ClassificationTimeoutError(ClassificationError):
    """Raised when a classification operation exceeds its allocated time."""
    pass

class ClassificationValidationError(ClassificationError):
    """Raised when input data or configuration fails validation."""
    pass

class ClassificationConfigurationError(ClassificationError):
    """Raised for errors related to engine or system configuration."""
    pass

class EngineInitializationError(ClassificationError):
    """Raised when an engine fails to initialize."""
    pass


if __name__ == "__main__":
    # Basic Test and Usage Example

    # 1. Mock Configuration and Engine for testing
    mock_config = ClassificationConfig() # Uses defaults or mocked above

    class MockEngine(ClassificationEngine):
        async def _initialize_engine(self):
            print(f"MockEngine '{self.config.backend_type}' initialized.")

        async def classify_content(self, content: str, metadata: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> ClassificationResult:
            op_id = self.performance_tracker.start_timing()
            await asyncio.sleep(0.01) # Simulate work
            duration_ms = self.performance_tracker.end_timing(op_id) or 0.0

            metrics = ClassificationMetrics(
                semantic_overlap=0.75,
                entity_confidence=0.88,
                content_length=len(content)
            )
            result = ClassificationResult(
                strategy=ClassificationStrategy.SHARED_ONTOLOGY, # type: ignore
                confidence=0.82,
                metrics=metrics,
                reasoning="Mock classification successful.",
                processing_time_ms=duration_ms
            )
            self.performance_tracker.record_classification(result)
            return result

        async def _shutdown_engine(self):
            print(f"MockEngine '{self.config.backend_type}' shutdown.")

    # 2. Register and Create Engine
    ClassificationEngineFactory.register_engine("mock_default", MockEngine)
    try:
        engine = ClassificationEngineFactory.create_engine("mock_default", mock_config)
    except ValueError as e:
        print(f"Error creating engine: {e}")
        engine = None # type: ignore

    async def main():
        if not engine:
            return

        await engine.initialize()

        # 3. Test Classification Request
        request = ClassificationRequest(
            content="This is a test document about AI.",
            metadata={"source": "test_script"}
        )

        print(f"\n--- Classifying single request (ID: {request.request_id}) ---")
        result = await engine.classify_content(request.content, request.metadata)
        print(f"Result ID: {result.classification_id}, Strategy: {result.strategy.value}, Confidence: {result.confidence:.2f}")
        print(f"Processing time: {result.processing_time_ms:.2f}ms, Cache Hit: {result.cache_hit}")
        print(f"Reasoning: {result.reasoning}")

        # 4. Test Batch Classification (using the default sequential batching)
        requests_batch = [
            ClassificationRequest(content="Document A on topic X.", metadata={"id": "A"}),
            ClassificationRequest(content="Document B about topic Y.", metadata={"id": "B"}),
        ]
        print(f"\n--- Classifying batch of {len(requests_batch)} requests ---")
        batch_results = await engine.classify_batch(requests_batch)
        for res in batch_results:
            print(f"Batch Result ID: {res.classification_id}, Strategy: {res.strategy.value}, Confidence: {res.confidence:.2f}, Time: {res.processing_time_ms:.2f}ms")

        # 5. Test Performance Summary
        print("\n--- Performance Summary ---")
        status = await engine.get_engine_status()
        # print(json.dumps(status['performance_summary'], indent=2))
        print(f"Total ops: {status['performance_summary']['total_operations']}, Success rate: {status['performance_summary']['success_rate']:.2f}")
        print(f"Avg response time: {status['performance_summary']['average_response_time_ms']:.2f}ms")


        # 6. Test Queued Batch Processing (if batch_size > 0)
        if engine.config.batch_size > 0 and engine.batch_processor_task:
            print("\n--- Testing queued batch processing ---")
            queued_requests = [
                ClassificationRequest(content=f"Queued doc {i}", metadata={"id": f"Q{i}"}) for i in range(5)
            ]
            for req in queued_requests:
                await engine.request_queue.put(req)

            print(f"Put {len(queued_requests)} requests into the queue. Max batch size: {engine.config.batch_size}")
            print("Waiting for queue to be processed (approx)... This depends on batch_timeout.")

            # Wait for queue to likely be processed.
            # In a real app, results would be associated back to callers, e.g., via futures or callbacks.
            # Here, we just check if the performance tracker reflects more operations.
            await asyncio.sleep(engine.config.batch_timeout / 1000 * (len(queued_requests) / engine.config.batch_size + 1) + 0.2)

            status_after_queue = await engine.get_engine_status()
            print(f"Total ops after queue: {status_after_queue['performance_summary']['total_operations']}")


        # 7. Shutdown
        await engine.shutdown()
        print("\nEngine shutdown complete.")

    if engine:
        asyncio.run(main())
    else:
        print("Engine could not be created. Skipping main async tests.")
