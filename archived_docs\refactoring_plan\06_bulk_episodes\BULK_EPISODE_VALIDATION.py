"""
Bulk Episode Processing Validation Script
Comprehensive testing and validation for the enhanced memory system with bulk processing
"""

import asyncio
import json
import time
from datetime import datetime, timezone
from typing import List, Dict, Any
import os
import sys

class BulkEpisodeValidator:
    """Validator for bulk episode processing functionality"""
    
    def __init__(self):
        self.test_results = []
        self.performance_metrics = {}
    
    async def run_all_validations(self) -> Dict[str, Any]:
        """Run comprehensive validation suite"""
        print("🚀 Starting Bulk Episode Processing Validation")
        print("=" * 60)
        
        # Test data preparation
        await self._prepare_test_data()
        
        # Core functionality tests
        await self._test_bulk_episode_creation()
        await self._test_bulk_knowledge_document_processing()
        await self._test_mixed_content_bulk_processing()
        
        # Performance tests
        await self._test_bulk_vs_individual_performance()
        await self._test_large_batch_processing()
        
        # Error handling tests
        await self._test_error_handling_and_fallback()
        await self._test_partial_failure_scenarios()
        
        # Integration tests
        await self._test_backend_switching()
        await self._test_configuration_validation()
        
        # Generate report
        return await self._generate_validation_report()
    
    async def _prepare_test_data(self):
        """Prepare test data for validation"""
        print("📋 Preparing test data...")
        
        # Sample episodes for bulk processing
        self.sample_episodes = [
            {
                "name": f"Test Episode {i}",
                "content": f"This is test episode content number {i}. It contains sample data for validation.",
                "source": "text",
                "source_description": "validation-test",
                "reference_time": datetime.now(timezone.utc).isoformat(),
                "metadata": {"test_id": i, "batch": "validation"}
            }
            for i in range(1, 51)  # 50 test episodes
        ]
        
        # Sample knowledge documents
        self.sample_knowledge_docs = [
            {
                "name": f"Knowledge Document {i}",
                "content": f"Knowledge content {i}: This document contains important information about topic {i}.",
                "metadata": {
                    "filename": f"doc_{i}.txt",
                    "area": "knowledge",
                    "topic": f"topic_{i}",
                    "test_id": i
                }
            }
            for i in range(1, 26)  # 25 knowledge documents
        ]
        
        # Mixed content for testing
        self.mixed_content = []
        for i in range(1, 21):
            if i % 2 == 0:
                # Knowledge document
                self.mixed_content.append({
                    "content_type": "knowledge_document",
                    "content": f"Mixed knowledge content {i}",
                    "metadata": {"filename": f"mixed_doc_{i}.txt", "area": "knowledge"}
                })
            else:
                # Agent memory
                self.mixed_content.append({
                    "content_type": "agent_memory",
                    "content": f"Mixed agent memory {i}",
                    "metadata": {"area": "main", "conversation_id": f"conv_{i}"}
                })
        
        print(f"✅ Prepared {len(self.sample_episodes)} episodes, {len(self.sample_knowledge_docs)} knowledge docs, {len(self.mixed_content)} mixed content items")
    
    async def _test_bulk_episode_creation(self):
        """Test basic bulk episode creation functionality"""
        print("\n🧪 Testing bulk episode creation...")
        
        try:
            # Mock the enhanced memory abstraction layer
            from helpers.memory_abstraction import EnhancedMemoryAbstractionLayer
            
            # This would normally be initialized with a real agent
            # For testing, we'll create a mock setup
            memory_layer = await self._create_test_memory_layer()
            
            start_time = time.time()
            episode_ids = await memory_layer.add_episodes_bulk(self.sample_episodes[:10])
            end_time = time.time()
            
            self.performance_metrics['bulk_episode_creation'] = {
                'duration': end_time - start_time,
                'episodes_processed': len(self.sample_episodes[:10]),
                'episodes_per_second': len(self.sample_episodes[:10]) / (end_time - start_time)
            }
            
            assert len(episode_ids) == 10, f"Expected 10 episode IDs, got {len(episode_ids)}"
            assert all(isinstance(id, str) for id in episode_ids), "All episode IDs should be strings"
            
            self.test_results.append({
                'test': 'bulk_episode_creation',
                'status': 'PASS',
                'message': f'Successfully created {len(episode_ids)} episodes in bulk'
            })
            print(f"✅ Bulk episode creation: {len(episode_ids)} episodes created")
            
        except Exception as e:
            self.test_results.append({
                'test': 'bulk_episode_creation',
                'status': 'FAIL',
                'message': f'Error: {str(e)}'
            })
            print(f"❌ Bulk episode creation failed: {e}")
    
    async def _test_bulk_knowledge_document_processing(self):
        """Test bulk knowledge document processing with entity extraction"""
        print("\n📚 Testing bulk knowledge document processing...")
        
        try:
            memory_layer = await self._create_test_memory_layer()
            
            start_time = time.time()
            doc_ids = await memory_layer.process_knowledge_documents_bulk(self.sample_knowledge_docs[:10])
            end_time = time.time()
            
            self.performance_metrics['bulk_knowledge_processing'] = {
                'duration': end_time - start_time,
                'documents_processed': len(self.sample_knowledge_docs[:10]),
                'documents_per_second': len(self.sample_knowledge_docs[:10]) / (end_time - start_time)
            }
            
            assert len(doc_ids) == 10, f"Expected 10 document IDs, got {len(doc_ids)}"
            
            self.test_results.append({
                'test': 'bulk_knowledge_processing',
                'status': 'PASS',
                'message': f'Successfully processed {len(doc_ids)} knowledge documents in bulk'
            })
            print(f"✅ Bulk knowledge processing: {len(doc_ids)} documents processed")
            
        except Exception as e:
            self.test_results.append({
                'test': 'bulk_knowledge_processing',
                'status': 'FAIL',
                'message': f'Error: {str(e)}'
            })
            print(f"❌ Bulk knowledge processing failed: {e}")
    
    async def _test_mixed_content_bulk_processing(self):
        """Test processing mixed content types in bulk"""
        print("\n🔀 Testing mixed content bulk processing...")
        
        try:
            memory_layer = await self._create_test_memory_layer()
            
            # Process mixed content
            results = []
            for item in self.mixed_content[:10]:
                if item['content_type'] == 'knowledge_document':
                    doc_id = await memory_layer.insert_content(
                        item['content'], 
                        'knowledge_document', 
                        item['metadata']
                    )
                else:
                    doc_id = await memory_layer.insert_content(
                        item['content'], 
                        'agent_memory', 
                        item['metadata']
                    )
                results.append(doc_id)
            
            assert len(results) == 10, f"Expected 10 results, got {len(results)}"
            
            self.test_results.append({
                'test': 'mixed_content_processing',
                'status': 'PASS',
                'message': f'Successfully processed {len(results)} mixed content items'
            })
            print(f"✅ Mixed content processing: {len(results)} items processed")
            
        except Exception as e:
            self.test_results.append({
                'test': 'mixed_content_processing',
                'status': 'FAIL',
                'message': f'Error: {str(e)}'
            })
            print(f"❌ Mixed content processing failed: {e}")
    
    async def _test_bulk_vs_individual_performance(self):
        """Compare bulk processing performance vs individual processing"""
        print("\n⚡ Testing bulk vs individual performance...")
        
        try:
            memory_layer = await self._create_test_memory_layer()
            test_episodes = self.sample_episodes[:20]
            
            # Test individual processing
            start_time = time.time()
            individual_ids = []
            for episode in test_episodes:
                episode_id = await memory_layer.insert_text(
                    episode['content'], 
                    episode.get('metadata', {})
                )
                individual_ids.append(episode_id)
            individual_time = time.time() - start_time
            
            # Test bulk processing
            start_time = time.time()
            bulk_ids = await memory_layer.add_episodes_bulk(test_episodes)
            bulk_time = time.time() - start_time
            
            performance_ratio = individual_time / bulk_time if bulk_time > 0 else float('inf')
            
            self.performance_metrics['performance_comparison'] = {
                'individual_time': individual_time,
                'bulk_time': bulk_time,
                'performance_ratio': performance_ratio,
                'episodes_count': len(test_episodes)
            }
            
            self.test_results.append({
                'test': 'performance_comparison',
                'status': 'PASS',
                'message': f'Bulk processing is {performance_ratio:.2f}x faster than individual'
            })
            print(f"✅ Performance comparison: Bulk is {performance_ratio:.2f}x faster")
            
        except Exception as e:
            self.test_results.append({
                'test': 'performance_comparison',
                'status': 'FAIL',
                'message': f'Error: {str(e)}'
            })
            print(f"❌ Performance comparison failed: {e}")
    
    async def _test_large_batch_processing(self):
        """Test processing large batches to validate scalability"""
        print("\n📊 Testing large batch processing...")
        
        try:
            memory_layer = await self._create_test_memory_layer()
            
            # Create a large batch
            large_batch = self.sample_episodes  # All 50 episodes
            
            start_time = time.time()
            batch_ids = await memory_layer.add_episodes_bulk(large_batch)
            end_time = time.time()
            
            self.performance_metrics['large_batch_processing'] = {
                'duration': end_time - start_time,
                'episodes_processed': len(large_batch),
                'episodes_per_second': len(large_batch) / (end_time - start_time)
            }
            
            assert len(batch_ids) == len(large_batch), f"Expected {len(large_batch)} IDs, got {len(batch_ids)}"
            
            self.test_results.append({
                'test': 'large_batch_processing',
                'status': 'PASS',
                'message': f'Successfully processed large batch of {len(batch_ids)} episodes'
            })
            print(f"✅ Large batch processing: {len(batch_ids)} episodes processed")
            
        except Exception as e:
            self.test_results.append({
                'test': 'large_batch_processing',
                'status': 'FAIL',
                'message': f'Error: {str(e)}'
            })
            print(f"❌ Large batch processing failed: {e}")
    
    async def _test_error_handling_and_fallback(self):
        """Test error handling and fallback mechanisms"""
        print("\n🛡️ Testing error handling and fallback...")
        
        try:
            memory_layer = await self._create_test_memory_layer()
            
            # Create episodes with some invalid data
            invalid_episodes = [
                {"content": "Valid episode 1", "metadata": {}},
                {"invalid_field": "This should cause an error"},  # Missing content
                {"content": "Valid episode 2", "metadata": {}},
            ]
            
            # This should handle errors gracefully
            result_ids = await memory_layer.add_episodes_bulk(invalid_episodes)
            
            # Should get IDs for valid episodes only
            assert len(result_ids) >= 2, "Should process valid episodes despite errors"
            
            self.test_results.append({
                'test': 'error_handling',
                'status': 'PASS',
                'message': f'Error handling successful: {len(result_ids)} valid episodes processed'
            })
            print(f"✅ Error handling: {len(result_ids)} valid episodes processed from mixed batch")
            
        except Exception as e:
            self.test_results.append({
                'test': 'error_handling',
                'status': 'FAIL',
                'message': f'Error: {str(e)}'
            })
            print(f"❌ Error handling failed: {e}")
    
    async def _test_partial_failure_scenarios(self):
        """Test scenarios where some episodes fail but others succeed"""
        print("\n⚠️ Testing partial failure scenarios...")
        
        # This test would be implemented based on specific failure modes
        # For now, we'll mark it as a placeholder
        self.test_results.append({
            'test': 'partial_failure_scenarios',
            'status': 'SKIP',
            'message': 'Test implementation pending - requires specific failure injection'
        })
        print("⏭️ Partial failure scenarios: Skipped (implementation pending)")
    
    async def _test_backend_switching(self):
        """Test switching between Graphiti and FAISS backends"""
        print("\n🔄 Testing backend switching...")
        
        # This would test the ability to switch backends
        # For now, we'll mark it as a placeholder
        self.test_results.append({
            'test': 'backend_switching',
            'status': 'SKIP',
            'message': 'Test implementation pending - requires backend configuration'
        })
        print("⏭️ Backend switching: Skipped (implementation pending)")
    
    async def _test_configuration_validation(self):
        """Test configuration validation and settings"""
        print("\n⚙️ Testing configuration validation...")
        
        try:
            # Test bulk processing configuration
            config_tests = [
                {'MEMORY_BULK_PROCESSING_ENABLED': 'true', 'expected': True},
                {'MEMORY_BULK_PROCESSING_ENABLED': 'false', 'expected': False},
                {'GRAPHITI_BULK_BATCH_SIZE': '50', 'expected': 50},
                {'GRAPHITI_BULK_TIMEOUT': '600', 'expected': 600},
            ]
            
            for test in config_tests:
                # Set environment variable
                for key, value in test.items():
                    if key != 'expected':
                        os.environ[key] = value
                
                # Test configuration parsing
                # This would normally test the actual configuration parsing
                # For now, we'll just validate the environment variables are set
                assert os.getenv(list(test.keys())[0]) == list(test.values())[0]
            
            self.test_results.append({
                'test': 'configuration_validation',
                'status': 'PASS',
                'message': 'Configuration validation successful'
            })
            print("✅ Configuration validation: All tests passed")
            
        except Exception as e:
            self.test_results.append({
                'test': 'configuration_validation',
                'status': 'FAIL',
                'message': f'Error: {str(e)}'
            })
            print(f"❌ Configuration validation failed: {e}")
    
    async def _create_test_memory_layer(self):
        """Create a test memory layer (mock implementation)"""
        # This is a mock implementation for testing
        # In real implementation, this would create actual memory layer instances
        
        class MockMemoryLayer:
            async def add_episodes_bulk(self, episodes):
                # Simulate bulk processing
                await asyncio.sleep(0.1)  # Simulate processing time
                return [f"episode_{i}" for i in range(len(episodes))]
            
            async def process_knowledge_documents_bulk(self, documents):
                await asyncio.sleep(0.1)
                return [f"doc_{i}" for i in range(len(documents))]
            
            async def insert_content(self, content, content_type, metadata):
                await asyncio.sleep(0.01)
                return f"content_{hash(content) % 1000}"
            
            async def insert_text(self, text, metadata):
                await asyncio.sleep(0.01)
                return f"text_{hash(text) % 1000}"
        
        return MockMemoryLayer()
    
    async def _generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        print("\n📊 Generating validation report...")
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results if t['status'] == 'PASS'])
        failed_tests = len([t for t in self.test_results if t['status'] == 'FAIL'])
        skipped_tests = len([t for t in self.test_results if t['status'] == 'SKIP'])
        
        report = {
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'skipped': skipped_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            'test_results': self.test_results,
            'performance_metrics': self.performance_metrics,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        print(f"\n📋 Validation Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Skipped: {skipped_tests}")
        print(f"   Success Rate: {report['summary']['success_rate']:.1f}%")
        
        if self.performance_metrics:
            print(f"\n⚡ Performance Metrics:")
            for metric, data in self.performance_metrics.items():
                print(f"   {metric}: {data}")
        
        return report

async def main():
    """Main validation function"""
    validator = BulkEpisodeValidator()
    report = await validator.run_all_validations()
    
    # Save report to file
    report_file = f"bulk_episode_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n💾 Validation report saved to: {report_file}")
    
    # Return exit code based on results
    if report['summary']['failed'] > 0:
        print("\n❌ Some tests failed. Please review the report.")
        return 1
    else:
        print("\n✅ All tests passed successfully!")
        return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
