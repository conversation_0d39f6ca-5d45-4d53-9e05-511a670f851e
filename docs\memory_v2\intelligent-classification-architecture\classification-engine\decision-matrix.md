# Classification Decision Matrix
## Intelligent Data Classification Engine

This document defines the comprehensive decision matrix and algorithms used by the Intelligent Data Classification Engine to route incoming data to appropriate storage systems.

## 🎯 Classification Overview

The classification engine analyzes incoming data streams and routes them based on quantitative metrics to one of three destination types:

1. **Shared Ontology Candidates**: General knowledge suitable for global sharing
2. **Custom Domain Ontologies**: Specialized knowledge for specific domains
3. **Isolated Namespaces**: Private, sensitive, or experimental data

## 📊 Decision Matrix Flowchart

```mermaid
flowchart TD
    A[Incoming Data] --> B[Content Analysis]
    B --> C[Entity Extraction]
    C --> D[Relationship Detection]
    D --> E[Semantic Overlap Analysis]
    
    E --> F{Semantic Overlap ≥ 70%?}
    F -->|Yes| G[Shared Ontology]
    F -->|No| H{Privacy Flags Detected?}
    
    H -->|Yes| I[Isolated Namespace]
    H -->|No| J{Related Entities ≥ 5?}
    
    J -->|Yes| K{Semantic Overlap ≤ 30%?}
    K -->|Yes| L[Custom Domain Ontology]
    K -->|No| M{Interconnectedness < 0.3?}
    
    J -->|No| M
    M -->|Yes| I
    M -->|No| N[Default: Custom Domain]
    
    G --> O[Route to Shared Global Storage]
    L --> P[Route to Domain-Specific Storage]
    I --> Q[Route to Isolated Storage]
    N --> P
```

## 🔢 Quantitative Classification Metrics

### 1. Semantic Overlap Score (0.0 - 1.0)

**Calculation Method:**
```python
def calculate_semantic_overlap(new_entities, existing_schemas):
    """
    Calculate semantic overlap using cosine similarity of entity embeddings
    """
    overlap_scores = []
    
    for entity in new_entities:
        entity_embedding = get_entity_embedding(entity)
        max_similarity = 0.0
        
        for schema in existing_schemas:
            schema_embedding = get_schema_embedding(schema)
            similarity = cosine_similarity(entity_embedding, schema_embedding)
            max_similarity = max(max_similarity, similarity)
        
        overlap_scores.append(max_similarity)
    
    return sum(overlap_scores) / len(overlap_scores) if overlap_scores else 0.0
```

**Thresholds:**
- **Shared Ontology**: ≥ 0.70 (70% semantic overlap)
- **Custom Domain**: ≤ 0.30 (30% semantic overlap)
- **Boundary Zone**: 0.30 - 0.70 (requires additional analysis)

### 2. Entity Type Confidence Score (0.0 - 1.0)

**Calculation Method:**
```python
def calculate_entity_confidence(entities):
    """
    Calculate confidence based on entity recognition accuracy
    """
    confidence_scores = []
    
    for entity in entities:
        # Use NER model confidence + domain-specific validation
        ner_confidence = entity.confidence_score
        domain_validation = validate_entity_in_domain(entity)
        
        combined_confidence = (ner_confidence * 0.7) + (domain_validation * 0.3)
        confidence_scores.append(combined_confidence)
    
    return sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
```

**Quality Thresholds:**
- **High Confidence**: ≥ 0.80 (proceed with classification)
- **Medium Confidence**: 0.50 - 0.79 (additional validation required)
- **Low Confidence**: < 0.50 (manual review or rejection)

### 3. Relationship Density Analysis

**Calculation Method:**
```python
def calculate_relationship_density(entities, relationships):
    """
    Calculate the density of relationships between entities
    """
    if len(entities) <= 1:
        return 0.0
    
    max_possible_relationships = len(entities) * (len(entities) - 1) / 2
    actual_relationships = len(relationships)
    
    return actual_relationships / max_possible_relationships
```

**Density Thresholds:**
- **High Density**: ≥ 0.60 (strong interconnectedness)
- **Medium Density**: 0.30 - 0.59 (moderate connections)
- **Low Density**: < 0.30 (sparse connections)

### 4. Domain Specificity Scoring

**Calculation Method:**
```python
def calculate_domain_specificity(content, entities):
    """
    Score how domain-specific the content is
    """
    domain_indicators = {
        'programming': ['function', 'class', 'variable', 'API', 'framework'],
        'medical': ['patient', 'diagnosis', 'treatment', 'symptom'],
        'legal': ['contract', 'clause', 'jurisdiction', 'liability'],
        'finance': ['investment', 'portfolio', 'risk', 'return']
    }
    
    domain_scores = {}
    for domain, keywords in domain_indicators.items():
        score = sum(1 for keyword in keywords if keyword.lower() in content.lower())
        domain_scores[domain] = score / len(keywords)
    
    max_domain_score = max(domain_scores.values()) if domain_scores else 0.0
    return max_domain_score, max(domain_scores, key=domain_scores.get)
```

### 5. Temporal Relevance Weighting

**Calculation Method:**
```python
def calculate_temporal_relevance(timestamp, current_time):
    """
    Apply decay function for time-sensitive data
    """
    import math
    
    time_diff_hours = (current_time - timestamp).total_seconds() / 3600
    
    # Exponential decay with half-life of 30 days (720 hours)
    half_life = 720
    decay_factor = math.exp(-0.693 * time_diff_hours / half_life)
    
    return max(0.1, decay_factor)  # Minimum relevance of 0.1
```

## 🚦 Classification Decision Rules

### Rule 1: Shared Ontology Classification
```python
def classify_as_shared_ontology(metrics):
    """
    Determine if data should go to shared ontology
    """
    return (
        metrics['semantic_overlap'] >= 0.70 and
        metrics['entity_confidence'] >= 0.80 and
        not metrics['privacy_flags'] and
        metrics['domain_specificity'] < 0.60
    )
```

### Rule 2: Custom Domain Classification
```python
def classify_as_custom_domain(metrics):
    """
    Determine if data should go to custom domain ontology
    """
    return (
        metrics['semantic_overlap'] <= 0.30 and
        metrics['related_entities'] >= 5 and
        metrics['entity_confidence'] >= 0.70 and
        metrics['domain_specificity'] >= 0.40 and
        not metrics['privacy_flags']
    )
```

### Rule 3: Isolated Namespace Classification
```python
def classify_as_isolated(metrics):
    """
    Determine if data should go to isolated namespace
    """
    return (
        metrics['privacy_flags'] or
        metrics['interconnectedness'] < 0.30 or
        metrics['entity_confidence'] < 0.50 or
        metrics['user_specified_isolation']
    )
```

## 🔍 Privacy and Security Detection

### Privacy Flag Detection
```python
def detect_privacy_flags(content, entities):
    """
    Detect privacy-sensitive content
    """
    privacy_indicators = [
        'personal', 'private', 'confidential', 'secret',
        'password', 'api_key', 'token', 'credential',
        'email', 'phone', 'address', 'ssn'
    ]
    
    privacy_score = 0
    for indicator in privacy_indicators:
        if indicator.lower() in content.lower():
            privacy_score += 1
    
    # Check for PII in entities
    pii_entities = ['PERSON', 'EMAIL', 'PHONE', 'SSN', 'CREDIT_CARD']
    for entity in entities:
        if entity.label in pii_entities:
            privacy_score += 2
    
    return privacy_score >= 3  # Threshold for privacy flag
```

## 📈 Performance Optimization

### Batch Processing
- Process multiple documents simultaneously
- Maintain chronological order for temporal analysis
- Parallel entity extraction with result aggregation

### Caching Strategy
- Cache entity embeddings for repeated entities
- Store domain-specific classification models
- Maintain recent classification decisions for consistency

### Adaptive Thresholds
- Monitor classification accuracy over time
- Adjust thresholds based on user feedback
- Learn from manual reclassification events

## 🎛️ Configuration Parameters

### Default Classification Thresholds
```yaml
classification_thresholds:
  shared_ontology:
    semantic_overlap: 0.70
    entity_confidence: 0.80
    max_domain_specificity: 0.60
  
  custom_domain:
    max_semantic_overlap: 0.30
    min_related_entities: 5
    min_entity_confidence: 0.70
    min_domain_specificity: 0.40
  
  isolated_namespace:
    max_interconnectedness: 0.30
    min_entity_confidence: 0.50
    privacy_threshold: 3
```

### User-Configurable Settings
```yaml
user_preferences:
  domain_weights:
    programming: 1.0
    documentation: 0.8
    conversations: 0.6
    general_knowledge: 0.4
  
  privacy_sensitivity: "medium"  # low, medium, high
  auto_classification: true
  manual_review_threshold: 0.60
```

---

*This decision matrix provides a robust, quantitative foundation for intelligent data classification while remaining configurable and adaptable to user needs.*
