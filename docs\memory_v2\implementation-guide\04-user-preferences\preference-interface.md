# Phase 4.4: Preference Interface
## User Interface for Preference Management and Feedback

### Overview
This step implements the user interface components for managing preferences, providing feedback, and viewing behavioral insights. This includes both programmatic APIs and user-facing interfaces.

### Time Estimate: 1-2 hours

---

## Step 4.4.1: Create Preference Management API

**File:** `python/helpers/preference_api.py`
**Action:** Create new file

```python
"""
User Preference Management API
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio

from .user_preferences import UserPreferenceManager, PreferenceType, LearningSignal
from .behavioral_analyzer import BehavioralPatternAnalyzer
from .preference_adaptation import PreferenceAdaptationEngine

class PreferenceAPI:
    """
    API for managing user preferences and behavioral insights
    """

    def __init__(self):
        self.preference_managers: Dict[str, UserPreferenceManager] = {}
        self.adaptation_engines: Dict[str, PreferenceAdaptationEngine] = {}

    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get all preferences for a user"""
        
        manager = await self._get_preference_manager(user_id)
        
        preferences = {}
        for key, pref_item in manager.preferences.items():
            preferences[key] = {
                'value': pref_item.value,
                'type': pref_item.preference_type.value,
                'confidence': pref_item.confidence,
                'last_updated': pref_item.last_updated.isoformat(),
                'user_explicit': pref_item.user_explicit,
                'learning_history_count': len(pref_item.learning_history)
            }
        
        return preferences

    async def set_user_preference(
        self,
        user_id: str,
        preference_key: str,
        value: Any,
        preference_type: str
    ) -> Dict[str, Any]:
        """Set a user preference"""
        
        manager = await self._get_preference_manager(user_id)
        
        try:
            pref_type = PreferenceType(preference_type)
            success = await manager.set_preference(
                preference_key, value, pref_type, user_explicit=True
            )
            
            return {
                'success': success,
                'preference_key': preference_key,
                'new_value': value,
                'timestamp': datetime.now().isoformat()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'error': f'Invalid preference type: {preference_type}',
                'valid_types': [t.value for t in PreferenceType]
            }

    async def get_user_behavioral_insights(self, user_id: str) -> Dict[str, Any]:
        """Get behavioral insights for a user"""
        
        manager = await self._get_preference_manager(user_id)
        analyzer = BehavioralPatternAnalyzer(user_id)
        
        # Get analysis report
        report = await analyzer.export_analysis_report()
        
        return {
            'user_id': user_id,
            'insights': report,
            'preference_count': len(manager.preferences),
            'feedback_history_count': len(manager.feedback_history),
            'last_analysis': datetime.now().isoformat()
        }

    async def trigger_preference_adaptation(self, user_id: str) -> Dict[str, Any]:
        """Manually trigger preference adaptation for a user"""
        
        adaptation_engine = await self._get_adaptation_engine(user_id)
        adaptation_session = await adaptation_engine.analyze_and_adapt()
        
        return {
            'user_id': user_id,
            'adaptation_session': adaptation_session,
            'timestamp': datetime.now().isoformat()
        }

    async def record_user_feedback(
        self,
        user_id: str,
        action: str,
        context: Dict[str, Any],
        feedback_type: str,
        value: Any = None
    ) -> Dict[str, Any]:
        """Record user feedback"""
        
        manager = await self._get_preference_manager(user_id)
        
        try:
            signal_type = LearningSignal(feedback_type)
            await manager.record_feedback(action, context, signal_type, value)
            
            return {
                'success': True,
                'user_id': user_id,
                'action': action,
                'feedback_type': feedback_type,
                'timestamp': datetime.now().isoformat()
            }
            
        except ValueError as e:
            return {
                'success': False,
                'error': f'Invalid feedback type: {feedback_type}',
                'valid_types': [s.value for s in LearningSignal]
            }

    async def get_preference_recommendations(self, user_id: str) -> Dict[str, Any]:
        """Get preference recommendations based on behavioral analysis"""
        
        adaptation_engine = await self._get_adaptation_engine(user_id)
        recommendations = await adaptation_engine.behavioral_analyzer.get_pattern_recommendations()
        
        return {
            'user_id': user_id,
            'recommendations': recommendations,
            'recommendation_count': len(recommendations),
            'timestamp': datetime.now().isoformat()
        }

    async def export_user_data(self, user_id: str) -> Dict[str, Any]:
        """Export all user preference and behavioral data"""
        
        manager = await self._get_preference_manager(user_id)
        insights = await self.get_user_behavioral_insights(user_id)
        
        return {
            'user_id': user_id,
            'export_timestamp': datetime.now().isoformat(),
            'preferences': await self.get_user_preferences(user_id),
            'behavioral_insights': insights,
            'feedback_history': [
                {
                    'action': fb.action,
                    'context': fb.context,
                    'feedback_type': fb.feedback_type.value,
                    'timestamp': fb.timestamp.isoformat(),
                    'value': fb.value
                }
                for fb in manager.feedback_history
            ]
        }

    async def reset_user_preferences(self, user_id: str, keep_explicit: bool = True) -> Dict[str, Any]:
        """Reset user preferences to defaults"""
        
        manager = await self._get_preference_manager(user_id)
        
        # Store current explicit preferences if requested
        explicit_prefs = {}
        if keep_explicit:
            explicit_prefs = {
                key: pref for key, pref in manager.preferences.items()
                if pref.user_explicit
            }
        
        # Reinitialize preferences
        manager._initialize_default_preferences()
        
        # Restore explicit preferences
        if keep_explicit:
            for key, pref in explicit_prefs.items():
                manager.preferences[key] = pref
        
        return {
            'success': True,
            'user_id': user_id,
            'reset_timestamp': datetime.now().isoformat(),
            'explicit_preferences_kept': len(explicit_prefs) if keep_explicit else 0,
            'total_preferences': len(manager.preferences)
        }

    async def _get_preference_manager(self, user_id: str) -> UserPreferenceManager:
        """Get or create preference manager for user"""
        if user_id not in self.preference_managers:
            self.preference_managers[user_id] = UserPreferenceManager(user_id)
        return self.preference_managers[user_id]

    async def _get_adaptation_engine(self, user_id: str) -> PreferenceAdaptationEngine:
        """Get or create adaptation engine for user"""
        if user_id not in self.adaptation_engines:
            manager = await self._get_preference_manager(user_id)
            self.adaptation_engines[user_id] = PreferenceAdaptationEngine(manager)
        return self.adaptation_engines[user_id]

# Global API instance
preference_api = PreferenceAPI()
```

**Validation:**
```python
# Test preference API
from python.helpers.preference_api import preference_api

# Test getting user preferences
prefs = await preference_api.get_user_preferences("test_user")
print(f"User has {len(prefs)} preferences")

# Test setting a preference
result = await preference_api.set_user_preference(
    "test_user",
    "domain_weight_programming",
    0.9,
    "domain_weight"
)
print(f"Preference set: {result['success']}")

# Test getting behavioral insights
insights = await preference_api.get_user_behavioral_insights("test_user")
print(f"Behavioral insights generated for user")
```

---

## Step 4.4.2: Create Command-Line Interface

**File:** `python/helpers/preference_cli.py`
**Action:** Create new file

```python
"""
Command-line interface for preference management
"""

import asyncio
import json
from typing import Dict, Any
import argparse

from .preference_api import preference_api

class PreferenceCLI:
    """Command-line interface for user preference management"""

    def __init__(self):
        self.parser = self._create_parser()

    def _create_parser(self) -> argparse.ArgumentParser:
        """Create command-line argument parser"""
        
        parser = argparse.ArgumentParser(
            description='Agent Zero User Preference Management CLI'
        )
        
        subparsers = parser.add_subparsers(dest='command', help='Available commands')

        # Get preferences command
        get_parser = subparsers.add_parser('get', help='Get user preferences')
        get_parser.add_argument('user_id', help='User ID')
        get_parser.add_argument('--format', choices=['json', 'table'], default='table',
                               help='Output format')

        # Set preference command
        set_parser = subparsers.add_parser('set', help='Set user preference')
        set_parser.add_argument('user_id', help='User ID')
        set_parser.add_argument('preference_key', help='Preference key')
        set_parser.add_argument('value', help='Preference value')
        set_parser.add_argument('preference_type', help='Preference type')

        # Insights command
        insights_parser = subparsers.add_parser('insights', help='Get behavioral insights')
        insights_parser.add_argument('user_id', help='User ID')
        insights_parser.add_argument('--format', choices=['json', 'summary'], default='summary',
                                   help='Output format')

        # Adapt command
        adapt_parser = subparsers.add_parser('adapt', help='Trigger preference adaptation')
        adapt_parser.add_argument('user_id', help='User ID')

        # Recommendations command
        rec_parser = subparsers.add_parser('recommendations', help='Get preference recommendations')
        rec_parser.add_argument('user_id', help='User ID')

        # Export command
        export_parser = subparsers.add_parser('export', help='Export user data')
        export_parser.add_argument('user_id', help='User ID')
        export_parser.add_argument('--output', help='Output file path')

        # Reset command
        reset_parser = subparsers.add_parser('reset', help='Reset user preferences')
        reset_parser.add_argument('user_id', help='User ID')
        reset_parser.add_argument('--keep-explicit', action='store_true',
                                 help='Keep explicitly set preferences')

        return parser

    async def run(self, args: list = None) -> None:
        """Run the CLI with given arguments"""
        
        parsed_args = self.parser.parse_args(args)
        
        if not parsed_args.command:
            self.parser.print_help()
            return

        try:
            if parsed_args.command == 'get':
                await self._handle_get_preferences(parsed_args)
            elif parsed_args.command == 'set':
                await self._handle_set_preference(parsed_args)
            elif parsed_args.command == 'insights':
                await self._handle_get_insights(parsed_args)
            elif parsed_args.command == 'adapt':
                await self._handle_trigger_adaptation(parsed_args)
            elif parsed_args.command == 'recommendations':
                await self._handle_get_recommendations(parsed_args)
            elif parsed_args.command == 'export':
                await self._handle_export_data(parsed_args)
            elif parsed_args.command == 'reset':
                await self._handle_reset_preferences(parsed_args)
                
        except Exception as e:
            print(f"Error: {e}")

    async def _handle_get_preferences(self, args) -> None:
        """Handle get preferences command"""
        
        prefs = await preference_api.get_user_preferences(args.user_id)
        
        if args.format == 'json':
            print(json.dumps(prefs, indent=2, default=str))
        else:
            print(f"\nPreferences for user: {args.user_id}")
            print("-" * 50)
            for key, pref in prefs.items():
                print(f"{key:30} = {pref['value']:15} (confidence: {pref['confidence']:.2f})")

    async def _handle_set_preference(self, args) -> None:
        """Handle set preference command"""
        
        # Try to parse value as appropriate type
        value = args.value
        try:
            if value.lower() in ['true', 'false']:
                value = value.lower() == 'true'
            elif '.' in value:
                value = float(value)
            elif value.isdigit():
                value = int(value)
        except:
            pass  # Keep as string
        
        result = await preference_api.set_user_preference(
            args.user_id, args.preference_key, value, args.preference_type
        )
        
        if result['success']:
            print(f"✓ Preference '{args.preference_key}' set to '{value}' for user {args.user_id}")
        else:
            print(f"✗ Failed to set preference: {result.get('error', 'Unknown error')}")

    async def _handle_get_insights(self, args) -> None:
        """Handle get insights command"""
        
        insights = await preference_api.get_user_behavioral_insights(args.user_id)
        
        if args.format == 'json':
            print(json.dumps(insights, indent=2, default=str))
        else:
            print(f"\nBehavioral Insights for user: {args.user_id}")
            print("-" * 50)
            
            metrics = insights['insights']['usage_metrics']
            print(f"Total interactions: {metrics['total_interactions']}")
            print(f"Average session length: {metrics['avg_session_length_minutes']:.1f} minutes")
            print(f"Preferred domains: {', '.join(metrics['preferred_domains'])}")
            print(f"Feedback ratio: {metrics['feedback_ratio']:.2f}")
            print(f"Error rate: {metrics['error_rate']:.2f}")
            
            patterns = insights['insights']['detected_patterns']
            print(f"\nDetected patterns: {len(patterns)}")
            for pattern in patterns[:5]:  # Show top 5
                print(f"  - {pattern['type']}: confidence {pattern['confidence']:.2f}")

    async def _handle_trigger_adaptation(self, args) -> None:
        """Handle trigger adaptation command"""
        
        result = await preference_api.trigger_preference_adaptation(args.user_id)
        session = result['adaptation_session']
        
        print(f"\nPreference adaptation completed for user: {args.user_id}")
        print(f"Patterns analyzed: {session['patterns_analyzed']}")
        print(f"Recommendations generated: {session['recommendations_generated']}")
        print(f"Adaptations applied: {session['adaptations_applied']}")
        
        if session['adaptations']:
            print("\nAdaptations made:")
            for adaptation in session['adaptations']:
                print(f"  - {adaptation['type']}: {adaptation.get('reason', 'No reason provided')}")

    async def _handle_get_recommendations(self, args) -> None:
        """Handle get recommendations command"""
        
        result = await preference_api.get_preference_recommendations(args.user_id)
        recommendations = result['recommendations']
        
        print(f"\nPreference recommendations for user: {args.user_id}")
        print("-" * 50)
        
        if not recommendations:
            print("No recommendations available at this time.")
        else:
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec['type']}")
                print(f"   Confidence: {rec['confidence']:.2f}")
                if 'reason' in rec:
                    print(f"   Reason: {rec['reason']}")
                print()

    async def _handle_export_data(self, args) -> None:
        """Handle export data command"""
        
        data = await preference_api.export_user_data(args.user_id)
        
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            print(f"User data exported to: {args.output}")
        else:
            print(json.dumps(data, indent=2, default=str))

    async def _handle_reset_preferences(self, args) -> None:
        """Handle reset preferences command"""
        
        result = await preference_api.reset_user_preferences(
            args.user_id, keep_explicit=args.keep_explicit
        )
        
        print(f"✓ Preferences reset for user: {args.user_id}")
        print(f"Total preferences: {result['total_preferences']}")
        if args.keep_explicit:
            print(f"Explicit preferences kept: {result['explicit_preferences_kept']}")

# CLI instance
preference_cli = PreferenceCLI()

async def main():
    """Main CLI entry point"""
    import sys
    await preference_cli.run(sys.argv[1:])

if __name__ == '__main__':
    asyncio.run(main())
```

**Validation:**
```bash
# Test CLI commands
python -m python.helpers.preference_cli get test_user
python -m python.helpers.preference_cli set test_user domain_weight_programming 0.9 domain_weight
python -m python.helpers.preference_cli insights test_user
python -m python.helpers.preference_cli recommendations test_user
```

---

## Step 4.4.3: Integration with Agent Zero

**File:** `python/helpers/agent_integration.py`
**Action:** Create new file

```python
"""
Integration of preference system with Agent Zero
"""

from typing import Dict, Any, Optional
import asyncio

from .preference_api import preference_api
from .memory_abstraction import EnhancedMemoryAbstractionLayer

class AgentPreferenceIntegration:
    """
    Integrates preference system with Agent Zero's main functionality
    """

    def __init__(self, memory_layer: EnhancedMemoryAbstractionLayer):
        self.memory_layer = memory_layer

    async def process_user_message_with_preferences(
        self,
        user_id: str,
        message: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process user message with preference-aware storage and learning"""

        # Store message using user preferences
        doc_id, classification_result = await self.memory_layer.store_with_user_preferences(
            content=message,
            content_type="conversation",
            metadata={
                'message_type': 'user_input',
                'context': context,
                'timestamp': context.get('timestamp')
            },
            user_id=user_id
        )

        # Record interaction for behavioral learning
        await preference_api.record_user_feedback(
            user_id=user_id,
            action='message_processing',
            context={
                'doc_id': doc_id,
                'classification_result': classification_result.__dict__,
                'message_length': len(message),
                'context': context
            },
            feedback_type='implicit'
        )

        return {
            'doc_id': doc_id,
            'classification_result': classification_result,
            'storage_location': classification_result.strategy.value,
            'confidence': classification_result.confidence
        }

    async def get_user_preference_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary of user preferences for display"""

        prefs = await preference_api.get_user_preferences(user_id)
        insights = await preference_api.get_user_behavioral_insights(user_id)

        # Extract key preferences
        key_prefs = {
            'domain_weights': {
                k.replace('domain_weight_', ''): v['value'] 
                for k, v in prefs.items() 
                if k.startswith('domain_weight_')
            },
            'privacy_level': prefs.get('privacy_sensitivity_level', {}).get('value', 'medium'),
            'search_limit': prefs.get('search_result_limit', {}).get('value', 10),
            'working_memory_days': prefs.get('memory_working_retention_days', {}).get('value', 7)
        }

        # Extract key insights
        usage_metrics = insights['insights']['usage_metrics']
        key_insights = {
            'total_interactions': usage_metrics['total_interactions'],
            'preferred_domains': usage_metrics['preferred_domains'][:3],
            'feedback_ratio': usage_metrics['feedback_ratio'],
            'avg_session_length': usage_metrics['avg_session_length_minutes']
        }

        return {
            'user_id': user_id,
            'key_preferences': key_prefs,
            'behavioral_insights': key_insights,
            'adaptation_available': len(insights['insights']['recommendations']) > 0
        }

    async def suggest_preference_adjustments(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user-friendly preference adjustment suggestions"""

        recommendations = await preference_api.get_preference_recommendations(user_id)
        
        user_friendly_suggestions = []
        for rec in recommendations['recommendations']:
            if rec['type'] == 'increase_domain_weight':
                user_friendly_suggestions.append({
                    'title': f"Boost {rec['domain']} content priority",
                    'description': f"You seem to work well with {rec['domain']} content. Would you like me to prioritize it more?",
                    'confidence': rec['confidence'],
                    'action': 'increase_domain_weight',
                    'parameters': rec
                })
            elif rec['type'] == 'adjust_search_limit':
                user_friendly_suggestions.append({
                    'title': "Optimize search results",
                    'description': f"You typically use about {rec['current_avg_used']:.0f} search results. Should I adjust the default limit?",
                    'confidence': rec['confidence'],
                    'action': 'adjust_search_limit',
                    'parameters': rec
                })
            elif rec['type'] == 'review_classification_thresholds':
                user_friendly_suggestions.append({
                    'title': "Improve content classification",
                    'description': "I notice some classification decisions might not match your preferences. Should I adjust my confidence thresholds?",
                    'confidence': rec['confidence'],
                    'action': 'review_thresholds',
                    'parameters': rec
                })

        return user_friendly_suggestions

    async def apply_user_preference_choice(
        self,
        user_id: str,
        choice: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply user's choice from preference suggestions"""

        action = choice['action']
        parameters = choice['parameters']

        if action == 'increase_domain_weight':
            domain = parameters['domain']
            current_weight = await preference_api.get_user_preferences(user_id)
            current_value = current_weight.get(f'domain_weight_{domain}', {}).get('value', 0.5)
            new_value = min(1.0, current_value + 0.1)
            
            result = await preference_api.set_user_preference(
                user_id, f'domain_weight_{domain}', new_value, 'domain_weight'
            )
            
            return {
                'success': result['success'],
                'message': f"Increased {domain} content priority to {new_value:.1f}",
                'preference_updated': f'domain_weight_{domain}'
            }

        elif action == 'adjust_search_limit':
            new_limit = parameters['recommended_limit']
            
            result = await preference_api.set_user_preference(
                user_id, 'search_result_limit', new_limit, 'search_preference'
            )
            
            return {
                'success': result['success'],
                'message': f"Adjusted search result limit to {new_limit}",
                'preference_updated': 'search_result_limit'
            }

        elif action == 'review_thresholds':
            current_threshold = await preference_api.get_user_preferences(user_id)
            current_value = current_threshold.get('classification_confidence_threshold', {}).get('value', 0.7)
            new_value = max(0.5, current_value - 0.05)
            
            result = await preference_api.set_user_preference(
                user_id, 'classification_confidence_threshold', new_value, 'classification_threshold'
            )
            
            return {
                'success': result['success'],
                'message': f"Adjusted classification confidence threshold to {new_value:.2f}",
                'preference_updated': 'classification_confidence_threshold'
            }

        return {'success': False, 'message': 'Unknown action type'}
```

**Validation:**
```python
# Test Agent Zero integration
from python.helpers.agent_integration import AgentPreferenceIntegration
from python.helpers.memory_abstraction import EnhancedMemoryAbstractionLayer

memory_layer = EnhancedMemoryAbstractionLayer()
integration = AgentPreferenceIntegration(memory_layer)

# Test processing user message
result = await integration.process_user_message_with_preferences(
    user_id="test_user",
    message="How do I implement machine learning in Python?",
    context={'session_id': 'test_session'}
)

print(f"Message processed: {result['storage_location']}")

# Test getting preference summary
summary = await integration.get_user_preference_summary("test_user")
print(f"User preference summary generated")

# Test getting suggestions
suggestions = await integration.suggest_preference_adjustments("test_user")
print(f"Generated {len(suggestions)} preference suggestions")
```

---

## Phase 4 Complete

✅ **Phase 4 Complete - Validation Checklist:**
- [ ] Preference management API provides full CRUD operations
- [ ] Command-line interface allows preference management
- [ ] Agent Zero integration processes messages with preferences
- [ ] User-friendly preference suggestions are generated
- [ ] Preference choices can be applied through the interface
- [ ] Behavioral insights are accessible through the API
- [ ] Export and reset functionality works correctly

**Next:** Proceed to Phase 5 - Data Separation Enforcement

---

## Summary

Phase 4 has successfully implemented a comprehensive User Preference Framework that includes:

1. **Preference System** - Core data structures and management for user preferences
2. **Adaptive Learning** - Behavioral pattern analysis and automatic preference adaptation
3. **Classification Integration** - Personalized classification based on user preferences
4. **User Interface** - APIs and CLI for preference management and feedback collection

The system now supports:
- Automatic learning from user behavior
- Personalized classification decisions
- User feedback collection and processing
- Preference recommendations and adjustments
- Full integration with Agent Zero's core functionality

Users can now have personalized experiences that adapt to their usage patterns and preferences, improving classification accuracy and user satisfaction over time.
