# Phase 5: Data Separation Enforcement - Framework
## Strict Data Separation Architecture

This document provides implementation instructions for the data separation framework that ensures zero duplication between the history system and knowledge graph systems.

## 🎯 Overview

The data separation framework enforces strict boundaries between:
- **History System**: Raw conversations, message threads, chat metadata
- **Knowledge Graph System**: Processed entities, relationships, temporal facts
- **Integration Layer**: Cross-system queries without data duplication

## 📊 Architecture Components

### Data Boundary Definitions

```mermaid
graph TB
    A[Input Data] --> B[Data Classifier]
    B --> C{Data Type?}
    C -->|Conversation| D[History System]
    C -->|Knowledge| E[Knowledge Graph]
    C -->|Mixed| F[Data Splitter]
    F --> G[Conversation Parts] --> D
    F --> H[Knowledge Parts] --> E
    D --> I[History Storage]
    E --> J[Graph Storage]
    I --> K[Integration Layer]
    J --> K
    K --> L[Unified Query Interface]
```

## 🔧 Implementation

### Step 5.1: Create Data Separation Framework

**File:** `python/helpers/data_separation_framework.py`
**Action:** Create new file

```python
"""
Data Separation Framework - Enforces strict boundaries between systems
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import uuid
import asyncio

class DataDomain(Enum):
    """Data domain classifications"""
    HISTORY = "history"
    KNOWLEDGE = "knowledge"
    MIXED = "mixed"
    UNKNOWN = "unknown"

class DataType(Enum):
    """Specific data type classifications"""
    CONVERSATION_MESSAGE = "conversation_message"
    CHAT_METADATA = "chat_metadata"
    USER_INTERACTION = "user_interaction"
    KNOWLEDGE_DOCUMENT = "knowledge_document"
    ENTITY_RELATIONSHIP = "entity_relationship"
    TEMPORAL_FACT = "temporal_fact"
    PROCESSED_KNOWLEDGE = "processed_knowledge"

@dataclass
class DataItem:
    """Unified data item representation"""
    id: str
    content: Any
    data_type: DataType
    data_domain: DataDomain
    metadata: Dict[str, Any]
    created_at: datetime
    source_system: str
    processing_flags: Dict[str, bool]

@dataclass
class SeparationResult:
    """Result of data separation operation"""
    history_items: List[DataItem]
    knowledge_items: List[DataItem]
    mixed_items: List[DataItem]
    separation_confidence: float
    processing_time_ms: float
    warnings: List[str]

class DataSeparationInterface(ABC):
    """Abstract interface for data separation"""
    
    @abstractmethod
    async def classify_data_domain(self, data: Any, metadata: Dict[str, Any]) -> DataDomain:
        """Classify data into appropriate domain"""
        pass
    
    @abstractmethod
    async def separate_mixed_data(self, data: Any, metadata: Dict[str, Any]) -> SeparationResult:
        """Separate mixed data into appropriate domains"""
        pass
    
    @abstractmethod
    async def validate_separation(self, result: SeparationResult) -> bool:
        """Validate that separation was performed correctly"""
        pass

class DataSeparationFramework(DataSeparationInterface):
    """
    Core data separation framework implementation
    """
    
    def __init__(self):
        self.separation_rules = self._initialize_separation_rules()
        self.validation_rules = self._initialize_validation_rules()
        self.processing_stats = {
            'total_processed': 0,
            'history_items': 0,
            'knowledge_items': 0,
            'mixed_items': 0,
            'validation_failures': 0
        }
    
    def _initialize_separation_rules(self) -> Dict[str, Any]:
        """Initialize rules for data separation"""
        return {
            'conversation_indicators': [
                'user:', 'assistant:', 'human:', 'ai:', 'chat:', 'message:',
                'conversation', 'dialogue', 'discussion', 'talk'
            ],
            'knowledge_indicators': [
                'entity:', 'relationship:', 'fact:', 'concept:', 'definition:',
                'knowledge', 'information', 'data', 'reference'
            ],
            'metadata_keys': {
                'history': ['conversation_id', 'message_id', 'user_id', 'session_id'],
                'knowledge': ['entity_id', 'relationship_id', 'concept_id', 'domain']
            },
            'content_patterns': {
                'history': [
                    r'^\s*(user|human|assistant|ai)\s*:',
                    r'conversation\s+history',
                    r'chat\s+log'
                ],
                'knowledge': [
                    r'entity\s*:\s*\w+',
                    r'relationship\s*:\s*\w+',
                    r'fact\s*:\s*'
                ]
            }
        }
    
    def _initialize_validation_rules(self) -> Dict[str, Any]:
        """Initialize validation rules for separation"""
        return {
            'no_duplication': True,
            'complete_coverage': True,
            'domain_consistency': True,
            'metadata_integrity': True,
            'content_preservation': True
        }
    
    async def classify_data_domain(self, data: Any, metadata: Dict[str, Any]) -> DataDomain:
        """
        Classify data into appropriate domain using multiple indicators
        """
        
        # Check metadata indicators
        metadata_domain = self._classify_by_metadata(metadata)
        if metadata_domain != DataDomain.UNKNOWN:
            return metadata_domain
        
        # Check content indicators
        content_domain = await self._classify_by_content(data)
        if content_domain != DataDomain.UNKNOWN:
            return content_domain
        
        # Check structural indicators
        structure_domain = self._classify_by_structure(data, metadata)
        if structure_domain != DataDomain.UNKNOWN:
            return structure_domain
        
        # Default to mixed if unclear
        return DataDomain.MIXED
    
    def _classify_by_metadata(self, metadata: Dict[str, Any]) -> DataDomain:
        """Classify based on metadata keys and values"""
        
        history_keys = self.separation_rules['metadata_keys']['history']
        knowledge_keys = self.separation_rules['metadata_keys']['knowledge']
        
        history_score = sum(1 for key in history_keys if key in metadata)
        knowledge_score = sum(1 for key in knowledge_keys if key in metadata)
        
        if history_score > knowledge_score and history_score > 0:
            return DataDomain.HISTORY
        elif knowledge_score > history_score and knowledge_score > 0:
            return DataDomain.KNOWLEDGE
        else:
            return DataDomain.UNKNOWN
    
    async def _classify_by_content(self, data: Any) -> DataDomain:
        """Classify based on content analysis"""
        import re
        
        if not isinstance(data, str):
            data = str(data)
        
        data_lower = data.lower()
        
        # Check for conversation indicators
        conversation_indicators = self.separation_rules['conversation_indicators']
        conv_score = sum(1 for indicator in conversation_indicators if indicator in data_lower)
        
        # Check for knowledge indicators
        knowledge_indicators = self.separation_rules['knowledge_indicators']
        know_score = sum(1 for indicator in knowledge_indicators if indicator in data_lower)
        
        # Check content patterns
        history_patterns = self.separation_rules['content_patterns']['history']
        knowledge_patterns = self.separation_rules['content_patterns']['knowledge']
        
        history_pattern_matches = sum(1 for pattern in history_patterns if re.search(pattern, data, re.IGNORECASE))
        knowledge_pattern_matches = sum(1 for pattern in knowledge_patterns if re.search(pattern, data, re.IGNORECASE))
        
        # Calculate total scores
        total_history_score = conv_score + history_pattern_matches
        total_knowledge_score = know_score + knowledge_pattern_matches
        
        if total_history_score > total_knowledge_score and total_history_score > 0:
            return DataDomain.HISTORY
        elif total_knowledge_score > total_history_score and total_knowledge_score > 0:
            return DataDomain.KNOWLEDGE
        else:
            return DataDomain.UNKNOWN
    
    def _classify_by_structure(self, data: Any, metadata: Dict[str, Any]) -> DataDomain:
        """Classify based on data structure"""
        
        # Check for conversation-like structure
        if isinstance(data, dict):
            if any(key in data for key in ['messages', 'conversation', 'chat', 'dialogue']):
                return DataDomain.HISTORY
            if any(key in data for key in ['entities', 'relationships', 'facts', 'concepts']):
                return DataDomain.KNOWLEDGE
        
        # Check for list of messages
        if isinstance(data, list):
            if len(data) > 0 and isinstance(data[0], dict):
                first_item = data[0]
                if any(key in first_item for key in ['role', 'content', 'message', 'user', 'assistant']):
                    return DataDomain.HISTORY
                if any(key in first_item for key in ['entity', 'relationship', 'fact', 'concept']):
                    return DataDomain.KNOWLEDGE
        
        return DataDomain.UNKNOWN
    
    async def separate_mixed_data(self, data: Any, metadata: Dict[str, Any]) -> SeparationResult:
        """
        Separate mixed data into appropriate domains
        """
        import time
        start_time = time.time()
        
        history_items = []
        knowledge_items = []
        mixed_items = []
        warnings = []
        
        try:
            # Handle different data types
            if isinstance(data, dict):
                result = await self._separate_dict_data(data, metadata)
            elif isinstance(data, list):
                result = await self._separate_list_data(data, metadata)
            elif isinstance(data, str):
                result = await self._separate_string_data(data, metadata)
            else:
                # Unknown data type - treat as mixed
                mixed_item = DataItem(
                    id=str(uuid.uuid4()),
                    content=data,
                    data_type=DataType.PROCESSED_KNOWLEDGE,
                    data_domain=DataDomain.MIXED,
                    metadata=metadata,
                    created_at=datetime.now(),
                    source_system="unknown",
                    processing_flags={'requires_manual_review': True}
                )
                mixed_items.append(mixed_item)
                warnings.append(f"Unknown data type: {type(data)}")
                result = SeparationResult([], [], [mixed_item], 0.5, 0, warnings)
            
            # Update processing statistics
            self.processing_stats['total_processed'] += 1
            self.processing_stats['history_items'] += len(result.history_items)
            self.processing_stats['knowledge_items'] += len(result.knowledge_items)
            self.processing_stats['mixed_items'] += len(result.mixed_items)
            
            # Calculate processing time
            processing_time = (time.time() - start_time) * 1000
            result.processing_time_ms = processing_time
            
            return result
            
        except Exception as e:
            warnings.append(f"Error during separation: {str(e)}")
            processing_time = (time.time() - start_time) * 1000
            
            return SeparationResult(
                history_items=[],
                knowledge_items=[],
                mixed_items=[],
                separation_confidence=0.0,
                processing_time_ms=processing_time,
                warnings=warnings
            )
    
    async def _separate_dict_data(self, data: Dict[str, Any], metadata: Dict[str, Any]) -> SeparationResult:
        """Separate dictionary data"""
        history_items = []
        knowledge_items = []
        mixed_items = []
        warnings = []
        
        # Separate keys based on domain
        for key, value in data.items():
            item_domain = await self.classify_data_domain(value, {**metadata, 'key': key})
            
            item = DataItem(
                id=str(uuid.uuid4()),
                content={key: value},
                data_type=self._determine_data_type(key, value),
                data_domain=item_domain,
                metadata={**metadata, 'original_key': key},
                created_at=datetime.now(),
                source_system=metadata.get('source_system', 'unknown'),
                processing_flags={'separated_from_dict': True}
            )
            
            if item_domain == DataDomain.HISTORY:
                history_items.append(item)
            elif item_domain == DataDomain.KNOWLEDGE:
                knowledge_items.append(item)
            else:
                mixed_items.append(item)
        
        # Calculate confidence based on clear classifications
        total_items = len(history_items) + len(knowledge_items) + len(mixed_items)
        clear_items = len(history_items) + len(knowledge_items)
        confidence = clear_items / total_items if total_items > 0 else 0.0
        
        return SeparationResult(
            history_items=history_items,
            knowledge_items=knowledge_items,
            mixed_items=mixed_items,
            separation_confidence=confidence,
            processing_time_ms=0,  # Will be set by caller
            warnings=warnings
        )
    
    async def _separate_list_data(self, data: List[Any], metadata: Dict[str, Any]) -> SeparationResult:
        """Separate list data"""
        history_items = []
        knowledge_items = []
        mixed_items = []
        warnings = []
        
        for i, item in enumerate(data):
            item_domain = await self.classify_data_domain(item, {**metadata, 'list_index': i})
            
            data_item = DataItem(
                id=str(uuid.uuid4()),
                content=item,
                data_type=self._determine_data_type(f"item_{i}", item),
                data_domain=item_domain,
                metadata={**metadata, 'list_index': i},
                created_at=datetime.now(),
                source_system=metadata.get('source_system', 'unknown'),
                processing_flags={'separated_from_list': True}
            )
            
            if item_domain == DataDomain.HISTORY:
                history_items.append(data_item)
            elif item_domain == DataDomain.KNOWLEDGE:
                knowledge_items.append(data_item)
            else:
                mixed_items.append(data_item)
        
        # Calculate confidence
        total_items = len(history_items) + len(knowledge_items) + len(mixed_items)
        clear_items = len(history_items) + len(knowledge_items)
        confidence = clear_items / total_items if total_items > 0 else 0.0
        
        return SeparationResult(
            history_items=history_items,
            knowledge_items=knowledge_items,
            mixed_items=mixed_items,
            separation_confidence=confidence,
            processing_time_ms=0,
            warnings=warnings
        )
    
    async def _separate_string_data(self, data: str, metadata: Dict[str, Any]) -> SeparationResult:
        """Separate string data"""
        domain = await self.classify_data_domain(data, metadata)
        
        item = DataItem(
            id=str(uuid.uuid4()),
            content=data,
            data_type=self._determine_data_type("string_content", data),
            data_domain=domain,
            metadata=metadata,
            created_at=datetime.now(),
            source_system=metadata.get('source_system', 'unknown'),
            processing_flags={'string_data': True}
        )
        
        if domain == DataDomain.HISTORY:
            return SeparationResult([item], [], [], 1.0, 0, [])
        elif domain == DataDomain.KNOWLEDGE:
            return SeparationResult([], [item], [], 1.0, 0, [])
        else:
            return SeparationResult([], [], [item], 0.5, 0, ["Unclear domain classification"])
    
    def _determine_data_type(self, key: str, value: Any) -> DataType:
        """Determine specific data type based on key and value"""
        
        key_lower = key.lower()
        
        # Conversation-related types
        if any(indicator in key_lower for indicator in ['message', 'chat', 'conversation', 'dialogue']):
            return DataType.CONVERSATION_MESSAGE
        if any(indicator in key_lower for indicator in ['user', 'session', 'interaction']):
            return DataType.USER_INTERACTION
        if any(indicator in key_lower for indicator in ['metadata', 'info', 'data']):
            return DataType.CHAT_METADATA
        
        # Knowledge-related types
        if any(indicator in key_lower for indicator in ['entity', 'concept', 'object']):
            return DataType.ENTITY_RELATIONSHIP
        if any(indicator in key_lower for indicator in ['fact', 'assertion', 'statement']):
            return DataType.TEMPORAL_FACT
        if any(indicator in key_lower for indicator in ['document', 'knowledge', 'reference']):
            return DataType.KNOWLEDGE_DOCUMENT
        
        # Default based on content
        if isinstance(value, str) and len(value) > 100:
            return DataType.KNOWLEDGE_DOCUMENT
        else:
            return DataType.PROCESSED_KNOWLEDGE
    
    async def validate_separation(self, result: SeparationResult) -> bool:
        """
        Validate that separation was performed correctly
        """
        validation_passed = True
        
        # Check for data duplication
        if self.validation_rules['no_duplication']:
            if not self._validate_no_duplication(result):
                validation_passed = False
                self.processing_stats['validation_failures'] += 1
        
        # Check for complete coverage
        if self.validation_rules['complete_coverage']:
            if not self._validate_complete_coverage(result):
                validation_passed = False
                self.processing_stats['validation_failures'] += 1
        
        # Check domain consistency
        if self.validation_rules['domain_consistency']:
            if not self._validate_domain_consistency(result):
                validation_passed = False
                self.processing_stats['validation_failures'] += 1
        
        return validation_passed
    
    def _validate_no_duplication(self, result: SeparationResult) -> bool:
        """Validate that no data is duplicated across domains"""
        all_items = result.history_items + result.knowledge_items + result.mixed_items
        
        # Check for duplicate IDs
        ids = [item.id for item in all_items]
        if len(ids) != len(set(ids)):
            return False
        
        # Check for duplicate content (simplified)
        content_hashes = []
        for item in all_items:
            content_hash = hash(str(item.content)[:100])  # First 100 chars
            if content_hash in content_hashes:
                return False
            content_hashes.append(content_hash)
        
        return True
    
    def _validate_complete_coverage(self, result: SeparationResult) -> bool:
        """Validate that all input data is covered in output"""
        # This is a simplified check - in production, would need more sophisticated validation
        total_items = len(result.history_items) + len(result.knowledge_items) + len(result.mixed_items)
        return total_items > 0
    
    def _validate_domain_consistency(self, result: SeparationResult) -> bool:
        """Validate that items are in correct domains"""
        
        # Check history items
        for item in result.history_items:
            if item.data_domain != DataDomain.HISTORY:
                return False
        
        # Check knowledge items
        for item in result.knowledge_items:
            if item.data_domain != DataDomain.KNOWLEDGE:
                return False
        
        # Mixed items can have any domain
        return True
    
    def get_separation_stats(self) -> Dict[str, Any]:
        """Get statistics about separation operations"""
        return {
            'processing_stats': self.processing_stats.copy(),
            'separation_rules_count': len(self.separation_rules),
            'validation_rules_count': len(self.validation_rules)
        }
```

**Validation:**
```python
# Test data separation framework
from python.helpers.data_separation_framework import DataSeparationFramework, DataDomain

framework = DataSeparationFramework()

# Test classification
test_data = "user: Hello, how are you?\nassistant: I'm doing well, thank you!"
domain = await framework.classify_data_domain(test_data, {})
print(f"Classified as: {domain}")

# Test separation
mixed_data = {
    "conversation": "user: Hello\nassistant: Hi there!",
    "entities": ["user", "assistant", "greeting"],
    "metadata": {"session_id": "123"}
}
result = await framework.separate_mixed_data(mixed_data, {"source": "test"})
print(f"Separated into {len(result.history_items)} history, {len(result.knowledge_items)} knowledge items")
```

---

**Next Step**: [Boundary Enforcement](boundary-enforcement.md)
