![Agent Zero Logo](../res/header.png)

# Agent Zero Documentation v2.0
## Enhanced with Graphiti Temporal Knowledge Graph Integration

Welcome to the comprehensive documentation for Agent Zero v2.0, featuring advanced memory systems powered by Graphiti temporal knowledge graphs alongside traditional FAISS backends.

## 🚀 Quick Start

- **[Installation Guide](getting-started/installation.md):** Set up Agent Zero with memory backend options
- **[Quick Start](getting-started/quickstart.md):** Get up and running in minutes
- **[Configuration](getting-started/configuration.md):** Configure memory backends and settings

## 📚 Core Concepts

- **[Architecture Overview](core-concepts/architecture.md):** Understand the enhanced framework architecture
- **[Agent System](core-concepts/agents.md):** Multi-agent cooperation and hierarchy
- **[Tools & Instruments](core-concepts/tools-instruments.md):** Extensible tool system and custom instruments
- **[Extensions](core-concepts/extensions.md):** Framework extensions and customization

## 🧠 Memory Systems

- **[Memory Overview](memory-systems/overview.md):** Understanding Agent Zero's memory architecture
- **[Graphiti Backend](memory-systems/graphiti-backend.md):** Temporal knowledge graph memory system
- **[FAISS Backend](memory-systems/faiss-backend.md):** Traditional vector-based memory system
- **[Memory Configuration](memory-systems/configuration.md):** Configuring and switching between backends
- **[Knowledge Management](memory-systems/knowledge-management.md):** Document import and entity extraction

## 🔧 Advanced Features

- **[Multi-Agent Cooperation](advanced-features/multi-agent.md):** Agent delegation and task management
- **[Docker Integration](advanced-features/docker-setup.md):** Containerized runtime environment
- **[Web UI Features](advanced-features/web-ui.md):** Browser-based interface capabilities
- **[Voice Interface](advanced-features/voice-interface.md):** Speech-to-text and text-to-speech
- **[File Management](advanced-features/file-management.md):** File browser and attachment handling

## 👨‍💻 Developer Guide

- **[Contributing](developer-guide/contributing.md):** How to contribute to Agent Zero
- **[API Reference](developer-guide/api-reference.md):** Complete API documentation
- **[Testing Guide](developer-guide/testing.md):** Testing framework and best practices
- **[Troubleshooting](developer-guide/troubleshooting.md):** Common issues and solutions
- **[Custom Tools](developer-guide/custom-tools.md):** Creating custom tools and instruments

## 🌟 What's New in v2.0

### Enhanced Memory Architecture
- **Graphiti Integration**: Temporal knowledge graph for advanced relationship modeling
- **Unified Memory Abstraction**: Seamless switching between FAISS and Graphiti backends
- **Bulk Episode Processing**: Efficient handling of large knowledge imports
- **Entity Extraction**: Automatic entity and relationship detection from documents

### Improved Agent Capabilities
- **Enhanced Multi-Agent System**: Better task delegation and coordination
- **Advanced Tool System**: More reliable tool usage with small models
- **Extended Knowledge Management**: Sophisticated document processing and retrieval
- **Persistent Memory**: Long-term memory retention across sessions

### Developer Experience
- **Modular Architecture**: Clean separation of concerns and extensibility
- **Comprehensive Testing**: Robust testing framework for reliability
- **Better Documentation**: This comprehensive documentation system
- **Docker Integration**: Standardized runtime environment

## 🔗 Community & Support

- **Join the Community:** [Skool](https://www.skool.com/agent-zero) | [Discord](https://discord.gg/Z2tun2N3)
- **Share Your Work:** [Show and Tell](https://github.com/frdel/agent-zero/discussions/categories/show-and-tell)
- **Report Issues:** [GitHub Issues](https://github.com/frdel/agent-zero/issues)
- **Legacy Documentation:** [Previous Version](../legacy/README.md)

## 📖 Documentation Structure

This documentation is organized into logical sections:

- **Getting Started**: Installation, setup, and basic configuration
- **Core Concepts**: Fundamental architecture and components
- **Memory Systems**: Detailed coverage of memory backends and knowledge management
- **Advanced Features**: Complex functionality and integrations
- **Developer Guide**: Contributing, API reference, and troubleshooting

Each section builds upon the previous, allowing you to progress from basic setup to advanced customization and development.

---

**Ready to get started?** Begin with the [Installation Guide](getting-started/installation.md) to set up your Agent Zero environment.
