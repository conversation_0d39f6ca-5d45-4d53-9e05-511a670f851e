# Phase 1: Foundation Setup - Base Framework
## Core Classification Framework Implementation

This document provides implementation for the base classification framework that serves as the foundation for the intelligent data classification system.

## 🏗️ Framework Architecture

### Core Components
1. **Classification Engine Interface**: Abstract base for all classification engines
2. **Performance Tracker**: Monitors and tracks classification performance
3. **Result Types**: Standardized result and metric structures
4. **Error Handling**: Comprehensive error management system

## 🔧 Implementation

### Step 1.3: Create Base Classification Framework

**File:** `python/helpers/classification_engine.py`
**Action:** Create new file

```python
"""
Base classification framework for intelligent data classification system
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time
import asyncio
from collections import deque, defaultdict

from .classification_config import ClassificationConfig, ClassificationStrategy

class ClassificationStatus(Enum):
    """Classification operation status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ClassificationPriority(Enum):
    """Classification priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4

@dataclass
class ClassificationMetrics:
    """Comprehensive metrics for classification decisions"""
    
    # Core similarity metrics
    semantic_overlap: float = 0.0
    entity_confidence: float = 0.0
    domain_specificity: float = 0.0
    relationship_density: float = 0.0
    
    # Privacy and security metrics
    privacy_score: int = 0
    privacy_flags: bool = False
    
    # Interconnectedness metrics
    interconnectedness: float = 0.0
    temporal_relevance: float = 1.0
    related_entities_count: int = 0
    
    # User preference metrics
    user_specified_isolation: bool = False
    user_domain_preference: Optional[str] = None
    
    # Quality metrics
    confidence_variance: float = 0.0
    processing_complexity: float = 0.0
    data_quality_score: float = 1.0
    
    # Additional context
    detected_language: Optional[str] = None
    content_length: int = 0
    entity_types: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary"""
        return {
            'semantic_overlap': self.semantic_overlap,
            'entity_confidence': self.entity_confidence,
            'domain_specificity': self.domain_specificity,
            'relationship_density': self.relationship_density,
            'privacy_score': self.privacy_score,
            'privacy_flags': self.privacy_flags,
            'interconnectedness': self.interconnectedness,
            'temporal_relevance': self.temporal_relevance,
            'related_entities_count': self.related_entities_count,
            'user_specified_isolation': self.user_specified_isolation,
            'user_domain_preference': self.user_domain_preference,
            'confidence_variance': self.confidence_variance,
            'processing_complexity': self.processing_complexity,
            'data_quality_score': self.data_quality_score,
            'detected_language': self.detected_language,
            'content_length': self.content_length,
            'entity_types': self.entity_types
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClassificationMetrics':
        """Create metrics from dictionary"""
        return cls(**{k: v for k, v in data.items() if hasattr(cls, k)})

@dataclass
class ClassificationResult:
    """Result of a classification operation"""
    
    # Core classification result
    strategy: ClassificationStrategy
    confidence: float
    metrics: ClassificationMetrics
    reasoning: str
    
    # Performance metrics
    processing_time_ms: float
    
    # Storage location information
    namespace: Optional[str] = None
    domain: Optional[str] = None
    
    # Operation metadata
    classification_id: str = field(default_factory=lambda: f"cls_{int(time.time() * 1000)}")
    timestamp: datetime = field(default_factory=datetime.now)
    status: ClassificationStatus = ClassificationStatus.COMPLETED
    
    # Quality assurance
    validation_passed: bool = True
    validation_warnings: List[str] = field(default_factory=list)
    
    # Additional context
    fallback_used: bool = False
    cache_hit: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary"""
        return {
            'classification_id': self.classification_id,
            'strategy': self.strategy.value,
            'confidence': self.confidence,
            'metrics': self.metrics.to_dict(),
            'reasoning': self.reasoning,
            'processing_time_ms': self.processing_time_ms,
            'namespace': self.namespace,
            'domain': self.domain,
            'timestamp': self.timestamp.isoformat(),
            'status': self.status.value,
            'validation_passed': self.validation_passed,
            'validation_warnings': self.validation_warnings,
            'fallback_used': self.fallback_used,
            'cache_hit': self.cache_hit
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ClassificationResult':
        """Create result from dictionary"""
        # Convert strategy and status from strings
        data['strategy'] = ClassificationStrategy(data['strategy'])
        data['status'] = ClassificationStatus(data['status'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['metrics'] = ClassificationMetrics.from_dict(data['metrics'])
        
        return cls(**{k: v for k, v in data.items() if hasattr(cls, k)})

@dataclass
class ClassificationRequest:
    """Request for classification operation"""
    
    # Content to classify
    content: str
    metadata: Dict[str, Any]
    
    # Request configuration
    request_id: str = field(default_factory=lambda: f"req_{int(time.time() * 1000)}")
    priority: ClassificationPriority = ClassificationPriority.NORMAL
    timeout_seconds: float = 30.0
    
    # Context information
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    source_system: Optional[str] = None
    
    # Processing options
    use_cache: bool = True
    force_reprocessing: bool = False
    include_debug_info: bool = False
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

class PerformanceTracker:
    """Tracks performance metrics for classification operations"""
    
    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.current_operations: Dict[str, float] = {}
        
        # Performance statistics
        self.total_operations = 0
        self.successful_operations = 0
        self.failed_operations = 0
        
        # Timing statistics
        self.response_times: deque = deque(maxlen=1000)
        self.strategy_performance: Dict[str, List[float]] = defaultdict(list)
        
        # Cache statistics
        self.cache_hits = 0
        self.cache_misses = 0
    
    def start_timing(self, operation_id: Optional[str] = None) -> str:
        """Start timing an operation"""
        if operation_id is None:
            operation_id = f"op_{int(time.time() * 1000000)}"
        
        self.current_operations[operation_id] = time.time()
        return operation_id
    
    def end_timing(self, operation_id: Optional[str] = None) -> float:
        """End timing an operation and return duration in milliseconds"""
        if operation_id and operation_id in self.current_operations:
            start_time = self.current_operations.pop(operation_id)
        elif self.current_operations:
            # Use the most recent operation if no ID specified
            operation_id = max(self.current_operations.keys())
            start_time = self.current_operations.pop(operation_id)
        else:
            return 0.0
        
        duration_ms = (time.time() - start_time) * 1000
        self.response_times.append(duration_ms)
        return duration_ms
    
    def record_classification(self, result: ClassificationResult):
        """Record a classification result for performance tracking"""
        
        self.total_operations += 1
        
        if result.status == ClassificationStatus.COMPLETED:
            self.successful_operations += 1
        else:
            self.failed_operations += 1
        
        # Record strategy performance
        strategy_key = result.strategy.value
        self.strategy_performance[strategy_key].append(result.processing_time_ms)
        
        # Keep only recent performance data
        if len(self.strategy_performance[strategy_key]) > 1000:
            self.strategy_performance[strategy_key] = self.strategy_performance[strategy_key][-1000:]
        
        # Record cache performance
        if result.cache_hit:
            self.cache_hits += 1
        else:
            self.cache_misses += 1
        
        # Store in history
        metric_record = {
            'timestamp': result.timestamp.isoformat(),
            'strategy': result.strategy.value,
            'confidence': result.confidence,
            'processing_time_ms': result.processing_time_ms,
            'cache_hit': result.cache_hit,
            'validation_passed': result.validation_passed,
            'fallback_used': result.fallback_used
        }
        
        self.metrics_history.append(metric_record)
    
    def get_average_processing_time(self, strategy: Optional[str] = None) -> float:
        """Get average processing time"""
        if strategy and strategy in self.strategy_performance:
            times = self.strategy_performance[strategy]
            return sum(times) / len(times) if times else 0.0
        elif self.response_times:
            return sum(self.response_times) / len(self.response_times)
        else:
            return 0.0
    
    def get_classification_accuracy(self) -> float:
        """Get classification accuracy (successful operations / total operations)"""
        if self.total_operations == 0:
            return 0.0
        return self.successful_operations / self.total_operations
    
    def get_cache_hit_rate(self) -> float:
        """Get cache hit rate"""
        total_cache_operations = self.cache_hits + self.cache_misses
        if total_cache_operations == 0:
            return 0.0
        return self.cache_hits / total_cache_operations
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        
        # Calculate percentiles for response times
        sorted_times = sorted(self.response_times) if self.response_times else []
        
        def percentile(data: List[float], p: float) -> float:
            if not data:
                return 0.0
            index = int(len(data) * p / 100)
            return data[min(index, len(data) - 1)]
        
        return {
            'total_operations': self.total_operations,
            'successful_operations': self.successful_operations,
            'failed_operations': self.failed_operations,
            'success_rate': self.get_classification_accuracy(),
            'average_response_time_ms': self.get_average_processing_time(),
            'response_time_p50_ms': percentile(sorted_times, 50),
            'response_time_p95_ms': percentile(sorted_times, 95),
            'response_time_p99_ms': percentile(sorted_times, 99),
            'cache_hit_rate': self.get_cache_hit_rate(),
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'strategy_performance': {
                strategy: {
                    'count': len(times),
                    'avg_time_ms': sum(times) / len(times) if times else 0.0,
                    'min_time_ms': min(times) if times else 0.0,
                    'max_time_ms': max(times) if times else 0.0
                }
                for strategy, times in self.strategy_performance.items()
            }
        }

class ClassificationEngine(ABC):
    """Abstract base class for classification engines"""
    
    def __init__(self, config: ClassificationConfig):
        self.config = config
        self.performance_tracker = PerformanceTracker()
        self.is_initialized = False
        
        # Error handling
        self.error_count = 0
        self.last_error: Optional[Exception] = None
        self.error_threshold = 10
        
        # Request queue for batch processing
        self.request_queue: asyncio.Queue = asyncio.Queue()
        self.batch_processor_task: Optional[asyncio.Task] = None
    
    async def initialize(self):
        """Initialize the classification engine"""
        if self.is_initialized:
            return
        
        await self._initialize_engine()
        self.is_initialized = True
        
        # Start batch processor if enabled
        if self.config.batch_size > 1:
            self.batch_processor_task = asyncio.create_task(self._batch_processor())
    
    @abstractmethod
    async def _initialize_engine(self):
        """Engine-specific initialization"""
        pass
    
    @abstractmethod
    async def classify_content(
        self, 
        content: str, 
        metadata: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ClassificationResult:
        """Classify content and return classification result"""
        pass
    
    async def classify_batch(
        self, 
        requests: List[ClassificationRequest]
    ) -> List[ClassificationResult]:
        """Classify multiple content items in batch"""
        
        results = []
        
        # Process requests concurrently with limit
        semaphore = asyncio.Semaphore(self.config.batch_size)
        
        async def process_request(request: ClassificationRequest) -> ClassificationResult:
            async with semaphore:
                try:
                    request.started_at = datetime.now()
                    
                    result = await asyncio.wait_for(
                        self.classify_content(request.content, request.metadata),
                        timeout=request.timeout_seconds
                    )
                    
                    request.completed_at = datetime.now()
                    return result
                    
                except asyncio.TimeoutError:
                    return self._create_timeout_result(request)
                except Exception as e:
                    return self._create_error_result(request, e)
        
        # Execute all requests
        tasks = [process_request(req) for req in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append(self._create_error_result(requests[i], result))
            else:
                final_results.append(result)
        
        return final_results
    
    async def _batch_processor(self):
        """Background batch processor for queued requests"""
        
        while True:
            try:
                # Collect batch of requests
                batch = []
                timeout = self.config.batch_timeout
                
                # Get first request (blocking)
                try:
                    first_request = await asyncio.wait_for(
                        self.request_queue.get(), 
                        timeout=timeout
                    )
                    batch.append(first_request)
                except asyncio.TimeoutError:
                    continue
                
                # Collect additional requests (non-blocking)
                while len(batch) < self.config.batch_size:
                    try:
                        request = await asyncio.wait_for(
                            self.request_queue.get(), 
                            timeout=0.1
                        )
                        batch.append(request)
                    except asyncio.TimeoutError:
                        break
                
                # Process batch
                if batch:
                    await self.classify_batch(batch)
                    
            except Exception as e:
                self.last_error = e
                self.error_count += 1
                
                if self.error_count > self.error_threshold:
                    print(f"Classification engine error threshold exceeded: {e}")
                    break
    
    def _create_timeout_result(self, request: ClassificationRequest) -> ClassificationResult:
        """Create result for timed-out request"""
        
        metrics = ClassificationMetrics()
        
        return ClassificationResult(
            strategy=ClassificationStrategy.ISOLATED_NAMESPACE,
            confidence=0.0,
            metrics=metrics,
            reasoning=f"Classification timed out after {request.timeout_seconds}s",
            processing_time_ms=request.timeout_seconds * 1000,
            status=ClassificationStatus.FAILED,
            validation_passed=False,
            validation_warnings=["Request timed out"],
            fallback_used=True
        )
    
    def _create_error_result(self, request: ClassificationRequest, error: Exception) -> ClassificationResult:
        """Create result for failed request"""
        
        metrics = ClassificationMetrics()
        
        return ClassificationResult(
            strategy=ClassificationStrategy.ISOLATED_NAMESPACE,
            confidence=0.0,
            metrics=metrics,
            reasoning=f"Classification failed: {str(error)}",
            processing_time_ms=0.0,
            status=ClassificationStatus.FAILED,
            validation_passed=False,
            validation_warnings=[f"Error: {str(error)}"],
            fallback_used=True
        )
    
    async def get_engine_status(self) -> Dict[str, Any]:
        """Get current engine status and statistics"""
        
        return {
            'is_initialized': self.is_initialized,
            'error_count': self.error_count,
            'last_error': str(self.last_error) if self.last_error else None,
            'queue_size': self.request_queue.qsize() if hasattr(self, 'request_queue') else 0,
            'performance_summary': self.performance_tracker.get_performance_summary(),
            'config_summary': {
                'backend_type': self.config.backend_type,
                'batch_size': self.config.batch_size,
                'batch_timeout': self.config.batch_timeout,
                'cache_enabled': self.config.cache.enabled
            }
        }
    
    async def shutdown(self):
        """Shutdown the classification engine"""
        
        if self.batch_processor_task:
            self.batch_processor_task.cancel()
            try:
                await self.batch_processor_task
            except asyncio.CancelledError:
                pass
        
        await self._shutdown_engine()
        self.is_initialized = False
    
    async def _shutdown_engine(self):
        """Engine-specific shutdown"""
        pass

class ClassificationEngineFactory:
    """Factory for creating classification engines"""
    
    _engines: Dict[str, type] = {}
    
    @classmethod
    def register_engine(cls, name: str, engine_class: type):
        """Register a classification engine"""
        cls._engines[name] = engine_class
    
    @classmethod
    def create_engine(cls, name: str, config: ClassificationConfig) -> ClassificationEngine:
        """Create a classification engine by name"""
        
        if name not in cls._engines:
            raise ValueError(f"Unknown classification engine: {name}")
        
        engine_class = cls._engines[name]
        return engine_class(config)
    
    @classmethod
    def list_engines(cls) -> List[str]:
        """List available classification engines"""
        return list(cls._engines.keys())

# Exception classes for classification errors
class ClassificationError(Exception):
    """Base exception for classification errors"""
    pass

class ClassificationTimeoutError(ClassificationError):
    """Exception for classification timeouts"""
    pass

class ClassificationValidationError(ClassificationError):
    """Exception for classification validation errors"""
    pass

class ClassificationConfigurationError(ClassificationError):
    """Exception for classification configuration errors"""
    pass
```

**Validation:**
```python
# Test base classification framework
from python.helpers.classification_engine import (
    ClassificationEngine, ClassificationResult, ClassificationMetrics,
    ClassificationRequest, PerformanceTracker, ClassificationEngineFactory
)
from python.helpers.classification_config import ClassificationConfig, ClassificationStrategy

# Test performance tracker
tracker = PerformanceTracker()
op_id = tracker.start_timing()
# Simulate some work
import time; time.sleep(0.01)
duration = tracker.end_timing(op_id)
print(f"Operation took {duration:.2f}ms")

# Test metrics and results
metrics = ClassificationMetrics(
    semantic_overlap=0.8,
    entity_confidence=0.9,
    domain_specificity=0.6
)

result = ClassificationResult(
    strategy=ClassificationStrategy.SHARED_ONTOLOGY,
    confidence=0.85,
    metrics=metrics,
    reasoning="Test classification",
    processing_time_ms=45.0
)

print(f"Classification result: {result.strategy.value} with confidence {result.confidence}")

# Test serialization
result_dict = result.to_dict()
restored_result = ClassificationResult.from_dict(result_dict)
print(f"Serialization test passed: {restored_result.strategy == result.strategy}")
```

---

**Next Step**: [Memory Abstraction](memory-abstraction.md)
