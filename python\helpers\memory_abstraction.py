"""
Enhanced Memory Abstraction Layer with Intelligent Classification and Bulk Processing Support
Extends the existing memory abstraction layer with intelligent classification
and bulk episode processing capabilities.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, TYPE_CHECKING, Tuple, Type
import os
import asyncio # Added for EnhancedMemoryAbstractionLayer
from datetime import datetime # Added for EnhancedMemoryAbstractionLayer
from enum import Enum

from python.helpers import settings as global_settings

# Attempt to import actual or placeholder components for EnhancedMemoryAbstractionLayer
from .classification_config import classification_config, ClassificationStrategy
# Use ActualClassificationStrategy to avoid name collision if this file defines a placeholder
# ClassificationStrategy = ActualClassificationStrategy # Not needed anymore with direct import

from .classification_engine import ClassificationEngine, ClassificationResult, ClassificationMetrics, ClassificationStatus, generate_id
# For EnhancedMemoryAbstractionLayer, it refers to IntelligentClassificationEngine.
from .intelligent_classification_engine import IntelligentClassificationEngine
# IntelligentClassificationEngine = ActualIntelligentClassificationEngine # Not needed
# ClassificationResult = ActualClassificationResult # Not needed
# ClassificationStatus = ActualClassificationStatus # Not needed
# generate_id = actual_generate_id # Use actual generate_id # Not needed

# Attempt to import the actual HierarchicalMemoryManager
from .hierarchical_memory_manager import HierarchicalMemoryManager

# Placeholders for other components used by EnhancedMemoryAbstractionLayer
# More robust placeholders for UserPreferenceManager and related enums
from .user_preferences import UserPreferenceManager, LearningSignal, PreferenceType, PreferenceItem
# UserPreferenceManager = ActualUserPreferenceManager # Not needed
# LearningSignal = ActualLearningSignal # Not needed
# PreferenceType = ActualPreferenceType # Not needed
# PreferenceItem = ActualPreferenceItem # Make sure PreferenceItem is available # Not needed

class DataSeparationFramework: # Renamed from DataSeparationLayer to match usage
    async def separate_mixed_data(self, content: str, metadata: Dict[str, Any]) -> Any:
        # Return a mock object that has the expected attributes
        class MockSeparationResult:
            def __init__(self):
                self.history_items = []
                self.knowledge_items = []
                self.mixed_items = []
        return MockSeparationResult()

class BoundaryEnforcementSystem:
    async def check_data_boundaries(self, items: List[Any]) -> Any:
        class MockBoundaryCheckResult:
            def __init__(self):
                self.is_valid = True
                self.violations = []
        return MockBoundaryCheckResult()

class PerformanceMonitor: # Renamed from PerformanceUtils to match usage
    async def start_monitoring(self): print("Placeholder PM started")
    def get_performance_summary(self) -> Dict[str,Any]: return {'monitoring_active':False, 'metrics_tracked':0, 'active_alerts':0, 'system_health':{}}
    async def stop_monitoring(self): print("Placeholder PM stopped")

performance_monitor = PerformanceMonitor() # Global instance

class PerformanceTimer: # This was correctly named
    def __init__(self, name: str): self.name = name
    async def __aenter__(self): pass
    async def __aexit__(self, exc_type, exc, tb): pass

# LearningSignal and PreferenceType placeholders are now defined above with other UPM components

@dataclass
class MemoryItem: # Placeholder Dataclass
    id: str
    content: str
    metadata: Dict[str, Any]
    importance_score: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class MemoryQuery: # Placeholder Dataclass
    query_text: str
    limit: int = 10
    importance_threshold: float = 0.5
    time_range: Optional[Any] = None
    tier_preference: Optional[str] = None


if TYPE_CHECKING:
    from agent import Agent

@dataclass
class MemoryDocument:
    """Document returned from memory operations"""
    id: str
    page_content: str
    metadata: Dict[str, Any]
    score: Optional[float] = None

@dataclass
class MemoryConfig:
    """Memory backend configuration with bulk processing support"""
    backend_type: str  # "faiss" or "graphiti"
    memory_subdir: str
    embeddings_model: Any
    graphiti_config: Optional[Dict[str, str]] = None
    # Bulk processing configuration
    bulk_processing_enabled: bool = True
    bulk_batch_size: int = 100
    bulk_timeout: int = 300


# Definition of EnhancedMemoryAbstractionLayer (from markdown)
class EnhancedMemoryAbstractionLayer:
    """
    Enhanced memory abstraction layer with intelligent classification
    """

    def __init__(self, user_id: str = "default_user"):
        # Core components
        # Assuming classification_config is available globally from classification_config.py
        self.classification_engine = IntelligentClassificationEngine(classification_config)
        self.memory_manager = HierarchicalMemoryManager()
        self.user_preferences = UserPreferenceManager(user_id)
        self.data_separation = DataSeparationFramework()
        self.boundary_enforcement = BoundaryEnforcementSystem()

        # Initialization state
        self.is_initialized = False
        self.initialization_lock = asyncio.Lock() # Ensure asyncio is imported

    async def initialize(self):
        """Initialize all components"""
        async with self.initialization_lock:
            if self.is_initialized:
                return

            await self.classification_engine.initialize()
            await self.memory_manager.initialize()
            await performance_monitor.start_monitoring() # Uses global performance_monitor

            await self._load_user_preferences()

            self.is_initialized = True
            print("Enhanced Memory Abstraction Layer initialized")

    async def _ensure_initialized(self):
        """Ensure system is initialized before operations"""
        if not self.is_initialized:
            await self.initialize()

    async def classify_and_route_content(
        self,
        content: str,
        content_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, ClassificationResult]:
        """
        Classify content and route to appropriate storage system
        Returns: Tuple of (item_id, classification_result)
        """
        await self._ensure_initialized()

        async with PerformanceTimer('classify_and_route_total'): # PerformanceTimer placeholder
            full_metadata = {
                'content_type': content_type,
                'timestamp': datetime.now().isoformat(), # datetime imported
                'user_id': self.user_preferences.user_id,
                **(metadata or {})
            }

            async with PerformanceTimer('classification_decision'):
                classification_result = await self.classification_engine.classify_content(
                    content, full_metadata
                )

            async with PerformanceTimer('data_separation'):
                separation_result = await self.data_separation.separate_mixed_data(
                    content, full_metadata
                )

            async with PerformanceTimer('boundary_validation'):
                all_items = (separation_result.history_items +
                           separation_result.knowledge_items +
                           separation_result.mixed_items)

                boundary_check = await self.boundary_enforcement.check_data_boundaries(all_items)

                if not boundary_check.is_valid:
                    print(f"Boundary violations detected: {len(boundary_check.violations)}")
                    classification_result.strategy = ClassificationStrategy.ISOLATED_NAMESPACE
                    # classification_result.validation_warnings.extend([
                    # v.description for v in boundary_check.violations
                    # ])

            async with PerformanceTimer('memory_storage'):
                item_id = await self.memory_manager.store_memory(
                    content, full_metadata, classification_result
                )

            await self._record_user_interaction(classification_result, full_metadata)

            return item_id, classification_result

    async def store_with_user_preferences(
       self,
       content: str,
       content_type: str,
       metadata: Optional[Dict[str, Any]] = None,
       user_id: str = "default_user"
    ) -> Tuple[str, ClassificationResult]:
       """
       Classify content using user-specific preferences and route to appropriate storage.
       Returns: Tuple of (item_id, classification_result)
       """
       await self._ensure_initialized()

       current_op_user_id = user_id if user_id != "default_user" else self.user_preferences.user_id

       async with PerformanceTimer(f'store_with_preferences_total_for_{current_op_user_id}'):
           full_metadata = {
               'content_type': content_type,
               'timestamp': datetime.now().isoformat(),
               'user_id': current_op_user_id,
               **(metadata or {})
           }

           classification_result: ClassificationResult # Ensure type hinting
           async with PerformanceTimer(f'classification_decision_with_prefs_for_{current_op_user_id}'):
               if not hasattr(self.classification_engine, 'classify_content_with_preferences'):
                   print(f"WARN: Classification engine {type(self.classification_engine).__name__} does not support preference-aware classification. Falling back to standard classification.")
                   if hasattr(self.classification_engine, 'classify_content'):
                        classification_result = await self.classification_engine.classify_content(
                            content=content,
                            metadata=full_metadata
                        )
                   else:
                       raise NotImplementedError(f"Current classification engine does not have 'classify_content_with_preferences' or 'classify_content'")
               else:
                   classification_result = await self.classification_engine.classify_content_with_preferences(
                       user_id=current_op_user_id,
                       content=content,
                       metadata=full_metadata
                   )

           separation_result = await self.data_separation.separate_mixed_data(
                content, full_metadata
           )

           all_items_for_boundary = []
           if hasattr(separation_result, 'history_items'): all_items_for_boundary.extend(separation_result.history_items)
           if hasattr(separation_result, 'knowledge_items'): all_items_for_boundary.extend(separation_result.knowledge_items)
           if hasattr(separation_result, 'mixed_items'): all_items_for_boundary.extend(separation_result.mixed_items)

           boundary_check = await self.boundary_enforcement.check_data_boundaries(
                [str(item) for item in all_items_for_boundary]
           )

           if not boundary_check.is_valid:
                print(f"Boundary violations detected for user {current_op_user_id}: {len(boundary_check.violations)}")
                # Ensure strategy assignment is compatible with actual/placeholder ClassificationStrategy
                if hasattr(ClassificationStrategy.ISOLATED_NAMESPACE, 'value'): # Actual Enum
                    classification_result.strategy = ClassificationStrategy.ISOLATED_NAMESPACE
                else: # Placeholder (string-based)
                    classification_result.strategy = str(ClassificationStrategy.ISOLATED_NAMESPACE)


           item_id: str
           strategy_value = classification_result.strategy.value if hasattr(classification_result.strategy, 'value') else str(classification_result.strategy)
           shared_ontology_val = ClassificationStrategy.SHARED_ONTOLOGY.value if hasattr(ClassificationStrategy.SHARED_ONTOLOGY, 'value') else str(ClassificationStrategy.SHARED_ONTOLOGY)

           if strategy_value == shared_ontology_val:
               item_id = await self._store_in_shared_ontology_with_preferences(
                   content, full_metadata, classification_result, current_op_user_id
               )
           else:
                async with PerformanceTimer(f'default_memory_storage_for_{current_op_user_id}'):
                   item_id = await self.memory_manager.store_memory(
                       content, full_metadata, classification_result
                   )

           await self._record_user_interaction(classification_result, full_metadata)

           await self._create_feedback_opportunity(current_op_user_id, item_id, classification_result)

           return item_id, classification_result

    async def _store_in_shared_ontology_with_preferences(
       self,
       content: str,
       metadata: Dict[str, Any],
       classification_result: ClassificationResult,
       user_id: str
    ) -> str:
       """
       Stores content in a shared ontology, potentially using preference-adjusted classification.
       """
       strategy_val = classification_result.strategy.value if hasattr(classification_result.strategy, 'value') else str(classification_result.strategy)
       print(f"INFO ({user_id}): Storing content in shared ontology. Strategy: {strategy_val}, Item: {metadata.get('content_type')}")

       item_id = await self.memory_manager.store_memory(
           content, metadata, classification_result
       )
       return item_id

    async def _create_feedback_opportunity(
       self,
       user_id: str,
       item_id: str,
       classification_details: ClassificationResult
    ) -> None:
       """
       Logs or prepares data for potential explicit user feedback on a classification event.
       """
       strategy_val = classification_details.strategy.value if hasattr(classification_details.strategy, 'value') else str(classification_details.strategy)
       print(f"INFO ({user_id}): Feedback opportunity created for item {item_id}.")
       print(f"  Classification Strategy: {strategy_val}")
       print(f"  Confidence: {classification_details.confidence:.2f}")
       if classification_details.reasoning:
           print(f"  Reasoning: {classification_details.reasoning[:100]}...")
       await asyncio.sleep(0.001)

    async def collect_user_feedback(
       self,
       user_id: str,
       item_id: str,
       feedback_type: str,
       feedback_value: Any,
       signal: LearningSignal, # Expecting LearningSignal Enum member or compatible string
       context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
       """
       Collects explicit user feedback and records it via UserPreferenceManager.
       """
       await self._ensure_initialized()

       target_upm = self.user_preferences
       if user_id != self.user_preferences.user_id:
           print(f"WARN: Feedback collected for user '{user_id}' which differs from EMAL instance user '{self.user_preferences.user_id}'. This may require fetching a different UPM if EMAL is multi-user.")
           # For this version, we'll assume self.user_preferences is the target or EMAL is user-specific.
           # A multi-user EMAL might have a self.get_user_preference_manager(user_id) method.
           # If EMAL is strictly single-user, this could be an error or ignored.

       action_key = f"explicit_feedback_{feedback_type}"
       full_context = {
           "item_id": item_id,
           "original_feedback_type": feedback_type,
           **(context or {})
       }

       try:
           current_signal_enum = signal
           if isinstance(signal, str): # Convert string to Enum if UPM expects Enum
                try:
                    current_signal_enum = LearningSignal[signal.upper()] # Assumes signal string matches Enum key
                except KeyError: # Fallback: try matching by value
                    found_signal = False
                    for ls_member in LearningSignal:
                        if ls_member.value == signal:
                            current_signal_enum = ls_member
                            found_signal = True
                            break
                    if not found_signal:
                        return {'success': False, 'error': f"Invalid LearningSignal string: {signal}"}

           # Ensure target_upm.record_feedback exists and is callable
           if not hasattr(target_upm, 'record_feedback') or not callable(target_upm.record_feedback):
               return {'success': False, 'error': f"UserPreferenceManager for user {target_upm.user_id} does not have a callable 'record_feedback' method."}

           await target_upm.record_feedback(
               action=action_key,
               context=full_context,
               signal_type=current_signal_enum,
               value=feedback_value
           )

           return {'success': True, 'message': f"Feedback recorded for item {item_id} for user {user_id}."}
       except Exception as e:
           print(f"ERROR ({user_id}): Failed to collect feedback for item {item_id}: {e}")
           return {'success': False, 'error': str(e)}

    async def store_with_user_preferences(
       self,
       content: str,
       content_type: str,
       metadata: Optional[Dict[str, Any]] = None,
       user_id: str = "default_user"
    ) -> Tuple[str, ClassificationResult]:
       """
       Classify content using user-specific preferences and route to appropriate storage.
       Returns: Tuple of (item_id, classification_result)
       """
       await self._ensure_initialized()

       current_op_user_id = user_id if user_id != "default_user" else self.user_preferences.user_id

       async with PerformanceTimer(f'store_with_preferences_total_for_{current_op_user_id}'):
           full_metadata = {
               'content_type': content_type,
               'timestamp': datetime.now().isoformat(),
               'user_id': current_op_user_id,
               **(metadata or {})
           }

           async with PerformanceTimer(f'classification_decision_with_prefs_for_{current_op_user_id}'):
               if not hasattr(self.classification_engine, 'classify_content_with_preferences'):
                   print(f"WARN: Classification engine {type(self.classification_engine).__name__} does not support preference-aware classification. Falling back to standard classification.")
                   if hasattr(self.classification_engine, 'classify_content'):
                        classification_result = await self.classification_engine.classify_content(
                            content=content,
                            metadata=full_metadata
                        )
                   else: # Should not happen if using IntelligentClassificationEngine or its placeholder
                       raise NotImplementedError(f"Current classification engine does not have 'classify_content_with_preferences' or 'classify_content'")
               else:
                   classification_result = await self.classification_engine.classify_content_with_preferences(
                       user_id=current_op_user_id,
                       content=content,
                       metadata=full_metadata
                   )

           # Assuming DataSeparationFramework and BoundaryEnforcementSystem are defined or placeholder-ed
           # These might need to be initialized if they have their own __init__ methods
           # For now, assuming they are stateless or initialized with EMAL
           separation_result = await self.data_separation.separate_mixed_data(
                content, full_metadata
           )

           all_items_for_boundary = []
           if hasattr(separation_result, 'history_items'): all_items_for_boundary.extend(separation_result.history_items)
           if hasattr(separation_result, 'knowledge_items'): all_items_for_boundary.extend(separation_result.knowledge_items)
           if hasattr(separation_result, 'mixed_items'): all_items_for_boundary.extend(separation_result.mixed_items)

           boundary_check = await self.boundary_enforcement.check_data_boundaries(
                [str(item) for item in all_items_for_boundary]
           )

           if not boundary_check.is_valid:
                print(f"Boundary violations detected for user {current_op_user_id}: {len(boundary_check.violations)}")
                current_strategy_val = ClassificationStrategy.ISOLATED_NAMESPACE.value \
                    if hasattr(ClassificationStrategy.ISOLATED_NAMESPACE, 'value') else str(ClassificationStrategy.ISOLATED_NAMESPACE)

                if hasattr(classification_result.strategy, 'value'): # Enum member
                    classification_result.strategy = ClassificationStrategy.ISOLATED_NAMESPACE
                else: # String (from placeholder)
                    classification_result.strategy = current_strategy_val


           item_id: str
           strategy_value = classification_result.strategy.value if hasattr(classification_result.strategy, 'value') else str(classification_result.strategy)
           shared_ontology_val = ClassificationStrategy.SHARED_ONTOLOGY.value if hasattr(ClassificationStrategy.SHARED_ONTOLOGY, 'value') else str(ClassificationStrategy.SHARED_ONTOLOGY)

           if strategy_value == shared_ontology_val:
               item_id = await self._store_in_shared_ontology_with_preferences(
                   content, full_metadata, classification_result, current_op_user_id
               )
           else:
                async with PerformanceTimer(f'default_memory_storage_for_{current_op_user_id}'):
                   item_id = await self.memory_manager.store_memory(
                       content, full_metadata, classification_result
                   )

           # Ensure self.user_preferences is aligned if EMAL is multi-user, for now assume it's correct for current_op_user_id
           await self._record_user_interaction(classification_result, full_metadata)

           await self._create_feedback_opportunity(current_op_user_id, item_id, classification_result)

           return item_id, classification_result

    async def _store_in_shared_ontology_with_preferences(
       self,
       content: str,
       metadata: Dict[str, Any],
       classification_result: ClassificationResult,
       user_id: str
    ) -> str:
       """
       Stores content in a shared ontology, potentially using preference-adjusted classification.
       """
       strategy_val = classification_result.strategy.value if hasattr(classification_result.strategy, 'value') else str(classification_result.strategy)
       print(f"INFO ({user_id}): Storing content in shared ontology. Strategy: {strategy_val}, Item: {metadata.get('content_type')}")

       item_id = await self.memory_manager.store_memory(
           content, metadata, classification_result
       )
       return item_id

    async def _create_feedback_opportunity(
       self,
       user_id: str,
       item_id: str,
       classification_details: ClassificationResult
    ) -> None:
       """
       Logs or prepares data for potential explicit user feedback on a classification event.
       """
       strategy_val = classification_details.strategy.value if hasattr(classification_details.strategy, 'value') else str(classification_details.strategy)
       print(f"INFO ({user_id}): Feedback opportunity created for item {item_id}.")
       print(f"  Classification Strategy: {strategy_val}")
       print(f"  Confidence: {classification_details.confidence:.2f}")
       if classification_details.reasoning:
           print(f"  Reasoning: {classification_details.reasoning[:100]}...")
       await asyncio.sleep(0.001)

    async def collect_user_feedback(
       self,
       user_id: str,
       item_id: str,
       feedback_type: str,
       feedback_value: Any,
       signal: LearningSignal,
       context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
       """
       Collects explicit user feedback and records it via UserPreferenceManager.
       """
       await self._ensure_initialized()

       target_upm = self.user_preferences
       if user_id != self.user_preferences.user_id:
           # This logic might need refinement if EMAL instance is multi-user and manages a dict of UPMs
           print(f"WARN: Feedback collected for user '{user_id}' which differs from EMAL instance user '{self.user_preferences.user_id}'. Ensure UPM is correctly targeted if EMAL is multi-user.")
           # For now, we'll assume self.user_preferences is the one to use, or this implies EMAL should fetch the right one.
           # If UserPreferenceManager is globally managed or accessible via a factory based on user_id, that would be better.
           # For this structure, assuming EMAL's self.user_preferences should be for 'user_id' or it's an error.
           # For simplicity in this step, we'll proceed with self.user_preferences but acknowledge this design point.
           # A better approach for multi-user EMAL would be:
           # target_upm = await self._get_user_preference_manager(user_id) # If such a method exists on EMAL
           # For now, this test will assume user_id matches or self.user_preferences is correctly scoped.
           if not hasattr(self, '_get_user_preference_manager'): # A check if we are missing this method
                print("ERROR: EMAL is not equipped to handle feedback for arbitrary user_ids without a _get_user_preference_manager method.")
                return {'success': False, 'error': f"User ID mismatch and no mechanism to get UPM for {user_id}"}
           # If _get_user_preference_manager is implemented on EMAL (it's not in current plan for EMAL directly)
           # target_upm = await self._get_user_preference_manager(user_id)


       action_key = f"explicit_feedback_{feedback_type}"
       full_context = {
           "item_id": item_id,
           "original_feedback_type": feedback_type,
           **(context or {})
       }

       try:
           # Ensure signal is the enum member, not string, if UPM expects enum.
           # Placeholder UPM might be fine with string, actual UPM might not.
           current_signal_enum = signal
           if isinstance(signal, str):
                try:
                    current_signal_enum = LearningSignal[signal.upper()]
                except KeyError: # Try by value
                    found_signal = False
                    for ls_enum in LearningSignal:
                        if ls_enum.value == signal:
                            current_signal_enum = ls_enum
                            found_signal = True
                            break
                    if not found_signal:
                        return {'success': False, 'error': f"Invalid LearningSignal string: {signal}"}


           await target_upm.record_feedback(
               action=action_key,
               context=full_context,
               signal_type=current_signal_enum,
               value=feedback_value
           )

           return {'success': True, 'message': f"Feedback recorded for item {item_id} for user {user_id}."}
       except Exception as e:
           print(f"ERROR ({user_id}): Failed to collect feedback for item {item_id}: {e}")
           return {'success': False, 'error': str(e)}

    async def search_memories(
        self,
        query: str,
        search_strategy: str = "hierarchical",
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 10
    ) -> List[MemoryItem]:
        """ Search across all memory tiers with intelligent routing """
        await self._ensure_initialized()

        async with PerformanceTimer('memory_search_total'):
            memory_query = MemoryQuery(
                query_text=query,
                limit=limit,
                importance_threshold=await self.user_preferences.get_preference(
                    'memory_importance_threshold', 0.5
                )
            )

            if filters:
                memory_query.time_range = filters.get('time_range')
                memory_query.tier_preference = filters.get('tier_preference')

            results = await self.memory_manager.retrieve_memories(memory_query, search_strategy)

            await self.user_preferences.record_user_feedback(
                'search_result', # action string
                { # context dict
                    'query': query,
                    'strategy': search_strategy,
                    'results_count': len(results),
                    'filters': filters
                },
                LearningSignal.IMPLICIT_USAGE, # signal_type enum/str
                value=None # Optional value for feedback
            )
            return results

    async def get_system_status(self) -> Dict[str, Any]:
        await self._ensure_initialized()
        engine_status = await self.classification_engine.get_engine_status()
        memory_stats = await self.memory_manager.get_memory_hierarchy_stats()
        preference_stats = await self.user_preferences.get_preference_stats()
        performance_summary = performance_monitor.get_performance_summary()

        return {
            'system_initialized': self.is_initialized,
            'classification_engine': {
                'initialized': engine_status['is_initialized'],
                'error_count': engine_status['error_count'],
                'performance': engine_status['performance_summary']
            },
            'memory_hierarchy': {
                'total_items': memory_stats['hierarchy_overview']['total_items'],
                'tier_distribution': memory_stats['tier_distribution'],
                'performance_targets_met': memory_stats['performance_summary']['targets_met']
            },
            'user_preferences': {
                'total_preferences': preference_stats['total_preferences'],
                'adaptation_enabled': preference_stats['adaptation_enabled'],
                'recent_feedback': preference_stats['recent_feedback_count']
            },
            'performance_monitoring': {
                'active': performance_summary['monitoring_active'],
                'metrics_tracked': performance_summary['metrics_tracked'],
                'active_alerts': performance_summary['active_alerts']
            }
        }

    async def _load_user_preferences(self):
        domain_weights = await self.user_preferences.get_domain_weights()
        thresholds = await self.user_preferences.get_classification_thresholds()
        classification_config.update_from_user_preferences({
            'domain_weights': domain_weights,
            'classification_thresholds': thresholds
        })

    async def _record_user_interaction(self, classification_result: ClassificationResult, metadata: Dict[str, Any]):
        if classification_result.confidence > 0.8:
            feedback_type = LearningSignal.POSITIVE_FEEDBACK
        elif classification_result.confidence < 0.5:
            feedback_type = LearningSignal.NEGATIVE_FEEDBACK
        else:
            feedback_type = LearningSignal.IMPLICIT_USAGE

        await self.user_preferences.record_user_feedback(
            'classification_result',
            {
                'strategy': classification_result.strategy.value if isinstance(classification_result.strategy, Enum) else str(classification_result.strategy),
                'confidence': classification_result.confidence,
                'processing_time_ms': classification_result.processing_time_ms,
                'content_type': metadata.get('content_type')
            },
            feedback_type
        )

    async def store(self, content: str, metadata: Dict[str, Any] = None) -> str:
        content_type = metadata.get('type', 'unknown') if metadata else 'unknown'
        item_id, _ = await self.classify_and_route_content(content, content_type, metadata)
        return item_id

    async def search(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        results = await self.search_memories(query, limit=limit)
        legacy_results = []
        for item in results:
            legacy_results.append({
                'id': item.id,
                'content': item.content,
                'metadata': item.metadata,
                'score': item.importance_score,
                'created_at': item.created_at.isoformat()
            })
        return legacy_results

    async def update_user_preference(self, key: str, value: Any) -> bool:
        preference_type_map = {
            'domain_weight': PreferenceType.DOMAIN_WEIGHT,
            'privacy_sensitivity': PreferenceType.PRIVACY_SENSITIVITY,
            'classification_threshold': PreferenceType.CLASSIFICATION_THRESHOLD,
            'memory_retention': PreferenceType.MEMORY_RETENTION,
            'search_preference': PreferenceType.SEARCH_PREFERENCE
        }
        pref_type = PreferenceType.SEARCH_PREFERENCE
        for key_part, mapped_type in preference_type_map.items():
            if key_part in key:
                pref_type = mapped_type
                break
        success = await self.user_preferences.set_preference(key, value, pref_type)
        if success:
            await self._load_user_preferences()
        return success

    async def get_performance_metrics(self) -> Dict[str, Any]:
        engine_status = await self.classification_engine.get_engine_status()
        classification_perf = engine_status['performance_summary']
        memory_stats = await self.memory_manager.get_memory_hierarchy_stats()
        memory_perf = memory_stats['performance_summary']
        system_perf = performance_monitor.get_performance_summary()
        return {
            'classification': {
                'average_response_time_ms': classification_perf.get('average_response_time_ms',0),
                'success_rate': classification_perf.get('success_rate',0),
                'cache_hit_rate': classification_perf.get('cache_hit_rate',0)
            },
            'memory': {
                'working_memory_avg_ms': memory_perf.get('working_memory_avg_response_ms',0),
                'long_term_memory_avg_ms': memory_perf.get('long_term_memory_avg_response_ms',0),
                'episodic_memory_avg_ms': memory_perf.get('episodic_memory_avg_response_ms',0),
                'targets_met': memory_perf.get('targets_met',False)
            },
            'system': {
                'cpu_usage_percent': system_perf.get('system_health',{}).get('cpu_percent', 0),
                'memory_usage_percent': system_perf.get('system_health',{}).get('memory_percent', 0),
                'active_alerts': system_perf.get('active_alerts',0)
            }
        }

    async def export_user_data(self) -> Dict[str, Any]:
        preferences_data = await self.user_preferences.export_preferences()
        memory_stats = await self.memory_manager.get_memory_hierarchy_stats()
        return {
            'user_id': self.user_preferences.user_id,
            'export_timestamp': datetime.now().isoformat(),
            'preferences': preferences_data,
            'memory_summary': {
                'total_items': memory_stats['hierarchy_overview']['total_items'],
                'tier_distribution': memory_stats['tier_distribution']
            },
            'system_version': classification_config.config_version
        }

    async def import_user_data(self, import_data: Dict[str, Any]) -> bool:
        try:
            if 'preferences' in import_data:
                success = await self.user_preferences.import_preferences(import_data['preferences'])
                if not success: return False
                await self._load_user_preferences()
            return True
        except Exception as e:
            print(f"Error importing user data: {e}")
            return False

    async def shutdown(self):
        if self.is_initialized:
            await self.classification_engine.shutdown()
            await self.memory_manager.shutdown()
            await performance_monitor.stop_monitoring()
            self.is_initialized = False
            print("Enhanced Memory Abstraction Layer shutdown complete")

# EnhancedMemoryBackend is the ABC for Faiss/Graphiti style backends
class EnhancedMemoryBackend(ABC):
    """Enhanced abstract base class for memory backends with bulk processing"""

    @abstractmethod
    async def initialize(self, config: MemoryConfig) -> None:
        """Initialize the memory backend"""
        pass

    @abstractmethod
    async def insert_text(self, text: str, metadata: Dict[str, Any]) -> str:
        """Insert text and return document ID"""
        pass

    @abstractmethod
    async def search_similarity_threshold(
        self,
        query: str,
        limit: int = 10,
        threshold: float = 0.7,
        filter: Optional[Dict[str, Any]] = None
    ) -> List[MemoryDocument]:
        """Search for similar documents"""
        pass

    @abstractmethod
    async def delete_documents_by_ids(self, ids: List[str]) -> List[MemoryDocument]:
        """Delete documents by IDs and return deleted documents"""
        pass

    @abstractmethod
    async def get_documents_by_ids(self, ids: List[str]) -> List[MemoryDocument]:
        """Get documents by their IDs"""
        pass

    @abstractmethod
    async def insert_knowledge_document(self, content: str, metadata: Dict[str, Any]) -> str:
        """Insert knowledge document with entity extraction"""
        pass

    # New bulk processing methods
    @abstractmethod
    async def add_episode_bulk(self, episodes: List[Dict[str, Any]]) -> List[str]:
        """Add multiple episodes efficiently"""
        pass

    @abstractmethod
    async def insert_knowledge_documents_bulk(self, documents: List[Dict[str, Any]]) -> List[str]:
        """Insert multiple knowledge documents with entity extraction"""
        pass

    def supports_bulk_processing(self) -> bool:
        """Check if backend supports bulk processing"""
        return hasattr(self, 'add_episode_bulk') and callable(getattr(self, 'add_episode_bulk'))

# This class is the original one, now renamed.
# It handles Faiss/Graphiti and the new "enhanced" backend from markdown.
class MemoryAbstractionLayer:
    """
    Main Memory Abstraction Layer.
    It can use one of the original backends (Faiss, Graphiti) or the
    new EnhancedMemoryAbstractionLayer (intelligent classification) based on configuration.
    """

    def __init__(self, agent: 'Agent'):
        self.agent = agent
        # For original Faiss/Graphiti backends:
        self.original_backend: Optional[EnhancedMemoryBackend] = None # Changed name
        self.original_config: Optional[MemoryConfig] = None # Changed name

        # For the new "enhanced" backend (intelligent classification):
        self.markdown_enhanced_layer: Optional[EnhancedMemoryAbstractionLayer] = None
        self.using_markdown_enhanced_layer = False # Flag to indicate which path is active

    async def initialize(self, memory_backend_override: Optional[str] = None) -> None:
        """Initialize the appropriate backend based on configuration or override."""

        backend_type_to_use = memory_backend_override if memory_backend_override else self._get_backend_type()

        if backend_type_to_use == "enhanced":
            self.using_markdown_enhanced_layer = True
            user_id_for_enhanced = getattr(self.agent, 'user_id', getattr(self.agent.config, 'user_id', 'default_user'))
            self.markdown_enhanced_layer = EnhancedMemoryAbstractionLayer(user_id=user_id_for_enhanced)
            await self.markdown_enhanced_layer.initialize()
            print("INFO: MemoryAbstractionLayer is using the 'enhanced' (intelligent classification) backend.")
            return

        # Original initialization logic for "faiss" or "graphiti"
        self.using_markdown_enhanced_layer = False
        if backend_type_to_use == "graphiti":
            from .enhanced_graphiti_backend import EnhancedGraphitiBackend # Assuming this path exists
            self.original_backend = EnhancedGraphitiBackend()
        else:  # Default to faiss
            from .enhanced_faiss_backend import EnhancedFaissBackend # Assuming this path exists
            self.original_backend = EnhancedFaissBackend()

        self.original_config = self._build_config_for_original_backend(backend_type_to_use) # Renamed helper
        if self.original_backend:
            await self.original_backend.initialize(self.original_config)
        else:
             print(f"CRITICAL: Could not initialize original backend for type '{backend_type_to_use}'")


    def _get_backend_type(self) -> str:
        """Determine which backend to use based on configuration."""
        backend_type_env = os.getenv("MEMORY_BACKEND", "").lower()
        if backend_type_env in ["graphiti", "faiss", "enhanced"]: # Added "enhanced"
            return backend_type_env

        if hasattr(self.agent, 'config') and hasattr(self.agent.config, 'memory_backend'):
            agent_backend_type = str(self.agent.config.memory_backend).lower()
            if agent_backend_type in ["graphiti", "faiss", "enhanced"]: # Added "enhanced"
                return agent_backend_type

        return "faiss" # Default

    def _build_config_for_original_backend(self, backend_type: str) -> MemoryConfig: # Renamed helper
        """Build configuration for the selected original backend (Faiss/Graphiti)"""
        # Extract basic configuration from agent
        memory_subdir = getattr(self.agent.config, 'memory_subdir', 'default')
        # Ensure embeddings_model is correctly accessed, it might be an object or a name string
        embeddings_model_config = getattr(self.agent.config, 'embeddings_model', None)
        # If embeddings_model is an object with a 'name' attribute (like an enum or specific class)
        # you might need embeddings_model_config.name or similar if the backend expects a string.
        # For now, passing it as is.

        # Bulk processing configuration from environment or defaults
        bulk_enabled = os.getenv("MEMORY_BULK_PROCESSING_ENABLED", "true").lower() == "true"
        # Ensure default values for batch size and timeout are integers
        bulk_batch_size = int(os.getenv("GRAPHITI_BULK_BATCH_SIZE", "100"))
        bulk_timeout = int(os.getenv("GRAPHITI_BULK_TIMEOUT", "300"))

        config = MemoryConfig(
            backend_type=backend_type,
            memory_subdir=memory_subdir,
            embeddings_model=embeddings_model_config, # Use the potentially complex object
            bulk_processing_enabled=bulk_enabled,
            bulk_batch_size=bulk_batch_size,
            bulk_timeout=bulk_timeout
        )

        # Get global UI settings for embedding model details if needed (especially for Graphiti)
        ui_settings = global_settings.get_settings()
        ui_embed_provider = ui_settings.get("embed_model_provider", "")
        ui_embed_model_name = ui_settings.get("embed_model_name", "")

        if backend_type == "graphiti":
            graphiti_embedding_model_to_use = os.getenv("GRAPHITI_EMBEDDINGS_MODEL", "text-embedding-ada-002") # Default
            provider_name_upper = str(ui_embed_provider).upper() # Make sure it's a string

            # This logic might need refinement based on how ModelProvider enum/values are structured
            if "OPENAI" in provider_name_upper and ui_embed_model_name:
                graphiti_embedding_model_to_use = ui_embed_model_name
            elif "GOOGLE" in provider_name_upper and ui_embed_model_name: # Example
                graphiti_embedding_model_to_use = ui_embed_model_name

            config.graphiti_config = {
                "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
                "user": os.getenv("NEO4J_USER", "neo4j"),
                "password": os.getenv("NEO4J_PASSWORD", "password"),
                "group_id": os.getenv("GRAPHITI_GROUP_ID", getattr(self.agent.config, 'graphiti_group_id', 'agent-zero-default')),
                "embeddings_model": graphiti_embedding_model_to_use,
                "bulk_batch_size": str(bulk_batch_size), # Graphiti backend might expect string
                "bulk_timeout": str(bulk_timeout)     # Graphiti backend might expect string
            }
        return config

    async def _ensure_initialized(self):
        """Ensure the backend is initialized."""
        if self.using_markdown_enhanced_layer:
            if self.markdown_enhanced_layer is None or not self.markdown_enhanced_layer.is_initialized:
                print("WARN: Markdown enhanced layer accessed before full initialization or was None. Re-initializing.")
                await self.initialize(memory_backend_override="enhanced")
        elif self.original_backend is None: # Original Faiss/Graphiti path
            await self.initialize()


    async def insert_text(self, text: str, metadata: Dict[str, Any]) -> str:
        await self._ensure_initialized()
        if self.using_markdown_enhanced_layer and self.markdown_enhanced_layer:
            return await self.markdown_enhanced_layer.store(text, metadata)
        elif self.original_backend:
            return await self.original_backend.insert_text(text, metadata)
        raise ConnectionError("Memory backend not available.")

    async def search_similarity_threshold(
        self, query: str, limit: int = 10, threshold: float = 0.7, filter: Optional[Dict[str, Any]] = None
    ) -> List[MemoryDocument]:
        await self._ensure_initialized()
        if self.using_markdown_enhanced_layer and self.markdown_enhanced_layer:
            memory_items = await self.markdown_enhanced_layer.search_memories(query, limit=limit, filters=filter)
            return [MemoryDocument(id=mi.id, page_content=mi.content, metadata=mi.metadata, score=mi.importance_score) for mi in memory_items]
        elif self.original_backend:
            return await self.original_backend.search_similarity_threshold(query, limit, threshold, filter)
        raise ConnectionError("Memory backend not available.")

    async def delete_documents_by_ids(self, ids: List[str]) -> List[MemoryDocument]:
        await self._ensure_initialized()
        if self.using_markdown_enhanced_layer:
            print("WARN: delete_documents_by_ids not directly implemented in the markdown enhanced layer.")
            return []
        elif self.original_backend:
            return await self.original_backend.delete_documents_by_ids(ids)
        raise ConnectionError("Memory backend not available for delete operation.")

    async def get_documents_by_ids(self, ids: List[str]) -> List[MemoryDocument]:
        await self._ensure_initialized()
        if self.using_markdown_enhanced_layer:
            print("WARN: get_documents_by_ids not directly implemented in the markdown enhanced layer.")
            return []
        elif self.original_backend:
            return await self.original_backend.get_documents_by_ids(ids)
        raise ConnectionError("Memory backend not available.")

    async def insert_knowledge_document(self, content: str, metadata: Dict[str, Any]) -> str:
        await self._ensure_initialized()
        if self.using_markdown_enhanced_layer and self.markdown_enhanced_layer:
            item_id, _ = await self.markdown_enhanced_layer.classify_and_route_content(content, "knowledge_document", metadata)
            return item_id
        elif self.original_backend:
            return await self.original_backend.insert_knowledge_document(content, metadata)
        raise ConnectionError("Memory backend not available.")

    async def insert_content(self, content: str, content_type: str, metadata: Dict[str, Any]) -> str:
        await self._ensure_initialized()
        metadata.setdefault("content_type", content_type)
        if self.using_markdown_enhanced_layer and self.markdown_enhanced_layer:
            item_id, _ = await self.markdown_enhanced_layer.classify_and_route_content(content, content_type, metadata)
            return item_id
        elif self.original_backend:
            if content_type == "knowledge_document":
                return await self.original_backend.insert_knowledge_document(content, metadata)
            elif content_type == "agent_memory":
                return await self.original_backend.insert_text(content, metadata)
            else:
                print(f"Warning: Unknown content_type '{content_type}' for original backend, attempting generic insert_text.")
                return await self.original_backend.insert_text(content, metadata)
        raise ConnectionError("Memory backend not available.")

    async def add_episodes_bulk(self, episodes: List[Dict[str, Any]]) -> List[str]:
        await self._ensure_initialized()
        if self.using_markdown_enhanced_layer and self.markdown_enhanced_layer:
            print("WARN: add_episodes_bulk is being processed individually through the markdown enhanced layer.")
            ids = []
            for episode_data in episodes:
                item_id, _ = await self.markdown_enhanced_layer.classify_and_route_content(
                    episode_data.get("content", ""),
                    episode_data.get("metadata", {}).get("content_type", "agent_memory"),
                    episode_data.get("metadata", {})
                )
                ids.append(item_id)
            return ids
        elif self.original_backend and self.original_config:
            if not self.original_config.bulk_processing_enabled or not self.original_backend.supports_bulk_processing():
                return await self._process_episodes_individually(episodes)
            return await self.original_backend.add_episode_bulk(episodes)
        raise ConnectionError("Memory backend not available.")

    async def process_knowledge_documents_bulk(self, documents: List[Dict[str, Any]]) -> List[str]:
        await self._ensure_initialized()
        if not documents: return []
        if self.using_markdown_enhanced_layer and self.markdown_enhanced_layer:
            print("WARN: process_knowledge_documents_bulk is being processed individually through markdown enhanced layer.")
            ids = []
            for doc_data in documents:
                item_id, _ = await self.markdown_enhanced_layer.classify_and_route_content(
                    doc_data.get("content", ""), "knowledge_document", doc_data.get("metadata", {}))
                ids.append(item_id)
            return ids
        elif self.original_backend and self.original_config:
            prepared_docs = self._prepare_knowledge_docs_for_bulk(documents)
            if (self.original_config.bulk_processing_enabled and
                self.original_backend.supports_bulk_processing() and
                hasattr(self.original_backend, 'insert_knowledge_documents_bulk')):
                return await self.original_backend.insert_knowledge_documents_bulk(prepared_docs)
            else:
                doc_ids = []
                for doc_content in prepared_docs:
                    doc_id = await self.original_backend.insert_knowledge_document(
                        doc_content["content"], doc_content.get("metadata", {}))
                    doc_ids.append(doc_id)
                return doc_ids
        raise ConnectionError("Memory backend not available.")

    def _prepare_knowledge_docs_for_bulk(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        prepared_docs = []
        for doc in documents:
            if "content" not in doc: continue
            metadata = doc.get("metadata", {})
            prepared_docs.append({
                "name": metadata.get("name", f"Knowledge Document: {metadata.get('filename', 'Unknown')}"),
                "content": doc["content"],
                "source": "json" if isinstance(doc["content"], dict) else "text",
                "source_description": metadata.get("source_description", f"agent-zero-knowledge-{metadata.get('area', 'main')}"),
                "reference_time": metadata.get("timestamp"),
                "metadata": {k: v for k, v in metadata.items() if k not in ["name", "timestamp", "source_description"]}
            })
        return prepared_docs

    async def _process_episodes_individually(self, episodes: List[Dict[str, Any]]) -> List[str]:
        if not self.original_backend:
            raise ConnectionError("Original memory backend not available for _process_episodes_individually.")
        episode_ids = []
        for episode in episodes:
            try:
                content = episode["content"]
                metadata = episode.get("metadata", {})
                if episode.get("source") == "knowledge" or metadata.get("content_type") == "knowledge_document":
                    episode_id = await self.original_backend.insert_knowledge_document(content, metadata)
                else:
                    episode_id = await self.original_backend.insert_text(content, metadata)
                episode_ids.append(episode_id)
            except Exception as e:
                print(f"Failed to process individual episode: {e}")
                continue
        return episode_ids

    def backend_supports_bulk(self) -> bool:
        if self.using_markdown_enhanced_layer:
            return False
        return (self.original_backend is not None and
                self.original_backend.supports_bulk_processing() and
                self.original_config is not None and self.original_config.bulk_processing_enabled)

    async def get_bulk_processing_stats(self) -> Dict[str, Any]:
        await self._ensure_initialized()
        if self.using_markdown_enhanced_layer and self.markdown_enhanced_layer:
            status = await self.markdown_enhanced_layer.get_system_status()
            return {
                "backend_type": "enhanced_intelligent_classification",
                "system_status": status
            }
        elif self.original_backend and self.original_config:
            return {
                "backend_type": self.original_config.backend_type,
                "bulk_processing_enabled": self.original_config.bulk_processing_enabled,
                "bulk_batch_size": self.original_config.bulk_batch_size,
                "bulk_timeout": self.original_config.bulk_timeout,
                "backend_supports_bulk": self.original_backend.supports_bulk_processing(),
                "effective_bulk_enabled": (self.original_backend.supports_bulk_processing() and
                                           self.original_config.bulk_processing_enabled)
            }
        raise ConnectionError("Memory backend not available.")
