# Environment Setup
## Development Environment Configuration

This document provides step-by-step instructions for setting up the development environment for implementing the intelligent data classification system.

## 🐍 Python Environment Setup

### Step 1: Python Installation

**Check Current Python Version:**
```bash
python --version
# Should be Python 3.8 or higher
```

**Install Python 3.9+ (if needed):**

**Windows:**
```bash
# Using winget
winget install Python.Python.3.11

# Or download from python.org
# https://www.python.org/downloads/windows/
```

**macOS:**
```bash
# Using Homebrew
brew install python@3.11

# Or using pyenv
brew install pyenv
pyenv install 3.11.0
pyenv global 3.11.0
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev
```

### Step 2: Virtual Environment Setup

**Create Project Virtual Environment:**
```bash
# Navigate to Agent Zero project root
cd /path/to/agent-zero-v2

# Create virtual environment
python -m venv agent-zero-env

# Activate virtual environment
# Windows:
agent-zero-env\Scripts\activate
# macOS/Linux:
source agent-zero-env/bin/activate

# Verify activation
which python
# Should point to your virtual environment
```

**Configure Virtual Environment:**
```bash
# Upgrade pip
python -m pip install --upgrade pip

# Install wheel for better package compilation
pip install wheel

# Verify environment
pip list
```

### Step 3: Development Tools Installation

**Essential Development Tools:**
```bash
# Code formatting and linting
pip install black>=23.0.0
pip install flake8>=6.0.0
pip install mypy>=1.5.0

# Testing framework
pip install pytest>=7.0.0
pip install pytest-asyncio>=0.21.0
pip install pytest-cov>=4.1.0
pip install pytest-mock>=3.11.0

# Development utilities
pip install ipython>=8.0.0
pip install jupyter>=1.0.0
pip install python-dotenv>=1.0.0
```

## 🔧 IDE Configuration

### VS Code Setup (Recommended)

**Install VS Code Extensions:**
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "ms-python.mypy-type-checker",
    "ms-toolsai.jupyter",
    "ms-vscode.test-adapter-converter",
    "littlefoxteam.vscode-python-test-adapter"
  ]
}
```

**VS Code Settings (`.vscode/settings.json`):**
```json
{
  "python.defaultInterpreterPath": "./agent-zero-env/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "python.testing.pytestArgs": [
    "tests"
  ],
  "files.exclude": {
    "**/__pycache__": true,
    "**/*.pyc": true,
    ".pytest_cache": true,
    ".mypy_cache": true
  }
}
```

**VS Code Launch Configuration (`.vscode/launch.json`):**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Current File",
      "type": "python",
      "request": "launch",
      "program": "${file}",
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/.env"
    },
    {
      "name": "Python: Test Current File",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": ["${file}", "-v"],
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/.env"
    }
  ]
}
```

### PyCharm Setup (Alternative)

**Configure Python Interpreter:**
1. File → Settings → Project → Python Interpreter
2. Add Interpreter → Existing Environment
3. Select `agent-zero-env/bin/python`

**Configure Code Style:**
1. File → Settings → Editor → Code Style → Python
2. Set line length to 88 (Black default)
3. Enable "Use tabs" = False, Tab size = 4

**Configure Testing:**
1. File → Settings → Tools → Python Integrated Tools
2. Default test runner: pytest
3. Enable pytest auto-detection

## 🗄️ Database Setup

### SQLite (Development)
```bash
# SQLite comes with Python, verify installation
python -c "import sqlite3; print(sqlite3.version)"

# Create development database directory
mkdir -p data/dev
```

### Redis (Optional - for caching)
**Installation:**

**Windows:**
```bash
# Using Chocolatey
choco install redis-64

# Or download from GitHub releases
# https://github.com/microsoftarchive/redis/releases
```

**macOS:**
```bash
brew install redis
brew services start redis
```

**Linux:**
```bash
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**Test Redis Connection:**
```bash
redis-cli ping
# Should return: PONG
```

### PostgreSQL (Production - Optional)
**Installation:**

**Windows:**
```bash
# Download from postgresql.org
# Or using Chocolatey
choco install postgresql
```

**macOS:**
```bash
brew install postgresql
brew services start postgresql
```

**Linux:**
```bash
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 🔐 Environment Variables Setup

### Create Environment File

**File:** `.env` (in project root)
```bash
# API Configuration
OPENAI_API_KEY=your-openai-api-key-here
GRAPHITI_API_KEY=your-graphiti-api-key-here

# Development Settings
DEBUG=true
LOG_LEVEL=DEBUG
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=sqlite:///data/dev/agent_zero.db
REDIS_URL=redis://localhost:6379/0

# Classification Configuration
CLASSIFICATION_BACKEND=faiss
CLASSIFICATION_SEMANTIC_OVERLAP_SHARED=0.70
CLASSIFICATION_SEMANTIC_OVERLAP_CUSTOM=0.30

# Performance Configuration
CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600
PERFORMANCE_MONITORING=true

# Testing Configuration
TEST_DATABASE_URL=sqlite:///data/test/agent_zero_test.db
TEST_REDIS_URL=redis://localhost:6379/1
```

### Environment Variable Validation

**Create validation script:** `scripts/validate_env.py`
```python
#!/usr/bin/env python3
"""
Environment validation script
"""

import os
from dotenv import load_dotenv

def validate_environment():
    """Validate that all required environment variables are set"""
    
    # Load environment variables
    load_dotenv()
    
    required_vars = [
        'OPENAI_API_KEY',
        'DEBUG',
        'LOG_LEVEL',
        'ENVIRONMENT'
    ]
    
    optional_vars = [
        'GRAPHITI_API_KEY',
        'REDIS_URL',
        'DATABASE_URL'
    ]
    
    missing_required = []
    missing_optional = []
    
    print("🔍 Validating Environment Variables...")
    print("=" * 50)
    
    # Check required variables
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 10)}")
        else:
            missing_required.append(var)
            print(f"❌ {var}: NOT SET")
    
    # Check optional variables
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * min(len(value), 10)}")
        else:
            missing_optional.append(var)
            print(f"⚠️  {var}: NOT SET (optional)")
    
    print("=" * 50)
    
    if missing_required:
        print(f"❌ Missing required variables: {missing_required}")
        return False
    
    if missing_optional:
        print(f"⚠️  Missing optional variables: {missing_optional}")
    
    print("✅ Environment validation passed!")
    return True

if __name__ == "__main__":
    success = validate_environment()
    exit(0 if success else 1)
```

**Run validation:**
```bash
python scripts/validate_env.py
```

## 🧪 Testing Environment Setup

### Test Directory Structure
```bash
# Create test directory structure
mkdir -p tests/{unit,integration,e2e}
mkdir -p tests/fixtures
mkdir -p tests/data

# Create test configuration
touch tests/conftest.py
touch tests/__init__.py
```

### Test Configuration

**File:** `tests/conftest.py`
```python
"""
Pytest configuration and fixtures
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)

@pytest.fixture
def test_env():
    """Set up test environment variables"""
    original_env = os.environ.copy()
    
    # Set test environment variables
    os.environ.update({
        'ENVIRONMENT': 'test',
        'DEBUG': 'true',
        'LOG_LEVEL': 'DEBUG',
        'DATABASE_URL': 'sqlite:///:memory:',
        'REDIS_URL': 'redis://localhost:6379/1',
        'CACHE_ENABLED': 'false'
    })
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)

@pytest.fixture
async def mock_openai_client():
    """Mock OpenAI client for testing"""
    from unittest.mock import AsyncMock, Mock
    
    mock_client = Mock()
    mock_client.embeddings = Mock()
    mock_client.embeddings.create = AsyncMock(return_value=Mock(
        data=[Mock(embedding=[0.1, 0.2, 0.3] * 100)]  # 300-dim embedding
    ))
    
    return mock_client
```

### Test Data Setup

**Create test data directory:**
```bash
mkdir -p tests/data
```

**File:** `tests/data/sample_documents.json`
```json
{
  "programming_docs": [
    {
      "content": "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
      "metadata": {"type": "code", "language": "python"}
    },
    {
      "content": "class DataProcessor: def __init__(self): self.data = []",
      "metadata": {"type": "code", "language": "python"}
    }
  ],
  "conversation_docs": [
    {
      "content": "user: Hello, how are you?\nassistant: I'm doing well, thank you!",
      "metadata": {"type": "conversation", "session_id": "test_123"}
    }
  ],
  "knowledge_docs": [
    {
      "content": "Machine learning is a subset of artificial intelligence.",
      "metadata": {"type": "knowledge", "domain": "ai"}
    }
  ]
}
```

## 📊 Monitoring and Logging Setup

### Logging Configuration

**File:** `python/helpers/logging_config.py`
```python
"""
Logging configuration for the classification system
"""

import logging
import logging.config
import os
from datetime import datetime

def setup_logging():
    """Set up logging configuration"""
    
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'detailed': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            },
            'simple': {
                'format': '%(levelname)s - %(message)s'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': log_level,
                'formatter': 'simple',
                'stream': 'ext://sys.stdout'
            },
            'file': {
                'class': 'logging.FileHandler',
                'level': 'DEBUG',
                'formatter': 'detailed',
                'filename': f'logs/agent_zero_{datetime.now().strftime("%Y%m%d")}.log',
                'mode': 'a'
            }
        },
        'loggers': {
            'agent_zero': {
                'level': log_level,
                'handlers': ['console', 'file'],
                'propagate': False
            }
        },
        'root': {
            'level': log_level,
            'handlers': ['console']
        }
    }
    
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    logging.config.dictConfig(config)
    
    return logging.getLogger('agent_zero')
```

## ✅ Environment Validation

### Complete Environment Check

**File:** `scripts/environment_check.py`
```python
#!/usr/bin/env python3
"""
Complete environment validation
"""

import sys
import subprocess
import importlib
from pathlib import Path

def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (requires 3.8+)")
        return False

def check_virtual_environment():
    """Check if running in virtual environment"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment active")
        return True
    else:
        print("⚠️  Not running in virtual environment")
        return False

def check_required_packages():
    """Check required packages"""
    required_packages = [
        'numpy', 'scipy', 'scikit-learn', 'networkx', 
        'python-dateutil', 'pytest', 'black', 'mypy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages

def check_optional_services():
    """Check optional services"""
    services = {
        'redis': 'redis-cli ping',
        'postgresql': 'psql --version'
    }
    
    for service, command in services.items():
        try:
            result = subprocess.run(command.split(), capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ {service} available")
            else:
                print(f"⚠️  {service} not available")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"⚠️  {service} not available")

def main():
    """Run complete environment check"""
    print("🔍 Environment Validation")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_virtual_environment()
    ]
    
    packages_ok, missing = check_required_packages()
    checks.append(packages_ok)
    
    print("\n🔧 Optional Services:")
    check_optional_services()
    
    print("\n" + "=" * 50)
    
    if all(checks):
        print("✅ Environment setup complete!")
        if missing:
            print(f"Install missing packages: pip install {' '.join(missing)}")
        return True
    else:
        print("❌ Environment setup incomplete")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

**Run complete validation:**
```bash
python scripts/environment_check.py
```

---

**Next Step**: [Phase 1: Foundation Setup](../01-foundation/dependencies.md)
