import asyncio
import unittest
from unittest.mock import patch, MagicMock # AsyncMock not needed if not mocking async methods of UPM itself directly
from datetime import datetime, timezone

# Adjust imports based on actual file structure and class locations
try:
    from python.helpers.user_preferences import (
        UserPreferenceManager,
        PreferenceType,
        LearningSignal,
        PreferenceItem,
        UserFeedback
    )
    # If using the placeholder for classification_config within user_preferences.py for testing:
    from python.helpers.user_preferences import classification_config as user_prefs_classification_config
except ImportError:
    print("WARN: test_user_preferences.py: Could not import from python.helpers.user_preferences. Using placeholders.")
    from enum import Enum
    from dataclasses import dataclass, field
    class PreferenceType(Enum): DOMAIN_WEIGHT = "domain_weight"; SEARCH_PREFERENCE = "search_preference"
    class LearningSignal(Enum): POSITIVE_FEEDBACK = "positive"; NEGATIVE_FEEDBACK = "negative"; IMPLICIT_USAGE = "implicit"; EXPLICIT_SETTING = "explicit"
    @dataclass
    class PreferenceItem: key: str; value: Any; confidence: float; last_updated: datetime; learning_history: list = field(default_factory=list); user_explicit: bool = False; preference_type: PreferenceType = PreferenceType.DOMAIN_WEIGHT
    @dataclass
    class UserFeedback: action: str; context: dict; feedback_type: LearningSignal; timestamp: datetime; value: Any = None
    class UserPreferenceManager:
        def __init__(self, user_id): self.user_id = user_id; self.preferences = {}; self.feedback_history = []; self.learning_rate = 0.1; self.adaptation_enabled = True; self._initialize_default_preferences()
        def _initialize_default_preferences(self):
            self.preferences['domain_weight_test'] = PreferenceItem("test",1.0,0.5,datetime.now(timezone.utc), preference_type=PreferenceType.DOMAIN_WEIGHT)
            self.preferences['search_result_limit'] = PreferenceItem("search_limit",10,0.5,datetime.now(timezone.utc), preference_type=PreferenceType.SEARCH_PREFERENCE)
        async def get_preference(self, key, default=None): return self.preferences.get(key, MagicMock(value=default)).value
        async def set_preference(self, key, value, pref_type, user_explicit=True): self.preferences[key] = PreferenceItem(key,value,1.0 if user_explicit else 0.5, datetime.now(timezone.utc),user_explicit=user_explicit, preference_type=pref_type); return True
        async def record_feedback(self, action, context, feedback_type, value=None): self.feedback_history.append(UserFeedback(action,context,feedback_type,datetime.now(timezone.utc),value)); await self._process_feedback(self.feedback_history[-1])
        async def _process_feedback(self, feedback): pass # Simplified for placeholder

    # Placeholder for classification_config if needed by tests directly (not typical for unit tests of UPM)
    class ClassificationConfigPlaceholder: preference_learning_rate = 0.1; enable_adaptive_learning = True
    user_prefs_classification_config = ClassificationConfigPlaceholder()


class TestUserPreferenceManager(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        # This ensures that each test uses a fresh manager with default settings.
        self.manager = UserPreferenceManager("test_user1")
        # If user_preferences.py uses a global classification_config, tests might need to patch it
        # or ensure the placeholder within user_preferences.py is suitable.
        # For now, assuming UserPreferenceManager correctly uses its own copy or a settable one.

    async def test_initialization_and_default_preferences(self):
        self.assertEqual(self.manager.user_id, "test_user1")
        # Check a few default preferences (keys might vary based on actual defaults)
        default_prog_weight = await self.manager.get_preference('domain_weight_programming')
        self.assertIsNotNone(default_prog_weight, "Default programming weight should exist.")
        self.assertTrue(0 <= default_prog_weight <= 1.0)

        default_search_limit = await self.manager.get_preference('search_result_limit')
        self.assertIsNotNone(default_search_limit)
        self.assertIsInstance(default_search_limit, int)

    async def test_get_set_preference_explicit(self):
        key = "test_explicit_pref"
        value = "test_value"
        pref_type = PreferenceType.DOMAIN_WEIGHT # Example type

        # Test setting a new preference
        set_success = await self.manager.set_preference(key, value, pref_type, user_explicit=True)
        self.assertTrue(set_success)

        retrieved_value = await self.manager.get_preference(key)
        self.assertEqual(retrieved_value, value)

        pref_item = self.manager.preferences.get(key)
        self.assertIsNotNone(pref_item)
        self.assertTrue(pref_item.user_explicit)
        self.assertEqual(pref_item.confidence, 1.0)
        self.assertEqual(pref_item.preference_type, pref_type)

        # Test updating an existing preference
        new_value = "new_test_value"
        update_success = await self.manager.set_preference(key, new_value, pref_type, user_explicit=True)
        self.assertTrue(update_success)

        retrieved_new_value = await self.manager.get_preference(key)
        self.assertEqual(retrieved_new_value, new_value)
        self.assertTrue(self.manager.preferences.get(key).user_explicit) # Should remain explicit

    async def test_get_non_existent_preference_with_default(self):
        default_val = "default_for_non_existent"
        retrieved_value = await self.manager.get_preference("non_existent_key_123", default_val)
        self.assertEqual(retrieved_value, default_val)

    async def test_record_feedback_history(self):
        self.assertEqual(len(self.manager.feedback_history), 0)
        await self.manager.record_feedback(
            action="test_action",
            context={"data": "test_context"},
            feedback_type=LearningSignal.IMPLICIT_USAGE,
            value="test_feedback_value"
        )
        self.assertEqual(len(self.manager.feedback_history), 1)
        feedback_entry = self.manager.feedback_history[0]
        self.assertEqual(feedback_entry.action, "test_action")
        self.assertEqual(feedback_entry.value, "test_feedback_value")

    async def test_positive_feedback_reinforces_preference(self):
        pref_key = 'domain_weight_documentation' # A default, non-explicit preference
        initial_value = await self.manager.get_preference(pref_key)
        self.assertFalse(self.manager.preferences[pref_key].user_explicit)

        # Simulate positive feedback for 'documentation' domain
        mock_classification_result = {'domain': 'documentation', 'confidence': 0.9}
        await self.manager.record_feedback(
            action="classification_success",
            context={'classification_result': mock_classification_result},
            feedback_type=LearningSignal.POSITIVE_FEEDBACK
        )

        updated_value = await self.manager.get_preference(pref_key)
        self.assertTrue(updated_value > initial_value,
                        f"Value for {pref_key} should increase after positive feedback. Initial: {initial_value}, Updated: {updated_value}")
        self.assertTrue(self.manager.preferences[pref_key].confidence > 0.5) # Confidence should increase

    async def test_negative_feedback_adjusts_preference(self):
        pref_key = 'domain_weight_general' # Another default, non-explicit
        initial_value = await self.manager.get_preference(pref_key)
        self.assertFalse(self.manager.preferences[pref_key].user_explicit)

        mock_classification_result = {'domain': 'general', 'confidence': 0.3} # Low confidence implies poor result here
        await self.manager.record_feedback(
            action="classification_failure", # Example action
            context={'classification_result': mock_classification_result},
            feedback_type=LearningSignal.NEGATIVE_FEEDBACK
        )

        updated_value = await self.manager.get_preference(pref_key)
        self.assertTrue(updated_value < initial_value,
                        f"Value for {pref_key} should decrease after negative feedback. Initial: {initial_value}, Updated: {updated_value}")

    async def test_explicit_preference_not_overridden_by_learning(self):
        pref_key = 'domain_weight_programming'
        explicit_value = 0.99
        await self.manager.set_preference(pref_key, explicit_value, PreferenceType.DOMAIN_WEIGHT, user_explicit=True)
        self.assertTrue(self.manager.preferences[pref_key].user_explicit)
        self.assertEqual(await self.manager.get_preference(pref_key), explicit_value)

        # Try to change it with strong positive feedback
        mock_classification_result = {'domain': 'programming', 'confidence': 1.0}
        await self.manager.record_feedback(
            action="classification_very_successful",
            context={'classification_result': mock_classification_result},
            feedback_type=LearningSignal.POSITIVE_FEEDBACK
        )

        # Value should remain unchanged because it was user_explicit
        value_after_learning_attempt = await self.manager.get_preference(pref_key)
        self.assertEqual(value_after_learning_attempt, explicit_value,
                         "User-explicit preference should not be changed by learning signals.")
        self.assertEqual(self.manager.preferences[pref_key].confidence, 1.0) # Confidence remains high

    async def test_implicit_usage_search_limit(self):
        pref_key = 'search_result_limit'
        initial_limit = await self.manager.get_preference(pref_key) # Default is 10
        self.assertFalse(self.manager.preferences[pref_key].user_explicit)

        # Simulate user consistently viewing fewer results than the limit
        await self.manager.record_feedback(
            action="search_query",
            context={'result_count': 2}, # User effectively used/needed only 2 results
            feedback_type=LearningSignal.IMPLICIT_USAGE
        )

        updated_limit = await self.manager.get_preference(pref_key)
        expected_new_limit = max(5, int(initial_limit * 0.9)) # Based on current UPM logic
        self.assertEqual(updated_limit, expected_new_limit,
                         f"Search limit should adjust downwards. Initial: {initial_limit}, Updated: {updated_limit}, Expected: {expected_new_limit}")

    async def test_learning_history_recorded(self):
        pref_key = 'domain_weight_conversations'
        initial_pref_item = self.manager.preferences[pref_key]
        self.assertEqual(len(initial_pref_item.learning_history), 0)

        # Explicit set
        await self.manager.set_preference(pref_key, 0.75, PreferenceType.DOMAIN_WEIGHT, user_explicit=True)
        self.assertEqual(len(initial_pref_item.learning_history), 1)
        self.assertEqual(initial_pref_item.learning_history[0]['new_value'], 0.75)
        self.assertEqual(initial_pref_item.learning_history[0]['reason'], 'explicit_setting')

        # Set it again (not explicit) to see another history item for learning
        await self.manager.set_preference(pref_key, 0.65, PreferenceType.DOMAIN_WEIGHT, user_explicit=False)
        self.assertEqual(len(initial_pref_item.learning_history), 2)
        self.assertEqual(initial_pref_item.learning_history[1]['new_value'], 0.65)
        self.assertEqual(initial_pref_item.learning_history[1]['reason'], 'system_update_via_set_preference') # As per current _record_preference_change

        # Now a learning feedback
        self.manager.preferences[pref_key].user_explicit = False # Ensure learning can happen
        initial_value = await self.manager.get_preference(pref_key) # Should be 0.65

        mock_cr = {'domain': 'conversations', 'confidence': 0.8}
        await self.manager.record_feedback("action", {'classification_result': mock_cr}, LearningSignal.POSITIVE_FEEDBACK)

        self.assertEqual(len(initial_pref_item.learning_history), 3)
        self.assertTrue(initial_pref_item.learning_history[2]['new_value'] > initial_value)
        self.assertEqual(initial_pref_item.learning_history[2]['reason'], 'positive_feedback_domain')


if __name__ == '__main__':
    unittest.main()
