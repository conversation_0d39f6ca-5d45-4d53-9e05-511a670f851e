# Data Separation and Integration Specifications
## Strict Boundaries Between History and Knowledge Systems

This document defines the critical data separation boundaries between Agent Zero's history system and the Graphiti knowledge graph system, ensuring zero data duplication while enabling seamless integration.

## 🎯 Separation Principles

### Core Separation Philosophy
- **History System**: Raw conversational data, message sequences, chat metadata
- **Graphiti System**: Processed knowledge, entities, relationships, imported documents
- **Zero Duplication**: No data exists in both systems simultaneously
- **Clear Boundaries**: Explicit data ownership and processing responsibilities

## 📊 Data Ownership Matrix

| Data Type | History System | Graphiti System | Processing Flow |
|-----------|---------------|-----------------|-----------------|
| **Raw Conversations** | ✅ Primary Owner | ❌ Never Stored | History → Extract → Graphiti |
| **Message Threads** | ✅ Primary Owner | ❌ Never Stored | History Only |
| **Chat Metadata** | ✅ Primary Owner | ❌ Never Stored | History Only |
| **User Interactions** | ✅ Primary Owner | ❌ Never Stored | History Only |
| **Extracted Entities** | ❌ Never Stored | ✅ Primary Owner | History → Process → Graphiti |
| **Relationships** | ❌ Never Stored | ✅ Primary Owner | History → Process → Graphiti |
| **Knowledge Documents** | ❌ Never Stored | ✅ Primary Owner | Direct → Graphiti |
| **Code Analysis** | ❌ Never Stored | ✅ Primary Owner | Direct → Graphiti |
| **Temporal Facts** | ❌ Never Stored | ✅ Primary Owner | History → Process → Graphiti |

## 🔄 Data Flow Architecture

### Processing Pipeline

```mermaid
graph TB
    subgraph "Input Sources"
        UC[User Conversations]
        DOC[Documents]
        CODE[Code Repos]
        API[External APIs]
    end
    
    subgraph "History System Boundary"
        HIS[History Storage<br/>Raw Conversations<br/>Message Threads<br/>Chat Metadata]
        HEX[History Extractor<br/>Entity Detection<br/>Fact Extraction]
    end
    
    subgraph "Processing Layer"
        CL[Classification Engine]
        ENT[Entity Processor]
        REL[Relationship Detector]
    end
    
    subgraph "Graphiti System Boundary"
        GKG[Knowledge Graph<br/>Entities<br/>Relationships<br/>Temporal Facts]
        GON[Ontology Manager<br/>Schema Evolution<br/>Namespace Isolation]
    end
    
    subgraph "Integration Layer"
        CSQ[Cross-System Query Engine]
        API_LAYER[Unified API Layer]
    end
    
    UC --> HIS
    UC --> HEX
    DOC --> CL
    CODE --> CL
    API --> CL
    
    HEX --> ENT
    CL --> ENT
    ENT --> REL
    REL --> GKG
    
    GKG --> GON
    
    HIS --> CSQ
    GKG --> CSQ
    CSQ --> API_LAYER
    
    style HIS fill:#ffebee
    style GKG fill:#e8f5e8
    style CSQ fill:#fff3e0
```

## 🛡️ Strict Boundary Enforcement

### History System Responsibilities

**Exclusive Data Types**:
```python
class HistorySystemBoundary:
    """
    Defines what data ONLY belongs in the history system
    """
    EXCLUSIVE_DATA_TYPES = {
        'raw_conversations': {
            'description': 'Unprocessed user messages and agent responses',
            'storage_format': 'chronological_sequence',
            'retention_policy': 'user_configurable',
            'processing_allowed': False  # Never processed into knowledge
        },
        
        'message_threads': {
            'description': 'Conversation threading and context',
            'storage_format': 'hierarchical_topics',
            'retention_policy': 'session_based',
            'processing_allowed': False
        },
        
        'chat_metadata': {
            'description': 'Timestamps, user IDs, session info',
            'storage_format': 'relational_metadata',
            'retention_policy': 'audit_requirements',
            'processing_allowed': False
        },
        
        'user_interactions': {
            'description': 'UI interactions, clicks, navigation',
            'storage_format': 'event_stream',
            'retention_policy': 'analytics_window',
            'processing_allowed': False
        }
    }
    
    @staticmethod
    def validate_data_ownership(data_type, target_system):
        """
        Validate that data is being stored in the correct system
        """
        if data_type in HistorySystemBoundary.EXCLUSIVE_DATA_TYPES:
            if target_system != 'history':
                raise DataBoundaryViolation(
                    f"Data type '{data_type}' can only be stored in history system"
                )
        return True
```

### Graphiti System Responsibilities

**Exclusive Data Types**:
```python
class GraphitiSystemBoundary:
    """
    Defines what data ONLY belongs in the Graphiti system
    """
    EXCLUSIVE_DATA_TYPES = {
        'extracted_entities': {
            'description': 'Named entities extracted from any source',
            'storage_format': 'graph_nodes',
            'source_systems': ['history_extractor', 'document_processor', 'code_analyzer'],
            'raw_data_stored': False
        },
        
        'entity_relationships': {
            'description': 'Relationships between entities',
            'storage_format': 'graph_edges',
            'temporal_tracking': True,
            'raw_data_stored': False
        },
        
        'knowledge_documents': {
            'description': 'Imported documents and their processed content',
            'storage_format': 'document_nodes_with_entities',
            'source_preservation': True,
            'raw_data_stored': True  # Original documents preserved
        },
        
        'temporal_facts': {
            'description': 'Time-stamped factual assertions',
            'storage_format': 'temporal_graph_edges',
            'validity_periods': True,
            'raw_data_stored': False
        },
        
        'code_knowledge': {
            'description': 'Code structure, APIs, dependencies',
            'storage_format': 'code_entity_graph',
            'version_tracking': True,
            'raw_data_stored': False  # Code files not duplicated
        }
    }
    
    @staticmethod
    def validate_processed_data(data, source_system):
        """
        Ensure only processed data enters Graphiti system
        """
        if source_system == 'history' and not data.get('processed', False):
            raise DataBoundaryViolation(
                "Raw history data cannot be stored in Graphiti system"
            )
        return True
```

## 🔗 Integration Points and APIs

### Cross-System Query Engine

**Unified Query Interface**:
```python
class CrossSystemQueryEngine:
    """
    Provides unified querying across history and knowledge systems
    without data duplication
    """
    
    def __init__(self, history_backend, graphiti_backend):
        self.history = history_backend
        self.graphiti = graphiti_backend
        self.query_router = QueryRouter()
    
    async def unified_search(self, query, context=None, include_history=True, include_knowledge=True):
        """
        Search across both systems while maintaining separation
        """
        results = {
            'history_results': [],
            'knowledge_results': [],
            'combined_insights': []
        }
        
        # Route query to appropriate systems
        if include_history:
            history_query = self.query_router.adapt_for_history(query, context)
            results['history_results'] = await self.history.search(history_query)
        
        if include_knowledge:
            knowledge_query = self.query_router.adapt_for_graphiti(query, context)
            results['knowledge_results'] = await self.graphiti.search(knowledge_query)
        
        # Generate combined insights without duplicating data
        if include_history and include_knowledge:
            results['combined_insights'] = self._generate_cross_system_insights(
                results['history_results'], 
                results['knowledge_results']
            )
        
        return results
    
    def _generate_cross_system_insights(self, history_results, knowledge_results):
        """
        Generate insights by correlating history and knowledge without duplication
        """
        insights = []
        
        for history_item in history_results:
            # Find related knowledge without storing history data in Graphiti
            related_entities = self._find_related_entities(history_item)
            
            for entity in related_entities:
                if entity in [kr.entity_id for kr in knowledge_results]:
                    insight = {
                        'type': 'correlation',
                        'history_reference': history_item.id,  # Reference only
                        'knowledge_entity': entity,
                        'correlation_strength': self._calculate_correlation(history_item, entity)
                    }
                    insights.append(insight)
        
        return insights
```

### Data Processing Boundaries

**Extraction and Processing Pipeline**:
```python
class DataProcessingPipeline:
    """
    Manages the flow of data from history to knowledge without duplication
    """
    
    def __init__(self):
        self.entity_extractor = EntityExtractor()
        self.relationship_detector = RelationshipDetector()
        self.fact_generator = FactGenerator()
    
    async def process_conversation_for_knowledge(self, conversation_id):
        """
        Extract knowledge from conversation without storing raw conversation in Graphiti
        """
        # Get conversation from history system (read-only)
        conversation = await self.history.get_conversation(conversation_id)
        
        # Extract entities and relationships
        entities = await self.entity_extractor.extract(conversation.content)
        relationships = await self.relationship_detector.detect(entities, conversation.content)
        facts = await self.fact_generator.generate(entities, relationships, conversation.timestamp)
        
        # Store ONLY processed knowledge in Graphiti
        knowledge_package = {
            'entities': entities,
            'relationships': relationships,
            'facts': facts,
            'source_reference': {
                'type': 'conversation',
                'id': conversation_id,
                'timestamp': conversation.timestamp
            },
            'raw_content': None  # Explicitly exclude raw content
        }
        
        await self.graphiti.store_knowledge_package(knowledge_package)
        
        # Return processing summary (no raw data)
        return {
            'entities_extracted': len(entities),
            'relationships_found': len(relationships),
            'facts_generated': len(facts),
            'source_conversation': conversation_id
        }
```

## 🔒 Data Integrity and Validation

### Boundary Violation Detection

**Automated Boundary Enforcement**:
```python
class DataBoundaryValidator:
    """
    Validates and enforces data separation boundaries
    """
    
    def __init__(self):
        self.violation_detector = BoundaryViolationDetector()
        self.audit_logger = AuditLogger()
    
    def validate_data_operation(self, operation_type, data_type, target_system, data_content):
        """
        Validate that data operations respect system boundaries
        """
        violations = []
        
        # Check for raw conversation data in Graphiti
        if target_system == 'graphiti' and self._contains_raw_conversation(data_content):
            violations.append({
                'type': 'raw_conversation_in_graphiti',
                'severity': 'critical',
                'message': 'Raw conversation data cannot be stored in Graphiti system'
            })
        
        # Check for processed entities in history
        if target_system == 'history' and self._contains_processed_entities(data_content):
            violations.append({
                'type': 'processed_entities_in_history',
                'severity': 'warning',
                'message': 'Processed entities should be stored in Graphiti system'
            })
        
        # Check for data duplication
        duplication_check = self._check_for_duplication(data_content)
        if duplication_check['duplicated']:
            violations.append({
                'type': 'data_duplication',
                'severity': 'critical',
                'message': f"Data already exists in {duplication_check['existing_system']}"
            })
        
        # Log violations
        if violations:
            self.audit_logger.log_boundary_violations(violations)
            
        return violations
    
    def _contains_raw_conversation(self, data_content):
        """
        Detect if data contains raw conversation content
        """
        indicators = ['user_message', 'agent_response', 'conversation_thread', 'chat_history']
        return any(indicator in str(data_content).lower() for indicator in indicators)
```

### Data Consistency Monitoring

**Consistency Verification**:
```python
class DataConsistencyMonitor:
    """
    Monitors data consistency across systems without accessing raw data
    """
    
    def __init__(self):
        self.consistency_checker = ConsistencyChecker()
        self.metrics_collector = MetricsCollector()
    
    async def verify_system_consistency(self):
        """
        Verify that data separation is maintained
        """
        consistency_report = {
            'boundary_violations': 0,
            'data_duplications': 0,
            'orphaned_references': 0,
            'integrity_score': 0.0
        }
        
        # Check for boundary violations
        violations = await self._check_boundary_violations()
        consistency_report['boundary_violations'] = len(violations)
        
        # Check for data duplications
        duplications = await self._check_data_duplications()
        consistency_report['data_duplications'] = len(duplications)
        
        # Check for orphaned references
        orphaned = await self._check_orphaned_references()
        consistency_report['orphaned_references'] = len(orphaned)
        
        # Calculate integrity score
        total_issues = sum([
            consistency_report['boundary_violations'],
            consistency_report['data_duplications'],
            consistency_report['orphaned_references']
        ])
        
        consistency_report['integrity_score'] = max(0.0, 1.0 - (total_issues / 1000))
        
        return consistency_report
```

## 📋 Migration and Compatibility

### Legacy Data Migration

**Safe Migration Strategy**:
```python
class LegacyDataMigrator:
    """
    Safely migrate existing data while establishing proper boundaries
    """
    
    def __init__(self):
        self.data_classifier = DataClassifier()
        self.boundary_enforcer = BoundaryEnforcer()
    
    async def migrate_existing_data(self):
        """
        Migrate existing data to proper systems while maintaining separation
        """
        migration_plan = {
            'history_migrations': [],
            'graphiti_migrations': [],
            'processing_required': []
        }
        
        # Analyze existing data
        existing_data = await self._analyze_existing_data()
        
        for data_item in existing_data:
            classification = self.data_classifier.classify(data_item)
            
            if classification['target_system'] == 'history':
                migration_plan['history_migrations'].append(data_item)
            elif classification['target_system'] == 'graphiti':
                if classification['requires_processing']:
                    migration_plan['processing_required'].append(data_item)
                else:
                    migration_plan['graphiti_migrations'].append(data_item)
        
        # Execute migration with boundary enforcement
        await self._execute_migration_plan(migration_plan)
        
        return migration_plan
```

---

*This data separation specification ensures strict boundaries between history and knowledge systems while enabling powerful cross-system integration capabilities.*
