# Installation Guide - Agent Zero v2.0

Welcome to Agent Zero v2.0 with enhanced memory systems! This guide will help you install and configure Agent Zero with support for both Graphiti temporal knowledge graphs and traditional FAISS backends.

## Quick Installation (Recommended)

The fastest way to get started with Agent Zero v2.0 is using Docker:

### Prerequisites
- Docker Desktop installed on your system
- At least 4GB of available RAM
- 10GB of free disk space

### Step 1: Install Docker Desktop

1. **Download Docker Desktop** from [docker.com](https://www.docker.com/products/docker-desktop/)
2. **Install** with default settings
3. **Launch** Docker Desktop and create an account if needed

> **macOS Users**: Enable "Allow the default Docker socket to be used" in Docker Desktop Settings → Advanced

### Step 2: Run Agent Zero v2.0

```bash
# Pull the latest Agent Zero image
docker pull frdel/agent-zero-run:latest

# Create a data directory for persistence
mkdir ~/agent-zero-data

# Run the container with memory backend support
docker run -d \
  --name agent-zero-v2 \
  -p 8080:80 \
  -v ~/agent-zero-data:/a0 \
  -e MEMORY_BACKEND=auto \
  frdel/agent-zero-run:latest
```

### Step 3: Access the Web Interface

1. Open your browser to `http://localhost:8080`
2. Complete the initial setup wizard
3. Configure your preferred memory backend (see [Memory Configuration](../memory-systems/configuration.md))

## Memory Backend Options

Agent Zero v2.0 supports multiple memory backends:

### FAISS Backend (Default)
- **Best for**: Getting started quickly, smaller datasets
- **Requirements**: No additional setup required
- **Storage**: Local vector database files

### Graphiti Backend (Advanced)
- **Best for**: Complex relationships, temporal queries, large knowledge bases
- **Requirements**: Neo4j database, OpenAI API key for embeddings
- **Storage**: Neo4j graph database

### Auto Backend Selection
- **Best for**: Automatic fallback based on available resources
- **Behavior**: Attempts Graphiti first, falls back to FAISS if unavailable

## Graphiti Backend Setup

To use the advanced Graphiti temporal knowledge graph backend:

### Step 1: Install Neo4j

**Option A: Docker (Recommended)**
```bash
docker run -d \
  --name neo4j-agent-zero \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/your-password \
  -e NEO4J_PLUGINS='["apoc"]' \
  neo4j:latest
```

**Option B: Neo4j Desktop**
1. Download from [neo4j.com](https://neo4j.com/download/)
2. Create a new database
3. Install APOC plugin
4. Set authentication credentials

### Step 2: Configure Environment Variables

Create or update your `.env` file:

```env
# Memory Backend Configuration
MEMORY_BACKEND=graphiti

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your-password

# Graphiti Configuration
GRAPHITI_GROUP_ID=agent-zero-default
OPENAI_API_KEY=your-openai-api-key

# Optional: Bulk processing settings
GRAPHITI_BULK_BATCH_SIZE=100
GRAPHITI_BULK_TIMEOUT=300
```

### Step 3: Restart Agent Zero

```bash
docker restart agent-zero-v2
```

## Development Installation

For developers who want to modify Agent Zero:

### Prerequisites
- Python 3.11 or 3.12
- Git
- Conda or virtualenv

### Step 1: Clone Repository

```bash
git clone https://github.com/frdel/agent-zero.git
cd agent-zero
```

### Step 2: Create Environment

```bash
# Using conda
conda create -n agent-zero python=3.12 -y
conda activate agent-zero

# Using virtualenv
python -m venv agent-zero-env
source agent-zero-env/bin/activate  # Linux/Mac
# or
agent-zero-env\Scripts\activate  # Windows
```

### Step 3: Install Dependencies

```bash
pip install -r requirements.txt
```

### Step 4: Configure Environment

```bash
cp example.env .env
# Edit .env with your API keys and settings
```

### Step 5: Run Agent Zero

```bash
# Web UI
python run_ui.py

# CLI interface
python run_cli.py
```

## Configuration

After installation, configure Agent Zero through the web interface:

1. **API Keys**: Set up your LLM provider API keys
2. **Memory Backend**: Choose between FAISS and Graphiti
3. **Model Selection**: Configure chat, utility, and embedding models
4. **Knowledge Import**: Import your existing knowledge base

See the [Configuration Guide](configuration.md) for detailed setup instructions.

## Verification

Test your installation:

1. **Basic Functionality**: Ask Agent Zero a simple question
2. **Memory System**: Check that memories are being saved and retrieved
3. **Tool Usage**: Verify that tools and instruments work correctly
4. **Knowledge Base**: Test knowledge import and retrieval

## Troubleshooting

Common installation issues:

- **Docker Issues**: Ensure Docker Desktop is running and you have sufficient resources
- **Memory Backend Errors**: Check Neo4j connection and API keys
- **Port Conflicts**: Use different ports if 8080 is already in use
- **Permission Issues**: Ensure proper file permissions for data directory

For detailed troubleshooting, see the [Troubleshooting Guide](../developer-guide/troubleshooting.md).

## Next Steps

- [Quick Start Guide](quickstart.md) - Get up and running quickly
- [Configuration Guide](configuration.md) - Detailed configuration options
- [Memory Systems Overview](../memory-systems/overview.md) - Understanding memory backends
- [Architecture Overview](../core-concepts/architecture.md) - System architecture details

## Migration from v1.x

If you're upgrading from Agent Zero v1.x:

1. **Backup Data**: Save your existing memory, knowledge, and settings
2. **Install v2.0**: Follow the installation guide above
3. **Migrate Data**: Copy your data to the new data directory
4. **Update Configuration**: Review and update your settings for v2.0

The legacy documentation is available at [../legacy/README.md](../legacy/README.md) for reference.
