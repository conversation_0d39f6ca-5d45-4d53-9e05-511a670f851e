# tests/__init__.py
"""
This file makes the 'tests' directory a Python package.

It can also be used for package-level test configurations or imports
if needed, though conftest.py is generally preferred for fixtures
and pytest-specific hooks.

For now, it can remain empty or include a simple docstring.
"""

# You could, for example, import common testing utilities here
# from .common_test_utils import SomeUtilClass

# Or define package-level variables
# TEST_PACKAGE_VERSION = "0.1.0"

print("tests package initialized.")
