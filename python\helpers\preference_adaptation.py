from typing import Dict, Any, List, Optional
from datetime import datetime, timezone # Added timezone
import asyncio # For async method signatures

# Attempt to import UserPreferenceManager, PreferenceType, BehavioralPatternAnalyzer
# Provide fallbacks for independent development/testing
try:
    from .user_preferences import UserPreferenceManager, PreferenceType
    from .behavioral_analyzer import BehavioralPatternAnalyzer
except ImportError:
    print("WARN: preference_adaptation.py: Could not import dependencies. Using placeholders.")
    from enum import Enum
    class PreferenceType(Enum): DOMAIN_WEIGHT = "domain_weight"; SEARCH_PREFERENCE = "search_preference"; CLASSIFICATION_THRESHOLD = "classification_threshold"
    class UserPreferenceManager:
        def __init__(self, user_id): self.user_id = user_id
        async def get_preference(self, key, default=None): return default
        async def set_preference(self, key, value, pref_type, user_explicit=False): return True
    class BehavioralPatternAnalyzer:
        def __init__(self, user_id): self.user_id = user_id
        async def analyze_patterns(self): return []
        async def get_pattern_recommendations(self): return []


class PreferenceAdaptationEngine:
    """
    Adapts user preferences based on behavioral pattern analysis.
    It uses BehavioralPatternAnalyzer to understand user behavior and
    UserPreferenceManager to apply changes to the user's preferences.
    """

    def __init__(self, preference_manager: UserPreferenceManager, behavioral_analyzer: Optional[BehavioralPatternAnalyzer] = None): # Allow injecting analyzer
        self.preference_manager = preference_manager
        # If no analyzer is injected, create one. This is useful for default behavior
        # but allows tests to inject a mock analyzer.
        self.behavioral_analyzer = behavioral_analyzer or BehavioralPatternAnalyzer(preference_manager.user_id)
        self.adaptation_history: List[Dict[str, Any]] = []

    async def analyze_and_adapt(self) -> Dict[str, Any]:
        """
        Analyzes behavioral patterns and applies adaptations to user preferences.
        This is the main entry point for the adaptation process.
        """
        # Get behavioral patterns and recommendations from the analyzer
        # The spec implies analyze_patterns might be called by get_pattern_recommendations if needed,
        # but explicit call here ensures patterns are fresh for the session report.
        patterns = await self.behavioral_analyzer.analyze_patterns()
        recommendations = await self.behavioral_analyzer.get_pattern_recommendations()

        adaptations_applied_details = []
        for recommendation in recommendations:
            adaptation_result = await self._apply_recommendation(recommendation)
            if adaptation_result:
                adaptations_applied_details.append(adaptation_result)

        adaptation_session_summary = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'user_id': self.preference_manager.user_id,
            'patterns_analyzed_count': len(patterns),
            'recommendations_generated_count': len(recommendations),
            'adaptations_applied_count': len(adaptations_applied_details),
            'adaptations': adaptations_applied_details
        }

        self.adaptation_history.append(adaptation_session_summary)
        # Keep adaptation history manageable, e.g., last 100 sessions
        if len(self.adaptation_history) > 100:
            self.adaptation_history = self.adaptation_history[-100:]

        return adaptation_session_summary

    async def _apply_recommendation(self, recommendation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Applies a specific recommendation to change a user preference.
        Routes to specific adaptation methods based on recommendation type.
        """
        rec_type = recommendation.get('type')
        adapted_detail = None

        if rec_type == 'increase_domain_weight':
            adapted_detail = await self._adapt_domain_weight(recommendation)
        elif rec_type == 'adjust_search_limit':
            adapted_detail = await self._adapt_search_limit(recommendation)
        elif rec_type == 'review_classification_thresholds': # As per spec for BehavioralAnalyzer
            # This might translate to adapting 'classification_confidence_threshold'
            adapted_detail = await self._adapt_classification_threshold(recommendation)
        else:
            print(f"PreferenceAdaptationEngine: Unknown recommendation type '{rec_type}'. Skipping.")

        return adapted_detail

    async def _adapt_domain_weight(self, recommendation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Adapts a domain weight preference."""
        domain = recommendation.get('domain')
        weight_increase = recommendation.get('recommended_weight_increase', 0.05) # Default small increase
        confidence_of_recommendation = recommendation.get('confidence', 0.0)

        if not domain: return None

        preference_key = f'domain_weight_{domain}'
        # Provide a sensible default if the preference doesn't exist yet (e.g., 0.5 for a new domain)
        current_weight = await self.preference_manager.get_preference(preference_key, default_value=0.5)

        new_weight = min(1.0, current_weight + weight_increase) # Cap at 1.0

        # Only apply if there's a meaningful change
        if abs(new_weight - current_weight) < 0.01: # Threshold for change
            return None

        success = await self.preference_manager.set_preference(
            preference_key, new_weight, PreferenceType.DOMAIN_WEIGHT, user_explicit=False
        )

        if success:
            return {
                'type': 'domain_weight_adaptation', 'preference_key': preference_key,
                'domain': domain, 'old_value': current_weight, 'new_value': new_weight,
                'recommendation_confidence': confidence_of_recommendation,
                'reason': recommendation.get('reason', f"Behavioral pattern indicated preference for {domain}")
            }
        return None

    async def _adapt_search_limit(self, recommendation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Adapts the search result limit preference."""
        recommended_limit = recommendation.get('recommended_limit')
        confidence_of_recommendation = recommendation.get('confidence', 0.0)

        if recommended_limit is None or recommended_limit <= 0: return None

        preference_key = 'search_result_limit'
        current_limit = await self.preference_manager.get_preference(preference_key, default_value=10) # Default from UPM

        # Ensure new limit is an int and has a minimum
        new_limit = max(1, int(recommended_limit))

        if new_limit == current_limit:
            return None

        success = await self.preference_manager.set_preference(
            preference_key, new_limit, PreferenceType.SEARCH_PREFERENCE, user_explicit=False
        )

        if success:
            return {
                'type': 'search_limit_adaptation', 'preference_key': preference_key,
                'old_value': current_limit, 'new_value': new_limit,
                'recommendation_confidence': confidence_of_recommendation,
                'reason': recommendation.get('reason', f"Behavioral pattern suggested optimal search limit: {new_limit}")
            }
        return None

    async def _adapt_classification_threshold(self, recommendation: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Adapts a classification confidence threshold."""
        # Recommendation from BehavioralAnalyzer for 'review_classification_thresholds'
        # might imply lowering 'classification_confidence_threshold'
        action = recommendation.get('recommended_action')
        confidence_of_recommendation = recommendation.get('confidence', 0.0)

        if action != 'lower_confidence_threshold': # Only handle this specific action
            return None

        preference_key = 'classification_confidence_threshold'
        # Default from UserPreferenceManager's _initialize_default_preferences
        current_threshold = await self.preference_manager.get_preference(preference_key, default_value=0.7)

        # Lower threshold by a small amount, e.g., 0.05, ensuring it doesn't go too low
        new_threshold = max(0.4, current_threshold - 0.05) # Min threshold e.g. 0.4

        if abs(new_threshold - current_threshold) < 0.01:
            return None

        success = await self.preference_manager.set_preference(
            preference_key, new_threshold, PreferenceType.CLASSIFICATION_THRESHOLD, user_explicit=False
        )

        if success:
            return {
                'type': 'classification_threshold_adaptation', 'preference_key': preference_key,
                'old_value': current_threshold, 'new_value': new_threshold,
                'recommendation_confidence': confidence_of_recommendation,
                'reason': recommendation.get('reason', f"High negative feedback rate triggered threshold review.")
            }
        return None

if __name__ == "__main__":
    async def test_preference_adaptation_engine_main():
        print("--- Testing PreferenceAdaptationEngine ---")

        # Mock UserPreferenceManager
        class MockUserPreferenceManager(UserPreferenceManager):
            def __init__(self, user_id):
                super().__init__(user_id)
                print(f"MockUPM initialized for {user_id}. Defaults: { {k: v.value for k,v in self.preferences.items()} }")
            async def get_preference(self, key, default=None):
                val = self.preferences.get(key)
                return val.value if val else default
            async def set_preference(self, key, value, pref_type, user_explicit=False):
                print(f"MockUPM: Setting '{key}' to '{value}' (Explicit: {user_explicit})")
                # Simulate actual update for testing
                if key in self.preferences:
                    self.preferences[key].value = value
                    self.preferences[key].user_explicit = user_explicit
                    self.preferences[key].last_updated = datetime.now(timezone.utc)
                else: # Simplified: assumes type matches existing or is fine
                    from unittest.mock import MagicMock # Import locally for the mock's main
                    self.preferences[key] = MagicMock(value=value, user_explicit=user_explicit, preference_type=pref_type)
                return True

        # Mock BehavioralPatternAnalyzer
        class MockBehavioralPatternAnalyzer(BehavioralPatternAnalyzer):
            async def analyze_patterns(self):
                # Return some mock patterns for testing
                from unittest.mock import MagicMock # Import locally for the mock's main
                return [MagicMock(pattern_type='domain_preference', pattern_data={'domain':'tech'}, confidence=0.8)]
            async def get_pattern_recommendations(self):
                # Return mock recommendations that _apply_recommendation can process
                return [
                    {'type': 'increase_domain_weight', 'domain': 'tech', 'recommended_weight_increase': 0.15, 'confidence': 0.8, 'reason': 'Strong tech preference'},
                    {'type': 'adjust_search_limit', 'recommended_limit': 7, 'confidence': 0.7, 'reason': 'User uses few results'},
                    {'type': 'review_classification_thresholds', 'recommended_action': 'lower_confidence_threshold', 'confidence': 0.6, 'reason': 'High error rate'}
                ]

        mock_upm = MockUserPreferenceManager("adapt_user_test")
        # Initialize some preferences that will be adapted
        await mock_upm.set_preference('domain_weight_tech', 0.5, PreferenceType.DOMAIN_WEIGHT, user_explicit=False)
        await mock_upm.set_preference('search_result_limit', 10, PreferenceType.SEARCH_PREFERENCE, user_explicit=False)
        await mock_upm.set_preference('classification_confidence_threshold', 0.7, PreferenceType.CLASSIFICATION_THRESHOLD, user_explicit=False)


        mock_bpa = MockBehavioralPatternAnalyzer("adapt_user_test")

        adaptation_engine = PreferenceAdaptationEngine(mock_upm, mock_bpa)

        print("\nInitial Preferences (before adaptation):")
        print(f"  domain_weight_tech: {await mock_upm.get_preference('domain_weight_tech')}")
        print(f"  search_result_limit: {await mock_upm.get_preference('search_result_limit')}")
        print(f"  classification_confidence_threshold: {await mock_upm.get_preference('classification_confidence_threshold')}")

        adaptation_session = await adaptation_engine.analyze_and_adapt()

        print(f"\nAdaptation Session Summary:")
        print(f"  Timestamp: {adaptation_session['timestamp']}")
        print(f"  Patterns Analyzed: {adaptation_session['patterns_analyzed_count']}")
        print(f"  Recommendations Generated: {adaptation_session['recommendations_generated_count']}")
        print(f"  Adaptations Applied: {adaptation_session['adaptations_applied_count']}")

        print("\nAdaptations Applied Details:")
        for adapt_detail in adaptation_session['adaptations']:
            print(f"  - Type: {adapt_detail['type']}, Key: {adapt_detail['preference_key']}, Old: {adapt_detail['old_value']}, New: {adapt_detail['new_value']}")

        print("\nFinal Preferences (after adaptation):")
        final_tech_weight = await mock_upm.get_preference('domain_weight_tech')
        final_search_limit = await mock_upm.get_preference('search_result_limit')
        final_class_thresh = await mock_upm.get_preference('classification_confidence_threshold')

        print(f"  domain_weight_tech: {final_tech_weight}")
        print(f"  search_result_limit: {final_search_limit}")
        print(f"  classification_confidence_threshold: {final_class_thresh}")

        assert adaptation_session['adaptations_applied_count'] == 3 # Based on mock recommendations
        assert final_tech_weight == 0.65 # 0.5 + 0.15
        assert final_search_limit == 7
        assert abs(final_class_thresh - 0.65) < 0.001 # 0.7 - 0.05

        print("\n--- PreferenceAdaptationEngine Test Completed ---")

    asyncio.run(test_preference_adaptation_engine_main())
