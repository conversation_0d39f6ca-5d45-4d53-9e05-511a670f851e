# Phase 4.1: User Preference System
## Adaptive User Preference Framework with Behavioral Learning

### Overview
This step implements the core user preference management system that learns from user behavior and adapts classification decisions based on individual preferences and usage patterns.

### Time Estimate: 2-3 hours

---

## Step 4.1.1: Create User Preference Data Structures

**File:** `python/helpers/user_preferences.py`
**Action:** Create new file

```python
"""
Adaptive User Preference Framework with Behavioral Learning
"""

from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import asyncio

from .classification_config import classification_config

class PreferenceType(Enum):
    DOMAIN_WEIGHT = "domain_weight"
    PRIVACY_SENSITIVITY = "privacy_sensitivity"
    CLASSIFICATION_THRESHOLD = "classification_threshold"
    MEMORY_RETENTION = "memory_retention"
    SEARCH_PREFERENCE = "search_preference"

class LearningSignal(Enum):
    POSITIVE_FEEDBACK = "positive"
    NEGATIVE_FEEDBACK = "negative"
    IMPLICIT_USAGE = "implicit"
    EXPLICIT_SETTING = "explicit"

@dataclass
class PreferenceItem:
    """Individual preference item with learning history"""
    preference_type: PreferenceType
    key: str
    value: Any
    confidence: float
    last_updated: datetime
    learning_history: List[Dict[str, Any]] = field(default_factory=list)
    user_explicit: bool = False

@dataclass
class UserFeedback:
    """User feedback for preference learning"""
    action: str
    context: Dict[str, Any]
    feedback_type: LearningSignal
    timestamp: datetime
    value: Any
```

**Validation:**
```python
# Test preference data structures
from python.helpers.user_preferences import PreferenceType, PreferenceItem, LearningSignal
from datetime import datetime

# Create test preference
test_pref = PreferenceItem(
    preference_type=PreferenceType.DOMAIN_WEIGHT,
    key="programming",
    value=0.8,
    confidence=0.7,
    last_updated=datetime.now()
)

print(f"Created preference: {test_pref.key} = {test_pref.value}")
```

---

## Step 4.1.2: Implement User Preference Manager

**File:** `python/helpers/user_preferences.py`
**Action:** Continue adding to existing file

```python
class UserPreferenceManager:
    """
    Manages user preferences with adaptive learning capabilities
    """

    def __init__(self, user_id: str):
        self.user_id = user_id
        self.preferences: Dict[str, PreferenceItem] = {}
        self.feedback_history: List[UserFeedback] = []
        self.learning_rate = classification_config.preference_learning_rate
        self.adaptation_enabled = classification_config.enable_adaptive_learning

        # Initialize default preferences
        self._initialize_default_preferences()

    def _initialize_default_preferences(self):
        """Initialize default user preferences"""

        default_prefs = {
            # Domain weights
            'domain_weight_programming': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'programming', 1.0, 0.5, datetime.now()
            ),
            'domain_weight_documentation': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'documentation', 0.8, 0.5, datetime.now()
            ),
            'domain_weight_conversations': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'conversations', 0.6, 0.5, datetime.now()
            ),
            'domain_weight_general': PreferenceItem(
                PreferenceType.DOMAIN_WEIGHT, 'general', 0.4, 0.5, datetime.now()
            ),

            # Privacy sensitivity
            'privacy_sensitivity_level': PreferenceItem(
                PreferenceType.PRIVACY_SENSITIVITY, 'level', 'medium', 0.7, datetime.now()
            ),
            'privacy_auto_isolation': PreferenceItem(
                PreferenceType.PRIVACY_SENSITIVITY, 'auto_isolation', True, 0.8, datetime.now()
            ),

            # Classification thresholds
            'classification_confidence_threshold': PreferenceItem(
                PreferenceType.CLASSIFICATION_THRESHOLD, 'confidence', 0.7, 0.6, datetime.now()
            ),
            'classification_manual_review_threshold': PreferenceItem(
                PreferenceType.CLASSIFICATION_THRESHOLD, 'manual_review', 0.6, 0.6, datetime.now()
            ),

            # Memory retention preferences
            'memory_working_retention_days': PreferenceItem(
                PreferenceType.MEMORY_RETENTION, 'working_days', 7, 0.8, datetime.now()
            ),
            'memory_importance_threshold': PreferenceItem(
                PreferenceType.MEMORY_RETENTION, 'importance_threshold', 0.5, 0.7, datetime.now()
            ),

            # Search preferences
            'search_strategy_preference': PreferenceItem(
                PreferenceType.SEARCH_PREFERENCE, 'strategy', 'hierarchical', 0.6, datetime.now()
            ),
            'search_result_limit': PreferenceItem(
                PreferenceType.SEARCH_PREFERENCE, 'result_limit', 10, 0.8, datetime.now()
            )
        }

        self.preferences.update(default_prefs)

    async def get_preference(self, preference_key: str, default_value: Any = None) -> Any:
        """Get user preference value"""
        if preference_key in self.preferences:
            return self.preferences[preference_key].value
        return default_value

    async def set_preference(
        self,
        preference_key: str,
        value: Any,
        preference_type: PreferenceType,
        user_explicit: bool = True
    ) -> bool:
        """Set user preference explicitly"""

        try:
            if preference_key in self.preferences:
                # Update existing preference
                pref = self.preferences[preference_key]
                pref.value = value
                pref.last_updated = datetime.now()
                pref.user_explicit = user_explicit
                if user_explicit:
                    pref.confidence = 1.0  # High confidence for explicit settings
            else:
                # Create new preference
                self.preferences[preference_key] = PreferenceItem(
                    preference_type=preference_type,
                    key=preference_key,
                    value=value,
                    confidence=1.0 if user_explicit else 0.5,
                    last_updated=datetime.now(),
                    user_explicit=user_explicit
                )

            # Record the change
            await self._record_preference_change(preference_key, value, user_explicit)

            return True

        except Exception as e:
            print(f"Error setting preference {preference_key}: {e}")
            return False
```

**Validation:**
```python
# Test user preference manager
from python.helpers.user_preferences import UserPreferenceManager, PreferenceType

manager = UserPreferenceManager("test_user")

# Test getting default preference
programming_weight = await manager.get_preference('domain_weight_programming')
print(f"Programming weight: {programming_weight}")

# Test setting preference
success = await manager.set_preference(
    'domain_weight_programming', 
    0.9, 
    PreferenceType.DOMAIN_WEIGHT
)
print(f"Preference set successfully: {success}")
```

---

## Step 4.1.3: Implement Preference Learning Methods

**File:** `python/helpers/user_preferences.py`
**Action:** Continue adding to existing file

```python
    async def record_feedback(
        self,
        action: str,
        context: Dict[str, Any],
        feedback_type: LearningSignal,
        value: Any = None
    ) -> None:
        """Record user feedback for learning"""

        feedback = UserFeedback(
            action=action,
            context=context,
            feedback_type=feedback_type,
            timestamp=datetime.now(),
            value=value
        )

        self.feedback_history.append(feedback)

        # Trigger adaptive learning if enabled
        if self.adaptation_enabled:
            await self._process_feedback(feedback)

        # Keep feedback history manageable
        if len(self.feedback_history) > 1000:
            self.feedback_history = self.feedback_history[-1000:]

    async def _process_feedback(self, feedback: UserFeedback) -> None:
        """Process feedback to update preferences"""

        if feedback.feedback_type == LearningSignal.POSITIVE_FEEDBACK:
            await self._reinforce_preferences(feedback)
        elif feedback.feedback_type == LearningSignal.NEGATIVE_FEEDBACK:
            await self._adjust_preferences(feedback)
        elif feedback.feedback_type == LearningSignal.IMPLICIT_USAGE:
            await self._learn_from_usage(feedback)

    async def _reinforce_preferences(self, feedback: UserFeedback) -> None:
        """Reinforce preferences based on positive feedback"""

        # Extract relevant preferences from context
        context = feedback.context
        
        if 'classification_result' in context:
            result = context['classification_result']
            
            # Reinforce domain weights if classification was successful
            if 'domain' in result:
                domain_key = f"domain_weight_{result['domain']}"
                if domain_key in self.preferences:
                    pref = self.preferences[domain_key]
                    # Increase weight slightly
                    new_value = min(1.0, pref.value + self.learning_rate * 0.1)
                    await self._update_preference_value(domain_key, new_value, 'positive_feedback')

            # Reinforce classification thresholds
            if 'confidence' in result:
                confidence = result['confidence']
                threshold_key = 'classification_confidence_threshold'
                if threshold_key in self.preferences:
                    pref = self.preferences[threshold_key]
                    # Adjust threshold based on successful classification
                    adjustment = (confidence - pref.value) * self.learning_rate * 0.05
                    new_value = max(0.1, min(0.9, pref.value + adjustment))
                    await self._update_preference_value(threshold_key, new_value, 'threshold_adjustment')

    async def _adjust_preferences(self, feedback: UserFeedback) -> None:
        """Adjust preferences based on negative feedback"""

        context = feedback.context
        
        if 'classification_result' in context:
            result = context['classification_result']
            
            # Reduce domain weights if classification was poor
            if 'domain' in result:
                domain_key = f"domain_weight_{result['domain']}"
                if domain_key in self.preferences:
                    pref = self.preferences[domain_key]
                    # Decrease weight slightly
                    new_value = max(0.1, pref.value - self.learning_rate * 0.1)
                    await self._update_preference_value(domain_key, new_value, 'negative_feedback')

    async def _learn_from_usage(self, feedback: UserFeedback) -> None:
        """Learn from implicit usage patterns"""

        context = feedback.context
        action = feedback.action

        if action == 'search_query':
            # Learn search preferences
            if 'result_count' in context:
                result_count = context['result_count']
                limit_key = 'search_result_limit'
                
                if limit_key in self.preferences:
                    pref = self.preferences[limit_key]
                    # Adjust based on usage patterns
                    if result_count < pref.value * 0.5:
                        # User typically uses fewer results
                        new_value = max(5, int(pref.value * 0.9))
                        await self._update_preference_value(limit_key, new_value, 'usage_pattern')

        elif action == 'memory_access':
            # Learn memory retention preferences
            if 'tier' in context and 'access_frequency' in context:
                tier = context['tier']
                frequency = context['access_frequency']
                
                if tier == 'working' and frequency > 5:
                    # User frequently accesses working memory
                    retention_key = 'memory_working_retention_days'
                    if retention_key in self.preferences:
                        pref = self.preferences[retention_key]
                        new_value = min(14, pref.value + 1)  # Increase retention
                        await self._update_preference_value(retention_key, new_value, 'usage_pattern')

    async def _update_preference_value(
        self, 
        preference_key: str, 
        new_value: Any, 
        reason: str
    ) -> None:
        """Update preference value with learning history"""

        if preference_key in self.preferences:
            pref = self.preferences[preference_key]
            old_value = pref.value
            
            # Update value
            pref.value = new_value
            pref.last_updated = datetime.now()
            
            # Increase confidence for learned preferences
            if not pref.user_explicit:
                pref.confidence = min(0.9, pref.confidence + 0.05)
            
            # Record in learning history
            pref.learning_history.append({
                'timestamp': datetime.now(),
                'old_value': old_value,
                'new_value': new_value,
                'reason': reason,
                'confidence': pref.confidence
            })
            
            # Keep history manageable
            if len(pref.learning_history) > 50:
                pref.learning_history = pref.learning_history[-50:]

    async def _record_preference_change(
        self, 
        preference_key: str, 
        value: Any, 
        user_explicit: bool
    ) -> None:
        """Record preference change in history"""

        if preference_key in self.preferences:
            pref = self.preferences[preference_key]
            pref.learning_history.append({
                'timestamp': datetime.now(),
                'old_value': pref.learning_history[-1]['new_value'] if pref.learning_history else None,
                'new_value': value,
                'reason': 'explicit_setting' if user_explicit else 'system_update',
                'confidence': pref.confidence
            })
```

**Validation:**
```python
# Test preference learning
from python.helpers.user_preferences import UserPreferenceManager, LearningSignal

manager = UserPreferenceManager("test_user")

# Test feedback recording
await manager.record_feedback(
    action="classification",
    context={
        'classification_result': {
            'domain': 'programming',
            'confidence': 0.85
        }
    },
    feedback_type=LearningSignal.POSITIVE_FEEDBACK
)

print("Feedback recorded and processed")

# Check if domain weight was adjusted
new_weight = await manager.get_preference('domain_weight_programming')
print(f"Updated programming weight: {new_weight}")
```

---

## Next Steps

✅ **Step 4.1 Complete - Validation Checklist:**
- [ ] User preference data structures are defined
- [ ] UserPreferenceManager can be instantiated with default preferences
- [ ] Preferences can be get and set programmatically
- [ ] Feedback recording and processing works
- [ ] Adaptive learning adjusts preferences based on feedback
- [ ] Learning history is maintained for preferences

**Next:** Proceed to Step 4.2 - Adaptive Learning Framework
